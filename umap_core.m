function Y = umap_core(X, n_neighbors, min_dist, n_epochs)
% UMAP core algorithm (simplified version)
[N, dim] = size(X);
Y = randn(N, 2)*0.1; % Initialize in 2D

% 1. Compute k-nearest neighbors
[~, knn_indices] = pdist2(X, X, 'euclidean', 'Smallest', n_neighbors+1);
knn_indices = knn_indices(2:end,:)'; % Remove self

% 2. Compute fuzzy simplicial set
[rows, cols, vals] = compute_fuzzy_simplicial(X, knn_indices, n_neighbors);

% 3. Optimize embedding
Y = optimize_layout(Y, rows, cols, vals, min_dist, n_epochs);
end

function [rows, cols, vals] = compute_fuzzy_simplicial(X, knn_indices, n_neighbors)
[N, ~] = size(X);
rows = repelem((1:N)', n_neighbors);
cols = knn_indices(:);
dists = sqrt(sum((X(rows,:) - X(cols,:)).^2, 2));

% Corrected reshaping and minimum calculation
reshaped_dists = reshape(dists, [n_neighbors, N]);  % Reshape to [k x N] matrix
rhos = min(reshaped_dists, [], 1)';  % Column-wise minimum

% Find sigmas (ensure this function is implemented)
sigmas = find_sigmas(dists, rhos, n_neighbors);

vals = exp(-(dists - rhos(rows))./sigmas(rows));
vals = vals./sum(vals);
end
function Y = optimize_layout(Y, rows, cols, vals, min_dist, n_epochs)
[N, d] = size(Y);
n_edges = length(rows);
a = 1.577/max(min_dist, 0.001);
b = 0.895;
eta = 1.0;

% Ensure rows is a column vector for GPU operations
if ~iscolumn(rows)
    rows = rows(:);
end

for epoch = 1:n_epochs
    % Positive forces (attraction)
    pos_dy = Y(rows,:) - Y(cols,:);
    pos_dist = sum(pos_dy.^2, 2) + 1e-7;
    pos_vals = 1./(1 + a*pos_dist.^b);
    pos_scaling = (vals - pos_vals) .* (-2*a*b*pos_dist.^(b-1))./(1 + a*pos_dist.^b);
    pos_grad = pos_scaling .* pos_dy;
    
    % Negative forces (repulsion)
    neg_samples = randi(N, n_edges, 1, 'like', rows);
    neg_dy = Y(rows,:) - Y(neg_samples,:);
    neg_dist = sum(neg_dy.^2, 2) + 1e-7;
    neg_scaling = 2*b./(0.1 + neg_dist)./(1 + a*neg_dist.^b);
    neg_grad = neg_scaling .* neg_dy;
    
    % GPU-compatible gradient accumulation
    delta_grad = pos_grad - neg_grad;
    
    % Split accumulation for each dimension
    grad_x = accumarray(rows, delta_grad(:,1), [N, 1]);
    grad_y = accumarray(rows, delta_grad(:,2), [N, 1]);
    grad = [grad_x, grad_y];
    
    % Update positions
    Y = Y - eta * grad;
    eta = eta * 0.99;
end
end

function sigmas = find_sigmas(dists, rhos, n_neighbors)
N = length(rhos);
sigmas = zeros(N, 1);

for i = 1:N
    % Extract distances for current point's neighbors
    start_idx = (i-1)*n_neighbors + 1;
    end_idx = i*n_neighbors;
    point_dists = dists(start_idx:end_idx);
    
    % Define search function with bounds
    target = log2(n_neighbors);
    f = @(sigma) sum(exp(-max(point_dists - rhos(i), 0)./sigma) - target);
    
    % Use bounded search with fallback
    try
        sigmas(i) = fzero(f, [1e-6, 100]); % Constrained search range
    catch
        sigmas(i) = 0.5; % Fallback for problematic cases
    end
end
end
