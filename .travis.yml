language: python

cache:
  apt: true
  # We use three different cache directory
  # to work around a Travis bug with multi-platform cache
  directories:
  - $HOME/.cache/pip
  - $HOME/download
env:
  global:
    # Directory where tests are run from
    - TEST_DIR=/tmp/test_dir/
    - MODULE=umap

matrix:
  include:
    - python: 3.6
      os: linux
    - env: DISTRIB="conda" PYTHON_VERSION="3.7" NUMPY_VERSION="1.17" SCIPY_VERSION="1.3.1"
      os: linux
    - env: DISTRIB="conda" PYTHON_VERSION="3.8" NUMPY_VERSION="1.20.0" SCIPY_VERSION="1.6.0"
      os: linux
    - env: DISTRIB="conda" PYTHON_VERSION="3.8" COVERAGE="true" NUMPY_VERSION="1.20.0" SCIPY_VERSION="1.6.0"
      os: linux
#    - env: DISTRIB="conda" PYTHON_VERSION="3.7" NUMBA_VERSION="0.51.2"
#      os: osx
#      language: generic
#    - env: DISTRIB="conda" PYTHON_VERSION="3.8" NUMBA_VERSION="0.51.2"
#      os: osx
#      language: generic

install: source ci_scripts/install.sh
script: travis_wait 90 bash ci_scripts/test.sh
after_success: source ci_scripts/success.sh
