build: "off"

environment:
  matrix:
    - PYTHON_VERSION: "3.7"
      MINICONDA: C:\Miniconda3-x64
    - PYTHON_VERSION: "3.8"
      MINICONDA: C:\Miniconda3-x64

init:
  - "ECHO %PYTHON_VERSION% %MINICONDA%"

install:
  - "set PATH=%MINICONDA%;%MINICONDA%\\Scripts;%PATH%"
  - conda config --set always_yes yes --set changeps1 no
  - conda update -q conda
  - conda info -a
  - "conda create -q -n test-environment python=%PYTHON_VERSION% numpy scipy scikit-learn numba pandas bokeh holoviews datashader scikit-image pytest"
  - activate test-environment
  - pip install "tensorflow>=2.1"
  - pip install pytest-benchmark
  - pip install -e .

test_script:
  - pytest --show-capture=no -v --disable-warnings
  