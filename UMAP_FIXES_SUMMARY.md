# UMAP MATLAB Implementation - Key Fixes

## Overview
This document summarizes the critical fixes applied to the MATLAB UMAP implementation to align it with the original Python algorithm by <PERSON><PERSON><PERSON>nnes, <PERSON><PERSON>, and Melville.

## Major Issues Fixed

### 1. **Sigma Computation in `find_sigmas` Function**
**Problem**: The objective function was incorrectly subtracting the target from each exponential term.
```matlab
% WRONG:
f = @(sigma) sum(exp(-max(point_dists - rhos(i), 0)./sigma) - target);

% FIXED:
f = @(sigma) sum(exp(-max(point_dists - rhos(i), 0)./sigma)) - target;
```
**Impact**: This was causing incorrect bandwidth estimation for the fuzzy membership functions.

### 2. **Fuzzy Simplicial Set Symmetrization**
**Problem**: Missing proper symmetrization using fuzzy set union (probabilistic t-conorm).
```matlab
% ADDED: Proper fuzzy set union
A = sparse(rows, cols, vals, N, N);
A_sym = A + A' - A .* A';  % Probabilistic t-conorm: a ⊕ b = a + b - ab
[rows, cols, vals] = find(A_sym);
```
**Impact**: The original code wasn't properly combining the asymmetric local fuzzy simplicial sets.

### 3. **Parameter Computation (a and b)**
**Problem**: Hard-coded parameters that didn't adapt to `min_dist`.
```matlab
% WRONG:
a = 1.577/max(min_dist, 0.001);
b = 0.895;

% FIXED:
if min_dist > 0
    a = 1.929 * (1 - min_dist) / min_dist;
    b = 0.7915;
else
    a = 1.929;
    b = 0.7915;
end
```
**Impact**: These parameters control the shape of the low-dimensional similarity function.

### 4. **Gradient Computation in Optimization**
**Problem**: Incorrect gradient formulation for both attractive and repulsive forces.
```matlab
% FIXED: Attractive forces
pos_scaling = vals .* 2*a*b*pos_dist_sq.^(b-1) ./ (1 + a*pos_dist_sq.^b);

% FIXED: Repulsive forces  
neg_vals = 1./(1 + a*neg_dist_sq.^b);
neg_scaling = (1 - vals) .* 2*a*b*neg_dist_sq.^(b-1) .* neg_vals ./ (1 + a*neg_dist_sq.^b);
```
**Impact**: Proper gradients are essential for the cross-entropy optimization to work correctly.

## Key Algorithmic Concepts

### Fuzzy Simplicial Sets
- UMAP constructs a fuzzy topological representation of the high-dimensional data
- Each point has its own local metric based on distance to k-th nearest neighbor
- Local fuzzy simplicial sets are combined using fuzzy set union

### Cross-Entropy Optimization
- UMAP minimizes cross-entropy between high-dimensional and low-dimensional fuzzy simplicial sets
- Uses stochastic gradient descent with both attractive and repulsive forces
- The cost function is: `C = Σ[v_ij * log(v_ij/w_ij) + (1-v_ij) * log((1-v_ij)/(1-w_ij))]`

### Distance Functions
- High-dimensional: `v_ij = exp(-(d_ij - ρ_i)/σ_i)` where ρ_i is distance to nearest neighbor
- Low-dimensional: `w_ij = 1/(1 + a*||y_i - y_j||^(2b))`

## Testing
Run `test_umap.m` to verify the implementation works correctly on a Swiss roll dataset.

## References
- McInnes, L., Healy, J., & Melville, J. (2018). UMAP: Uniform Manifold Approximation and Projection for Dimension Reduction. arXiv:1802.03426
- Original Python implementation: https://github.com/lmcinnes/umap
