{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Autoencoder + UMAP\n", "This notebook extends the last notebook to train the embedding jointly on the reconstruction loss, and UMAP loss, resulting in slightly better reconstructions, and a slightly modified UMAP embedding. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### load data"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T01:25:15.362554Z", "start_time": "2020-08-17T01:25:15.000091Z"}}, "outputs": [], "source": ["from tensorflow.keras.datasets import mnist\n", "(train_images, Y_train), (test_images, Y_test) = mnist.load_data()\n", "train_images = train_images.reshape((train_images.shape[0], -1))/255.\n", "test_images = test_images.reshape((test_images.shape[0], -1))/255."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### define the encoder network"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tensorflow as tf"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T01:25:16.085174Z", "start_time": "2020-08-17T01:25:15.363839Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model: \"sequential\"\n", "_________________________________________________________________\n", "Layer (type)                 Output Shape              Param #   \n", "=================================================================\n", "conv2d (Conv2D)              (None, 14, 14, 64)        640       \n", "_________________________________________________________________\n", "conv2d_1 (Conv2D)            (None, 7, 7, 128)         73856     \n", "_________________________________________________________________\n", "flatten (Flatten)            (None, 6272)              0         \n", "_________________________________________________________________\n", "dense (Dense)                (None, 512)               3211776   \n", "_________________________________________________________________\n", "dense_1 (<PERSON><PERSON>)              (None, 512)               262656    \n", "_________________________________________________________________\n", "dense_2 (<PERSON><PERSON>)              (None, 2)                 1026      \n", "=================================================================\n", "Total params: 3,549,954\n", "Trainable params: 3,549,954\n", "Non-trainable params: 0\n", "_________________________________________________________________\n"]}], "source": ["dims = (28,28, 1)\n", "n_components = 2\n", "encoder = tf.keras.Sequential([\n", "    tf.keras.layers.InputLayer(input_shape=dims),\n", "    tf.keras.layers.Conv2D(\n", "        filters=64, kernel_size=3, strides=(2, 2), activation=\"relu\", padding=\"same\"\n", "    ),\n", "    tf.keras.layers.Conv2D(\n", "        filters=128, kernel_size=3, strides=(2, 2), activation=\"relu\", padding=\"same\"\n", "    ),\n", "    tf.keras.layers.<PERSON><PERSON>(),\n", "    tf.keras.layers.Dense(units=512, activation=\"relu\"),\n", "    tf.keras.layers.Dense(units=512, activation=\"relu\"),\n", "    tf.keras.layers.Dense(units=n_components),\n", "])\n", "encoder.summary()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T01:25:16.237219Z", "start_time": "2020-08-17T01:25:16.087260Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model: \"sequential_1\"\n", "_________________________________________________________________\n", "Layer (type)                 Output Shape              Param #   \n", "=================================================================\n", "dense_3 (<PERSON><PERSON>)              (None, 512)               1536      \n", "_________________________________________________________________\n", "dense_4 (<PERSON><PERSON>)              (None, 512)               262656    \n", "_________________________________________________________________\n", "dense_5 (<PERSON><PERSON>)              (None, 6272)              3217536   \n", "_________________________________________________________________\n", "reshape (Reshape)            (None, 7, 7, 128)         0         \n", "_________________________________________________________________\n", "up_sampling2d (UpSampling2D) (None, 14, 14, 128)       0         \n", "_________________________________________________________________\n", "conv2d_2 (Conv2D)            (None, 14, 14, 64)        73792     \n", "_________________________________________________________________\n", "up_sampling2d_1 (UpSampling2 (None, 28, 28, 64)        0         \n", "_________________________________________________________________\n", "conv2d_3 (Conv2D)            (None, 28, 28, 32)        18464     \n", "=================================================================\n", "Total params: 3,573,984\n", "Trainable params: 3,573,984\n", "Non-trainable params: 0\n", "_________________________________________________________________\n"]}], "source": ["decoder = tf.keras.Sequential([\n", "    tf.keras.layers.InputLayer(input_shape=(n_components)),\n", "    tf.keras.layers.Dense(units=512, activation=\"relu\"),\n", "    tf.keras.layers.Dense(units=7 * 7 * 256, activation=\"relu\"),\n", "    tf.keras.layers.Reshape(target_shape=(7, 7, 256)),\n", "    tf.keras.layers.Conv2DTranspose(\n", "        filters=128, kernel_size=3, strides=(2, 2), padding=\"SAME\", activation=\"relu\"\n", "    ),\n", "    tf.keras.layers.Conv2DTranspose(\n", "        filters=64, kernel_size=3, strides=(2, 2), padding=\"SAME\", activation=\"relu\"\n", "    ),\n", "    tf.keras.layers.Conv2DTranspose(\n", "        filters=1, kernel_size=3, strides=(1, 1), padding=\"SAME\", activation=\"sigmoid\"\n", "    )\n", "])\n", "decoder.summary()"]}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2020-08-16T22:12:46.790121Z", "start_time": "2020-08-16T22:12:46.759185Z"}}, "source": ["### create parametric umap model"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T01:25:21.373572Z", "start_time": "2020-08-17T01:25:16.238681Z"}}, "outputs": [], "source": ["from umap.parametric_umap import ParametricUMAP"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T01:25:21.455022Z", "start_time": "2020-08-17T01:25:21.420662Z"}}, "outputs": [], "source": ["embedder = ParametricUMAP(\n", "    encoder=encoder,\n", "    decoder=decoder,\n", "    dims=dims,\n", "    n_training_epochs=1,\n", "    n_components=n_components,\n", "    parametric_reconstruction= True,\n", "    autoencoder_loss = True,\n", "    reconstruction_validation=test_images,\n", "    verbose=True,\n", ")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T01:38:12.451321Z", "start_time": "2020-08-17T01:25:21.456455Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParametricUMAP(autoencoder_loss=True, n_training_epochs=5,\n", "               optimizer=<tensorflow.python.keras.optimizer_v2.adam.Adam object at 0x7f92a05514a8>,\n", "               parametric_reconstruction=True,\n", "               reconstruction_validation=array([[0., 0., 0., ..., 0., 0., 0.],\n", "       [0., 0., 0., ..., 0., 0., 0.],\n", "       [0., 0., 0., ..., 0., 0., 0.],\n", "       ...,\n", "       [0., 0., 0., ..., 0., 0., 0.],\n", "       [0., 0., 0., ..., 0., 0., 0.],\n", "       [0., 0., 0., ..., 0., 0., 0.]]))\n", "Construct fuzzy simplicial set\n", "Sun Aug 16 18:25:21 2020 Finding Nearest Neighbors\n", "Sun Aug 16 18:25:21 2020 Building RP forest with 17 trees\n", "Sun Aug 16 18:25:25 2020 parallel NN descent for 16 iterations\n", "\t 0  /  16\n", "\t 1  /  16\n", "\t 2  /  16\n", "\t 3  /  16\n", "\t 4  /  16\n", "Sun Aug 16 18:25:37 2020 Finished Nearest Neighbor Search\n", "Sun Aug 16 18:25:40 2020 Construct embedding\n", "Epoch 1/50\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.4622 - reconstruction_loss: 0.2297 - umap_loss: 0.2325 - val_loss: 0.2012 - val_reconstruction_loss: 0.2012 - val_umap_loss: 0.0000e+00\n", "Epoch 2/50\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.3612 - reconstruction_loss: 0.1873 - umap_loss: 0.1739 - val_loss: 0.1897 - val_reconstruction_loss: 0.1897 - val_umap_loss: 0.0000e+00\n", "Epoch 3/50\n", "724/724 [==============================] - 13s 18ms/step - loss: 0.3373 - reconstruction_loss: 0.1791 - umap_loss: 0.1581 - val_loss: 0.1834 - val_reconstruction_loss: 0.1834 - val_umap_loss: 0.0000e+00\n", "Epoch 4/50\n", "724/724 [==============================] - 13s 18ms/step - loss: 0.3224 - reconstruction_loss: 0.1739 - umap_loss: 0.1484 - val_loss: 0.1802 - val_reconstruction_loss: 0.1802 - val_umap_loss: 0.0000e+00\n", "Epoch 5/50\n", "724/724 [==============================] - 14s 20ms/step - loss: 0.3112 - reconstruction_loss: 0.1701 - umap_loss: 0.1411 - val_loss: 0.1777 - val_reconstruction_loss: 0.1777 - val_umap_loss: 0.0000e+00\n", "Epoch 6/50\n", "724/724 [==============================] - 13s 18ms/step - loss: 0.3052 - reconstruction_loss: 0.1679 - umap_loss: 0.1373 - val_loss: 0.1759 - val_reconstruction_loss: 0.1759 - val_umap_loss: 0.0000e+00\n", "Epoch 7/50\n", "724/724 [==============================] - 13s 18ms/step - loss: 0.2997 - reconstruction_loss: 0.1661 - umap_loss: 0.1336 - val_loss: 0.1752 - val_reconstruction_loss: 0.1752 - val_umap_loss: 0.0000e+00\n", "Epoch 8/50\n", "724/724 [==============================] - 14s 20ms/step - loss: 0.2966 - reconstruction_loss: 0.1648 - umap_loss: 0.1318 - val_loss: 0.1735 - val_reconstruction_loss: 0.1735 - val_umap_loss: 0.0000e+00\n", "Epoch 9/50\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.2938 - reconstruction_loss: 0.1638 - umap_loss: 0.1300 - val_loss: 0.1735 - val_reconstruction_loss: 0.1735 - val_umap_loss: 0.0000e+00\n", "Epoch 10/50\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.2911 - reconstruction_loss: 0.1630 - umap_loss: 0.1281 - val_loss: 0.1724 - val_reconstruction_loss: 0.1724 - val_umap_loss: 0.0000e+00\n", "Epoch 11/50\n", "724/724 [==============================] - 13s 18ms/step - loss: 0.2888 - reconstruction_loss: 0.1623 - umap_loss: 0.1265 - val_loss: 0.1724 - val_reconstruction_loss: 0.1724 - val_umap_loss: 0.0000e+00\n", "Epoch 12/50\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.2879 - reconstruction_loss: 0.1618 - umap_loss: 0.1260 - val_loss: 0.1715 - val_reconstruction_loss: 0.1715 - val_umap_loss: 0.0000e+00\n", "Epoch 13/50\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.2864 - reconstruction_loss: 0.1613 - umap_loss: 0.1250 - val_loss: 0.1710 - val_reconstruction_loss: 0.1710 - val_umap_loss: 0.0000e+00\n", "Epoch 14/50\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.2846 - reconstruction_loss: 0.1607 - umap_loss: 0.1239 - val_loss: 0.1706 - val_reconstruction_loss: 0.1706 - val_umap_loss: 0.0000e+00\n", "Epoch 15/50\n", "724/724 [==============================] - 15s 21ms/step - loss: 0.2843 - reconstruction_loss: 0.1606 - umap_loss: 0.1237 - val_loss: 0.1707 - val_reconstruction_loss: 0.1707 - val_umap_loss: 0.0000e+00\n", "Epoch 16/50\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.2833 - reconstruction_loss: 0.1602 - umap_loss: 0.1231 - val_loss: 0.1706 - val_reconstruction_loss: 0.1706 - val_umap_loss: 0.0000e+00\n", "Epoch 17/50\n", "724/724 [==============================] - 14s 20ms/step - loss: 0.2833 - reconstruction_loss: 0.1601 - umap_loss: 0.1232 - val_loss: 0.1703 - val_reconstruction_loss: 0.1703 - val_umap_loss: 0.0000e+00\n", "Epoch 18/50\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.2821 - reconstruction_loss: 0.1595 - umap_loss: 0.1226 - val_loss: 0.1698 - val_reconstruction_loss: 0.1698 - val_umap_loss: 0.0000e+00\n", "Epoch 19/50\n", "724/724 [==============================] - 14s 20ms/step - loss: 0.2806 - reconstruction_loss: 0.1591 - umap_loss: 0.1215 - val_loss: 0.1699 - val_reconstruction_loss: 0.1699 - val_umap_loss: 0.0000e+00\n", "Epoch 20/50\n", "724/724 [==============================] - 15s 20ms/step - loss: 0.2808 - reconstruction_loss: 0.1590 - umap_loss: 0.1219 - val_loss: 0.1697 - val_reconstruction_loss: 0.1697 - val_umap_loss: 0.0000e+00\n", "Epoch 21/50\n", "724/724 [==============================] - 15s 20ms/step - loss: 0.2803 - reconstruction_loss: 0.1589 - umap_loss: 0.1214 - val_loss: 0.1697 - val_reconstruction_loss: 0.1697 - val_umap_loss: 0.0000e+00\n", "Epoch 22/50\n", "724/724 [==============================] - 15s 20ms/step - loss: 0.2797 - reconstruction_loss: 0.1586 - umap_loss: 0.1211 - val_loss: 0.1695 - val_reconstruction_loss: 0.1695 - val_umap_loss: 0.0000e+00\n", "Epoch 23/50\n", "724/724 [==============================] - 14s 20ms/step - loss: 0.2791 - reconstruction_loss: 0.1584 - umap_loss: 0.1208 - val_loss: 0.1693 - val_reconstruction_loss: 0.1693 - val_umap_loss: 0.0000e+00\n", "Epoch 24/50\n", "724/724 [==============================] - 17s 23ms/step - loss: 0.2789 - reconstruction_loss: 0.1581 - umap_loss: 0.1208 - val_loss: 0.1689 - val_reconstruction_loss: 0.1689 - val_umap_loss: 0.0000e+00\n", "Epoch 25/50\n", "724/724 [==============================] - 16s 21ms/step - loss: 0.2784 - reconstruction_loss: 0.1580 - umap_loss: 0.1204 - val_loss: 0.1710 - val_reconstruction_loss: 0.1710 - val_umap_loss: 0.0000e+00\n", "Epoch 26/50\n", "724/724 [==============================] - 16s 22ms/step - loss: 0.2779 - reconstruction_loss: 0.1578 - umap_loss: 0.1201 - val_loss: 0.1691 - val_reconstruction_loss: 0.1691 - val_umap_loss: 0.0000e+00\n", "Epoch 27/50\n", "724/724 [==============================] - 16s 22ms/step - loss: 0.2780 - reconstruction_loss: 0.1578 - umap_loss: 0.1202 - val_loss: 0.1688 - val_reconstruction_loss: 0.1688 - val_umap_loss: 0.0000e+00\n", "Epoch 28/50\n", "724/724 [==============================] - 16s 22ms/step - loss: 0.2764 - reconstruction_loss: 0.1571 - umap_loss: 0.1192 - val_loss: 0.1686 - val_reconstruction_loss: 0.1686 - val_umap_loss: 0.0000e+00\n", "Epoch 29/50\n", "724/724 [==============================] - 16s 22ms/step - loss: 0.2774 - reconstruction_loss: 0.1574 - umap_loss: 0.1200 - val_loss: 0.1685 - val_reconstruction_loss: 0.1685 - val_umap_loss: 0.0000e+00\n", "Epoch 30/50\n", "724/724 [==============================] - 16s 22ms/step - loss: 0.2766 - reconstruction_loss: 0.1570 - umap_loss: 0.1196 - val_loss: 0.1682 - val_reconstruction_loss: 0.1682 - val_umap_loss: 0.0000e+00\n", "Epoch 31/50\n", "724/724 [==============================] - 16s 23ms/step - loss: 0.2763 - reconstruction_loss: 0.1569 - umap_loss: 0.1194 - val_loss: 0.1685 - val_reconstruction_loss: 0.1685 - val_umap_loss: 0.0000e+00\n", "Epoch 32/50\n", "724/724 [==============================] - 16s 23ms/step - loss: 0.2758 - reconstruction_loss: 0.1566 - umap_loss: 0.1192 - val_loss: 0.1695 - val_reconstruction_loss: 0.1695 - val_umap_loss: 0.0000e+00\n", "Epoch 33/50\n", "724/724 [==============================] - 16s 22ms/step - loss: 0.2756 - reconstruction_loss: 0.1565 - umap_loss: 0.1191 - val_loss: 0.1693 - val_reconstruction_loss: 0.1693 - val_umap_loss: 0.0000e+00\n", "Epoch 34/50\n", "724/724 [==============================] - 16s 22ms/step - loss: 0.2748 - reconstruction_loss: 0.1562 - umap_loss: 0.1186 - val_loss: 0.1678 - val_reconstruction_loss: 0.1678 - val_umap_loss: 0.0000e+00\n", "Epoch 35/50\n", "724/724 [==============================] - 16s 22ms/step - loss: 0.2750 - reconstruction_loss: 0.1562 - umap_loss: 0.1188 - val_loss: 0.1676 - val_reconstruction_loss: 0.1676 - val_umap_loss: 0.0000e+00\n", "Epoch 36/50\n", "724/724 [==============================] - 16s 22ms/step - loss: 0.2740 - reconstruction_loss: 0.1560 - umap_loss: 0.1181 - val_loss: 0.1679 - val_reconstruction_loss: 0.1679 - val_umap_loss: 0.0000e+00\n", "Epoch 37/50\n", "724/724 [==============================] - 17s 23ms/step - loss: 0.2750 - reconstruction_loss: 0.1561 - umap_loss: 0.1189 - val_loss: 0.1680 - val_reconstruction_loss: 0.1680 - val_umap_loss: 0.0000e+00\n", "Epoch 38/50\n", "724/724 [==============================] - 16s 22ms/step - loss: 0.2743 - reconstruction_loss: 0.1557 - umap_loss: 0.1186 - val_loss: 0.1687 - val_reconstruction_loss: 0.1687 - val_umap_loss: 0.0000e+00\n", "Epoch 39/50\n", "724/724 [==============================] - 16s 22ms/step - loss: 0.2738 - reconstruction_loss: 0.1555 - umap_loss: 0.1182 - val_loss: 0.1679 - val_reconstruction_loss: 0.1679 - val_umap_loss: 0.0000e+00\n", "Epoch 40/50\n", "724/724 [==============================] - 16s 22ms/step - loss: 0.2738 - reconstruction_loss: 0.1554 - umap_loss: 0.1184 - val_loss: 0.1681 - val_reconstruction_loss: 0.1681 - val_umap_loss: 0.0000e+00\n", "Epoch 41/50\n", "724/724 [==============================] - 15s 21ms/step - loss: 0.2731 - reconstruction_loss: 0.1551 - umap_loss: 0.1179 - val_loss: 0.1671 - val_reconstruction_loss: 0.1671 - val_umap_loss: 0.0000e+00\n", "Epoch 42/50\n", "724/724 [==============================] - 16s 22ms/step - loss: 0.2729 - reconstruction_loss: 0.1552 - umap_loss: 0.1177 - val_loss: 0.1676 - val_reconstruction_loss: 0.1676 - val_umap_loss: 0.0000e+00\n", "Epoch 43/50\n", "724/724 [==============================] - 15s 21ms/step - loss: 0.2733 - reconstruction_loss: 0.1553 - umap_loss: 0.1180 - val_loss: 0.1670 - val_reconstruction_loss: 0.1670 - val_umap_loss: 0.0000e+00\n", "Epoch 44/50\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.2729 - reconstruction_loss: 0.1550 - umap_loss: 0.1178 - val_loss: 0.1671 - val_reconstruction_loss: 0.1671 - val_umap_loss: 0.0000e+00\n", "Epoch 45/50\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.2726 - reconstruction_loss: 0.1549 - umap_loss: 0.1177 - val_loss: 0.1668 - val_reconstruction_loss: 0.1668 - val_umap_loss: 0.0000e+00\n", "Epoch 46/50\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.2720 - reconstruction_loss: 0.1547 - umap_loss: 0.1172 - val_loss: 0.1673 - val_reconstruction_loss: 0.1673 - val_umap_loss: 0.0000e+00\n", "Epoch 47/50\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.2719 - reconstruction_loss: 0.1547 - umap_loss: 0.1171 - val_loss: 0.1667 - val_reconstruction_loss: 0.1667 - val_umap_loss: 0.0000e+00\n", "Epoch 48/50\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.2719 - reconstruction_loss: 0.1545 - umap_loss: 0.1173 - val_loss: 0.1663 - val_reconstruction_loss: 0.1663 - val_umap_loss: 0.0000e+00\n", "Epoch 49/50\n", "724/724 [==============================] - 13s 19ms/step - loss: 0.2718 - reconstruction_loss: 0.1544 - umap_loss: 0.1173 - val_loss: 0.1668 - val_reconstruction_loss: 0.1668 - val_umap_loss: 0.0000e+00\n", "Epoch 50/50\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.2722 - reconstruction_loss: 0.1544 - umap_loss: 0.1177 - val_loss: 0.1660 - val_reconstruction_loss: 0.1660 - val_umap_loss: 0.0000e+00\n", "1875/1875 [==============================] - 2s 816us/step\n", "Sun Aug 16 18:38:12 2020 Finished embedding\n"]}], "source": ["embedding = embedder.fit_transform(train_images)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### plot reconstructions"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T01:38:33.807826Z", "start_time": "2020-08-17T01:38:33.767248Z"}}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T01:38:34.213326Z", "start_time": "2020-08-17T01:38:34.023668Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["10/10 [==============================] - 0s 2ms/step\n", "10/10 [==============================] - 0s 1ms/step\n"]}], "source": ["test_images_recon = embedder.inverse_transform(embedder.transform(test_images))"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T01:38:34.945281Z", "start_time": "2020-08-17T01:38:34.214974Z"}}, "outputs": [{"data": {"image/png": "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*****************************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\n", "text/plain": ["<Figure size 720x144 with 20 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["nex = 10\n", "fig, axs = plt.subplots(ncols=10, nrows=2, figsize=(nex, 2))\n", "for i in range(nex):\n", "    axs[0, i].matshow(np.squeeze(test_images[i].reshape(28, 28, 1)), cmap=plt.cm.Greys)\n", "    axs[1, i].matshow(\n", "        tf.nn.sigmoid(np.squeeze(test_images_recon[i].reshape(28, 28, 1))),\n", "        cmap=plt.cm.<PERSON>,\n", "    )\n", "for ax in axs.flatten():\n", "    ax.axis(\"off\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### plot results"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T01:38:12.856292Z", "start_time": "2020-08-17T01:38:12.453257Z"}}, "outputs": [], "source": ["embedding = embedder.embedding_"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T01:38:13.014546Z", "start_time": "2020-08-17T01:38:12.858420Z"}}, "outputs": [], "source": ["import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T01:38:14.488114Z", "start_time": "2020-08-17T01:38:13.015738Z"}}, "outputs": [{"data": {"image/png": "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*********************************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\n", "text/plain": ["<Figure size 576x576 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots( figsize=(8, 8))\n", "sc = ax.scatter(\n", "    embedding[:, 0],\n", "    embedding[:, 1],\n", "    c=Y_train.astype(int),\n", "    cmap=\"tab10\",\n", "    s=0.1,\n", "    alpha=0.5,\n", "    rasterized=True,\n", ")\n", "ax.axis('equal')\n", "ax.set_title(\"UMAP in Tensorflow embedding\", fontsize=20)\n", "plt.colorbar(sc, ax=ax);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### plotting loss"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T05:44:02.572881Z", "start_time": "2020-08-17T05:44:02.530419Z"}}, "outputs": [{"data": {"text/plain": ["dict_keys(['loss', 'reconstruction_loss', 'umap_loss', 'val_loss', 'val_reconstruction_loss', 'val_umap_loss'])"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["embedder._history.keys()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T05:44:02.906686Z", "start_time": "2020-08-17T05:44:02.662782Z"}}, "outputs": [{"data": {"text/plain": ["Text(0.5, 0, 'Epoch')"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAm4AAAE9CAYAAACyWu7XAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4xLjEsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy8QZhcZAAAgAElEQVR4nOzdeXicdbn/8fc9k0mmTfcmXdMlLaWlLaXQUJBNFoECauGIUlTkIIuoiNv5HRDPcTnqETioiAesFavoASoChaoFREQKWKAtQjdautKma7qvSZv0/v3xTNohnaSTZCbJPPm8rmuumWf5PnOnXny957uauyMiIiIibV+ktQMQERERkfQocRMRERHJEUrcRERERHKEEjcRERGRHKHETURERCRHKHETERERyRF5rR1ASygqKvLBgwe3dhgi0oLmzZu3xd2LWzuO5lL9JdL+NFR/tYvEbfDgwcydO7e1wxCRFmRm77V2DJmg+kuk/Wmo/lJXqYiIiEiOUOImIiIikiOUuImIiIjkiHYxxk1ERERyw8GDBykvL6eysrK1Q8m6eDxOSUkJsVgs7TJK3ERERKTNKC8vp3PnzgwePBgza+1wssbd2bp1K+Xl5ZSWlqZdTl2lIiKNZGYTzGypmS03s9tTXP+Umc1PvP5hZiclzsfN7A0ze9vMFpnZd1s+epG2rbKykp49e4Y6aQMwM3r27NnolkW1uImINIKZRYH7gQuBcmCOmc1w98VJt60CPuju283sEmAKcBpQBZzv7nvMLAa8YmbPuPtrLfxniLRpYU/aajXl71SLm4hI44wHlrv7Snc/AEwDJibf4O7/cPfticPXgJLEeXf3PYnzscTLWyZsEUnHjh07eOCBBxpd7tJLL2XHjh1ZiOj9lLiJiDROf2Bt0nF54lx9rgeeqT0ws6iZvQVsBp5399ezEqWINEl9iVtNTU2D5WbOnEm3bt2yFdZhStySbNi5n0deX0PF7qrWDkVE2q5UfRspW83M7DyCxO22wze617j7WIJWuPFmNjpFuZvMbK6Zza2oqEg7sCffLOeNVdvSvl9Ejnb77bezYsUKxo4dy6mnnsp5553HJz/5SU488UQALr/8csaNG8eoUaOYMmXK4XKDBw9my5YtrF69mhNOOIEbb7yRUaNGcdFFF7F///6MxafELcnyzXu4Y/oCVm/d29qhiEjbVQ4MSDouAdbXvcnMxgAPAhPdfWvd6+6+A/g7MCHFtSnuXubuZcXF6W+3+t8zlzD9n+vSvl9EjnbnnXcydOhQ3nrrLf7nf/6HN954gx/84AcsXhwMY506dSrz5s1j7ty53HfffWzdetR/3ixbtowvfvGLLFq0iG7duvHEE09kLD5NTkgSj0UB2H+g4eZQEWnX5gDDzKwUWAdMAj6ZfIOZDQSeBK5x93eTzhcDB919h5l1AD4E3JWpwAryIlRVq/6S8PjuHxexeP2ujD5zZL8ufPsjo9K+f/z48e9bruO+++5j+vTpAKxdu5Zly5bRs2fP95UpLS1l7NixAIwbN47Vq1c3P/AEJW5J4nlB4lZ5UBWfiKTm7tVmdgvwHBAFprr7IjO7OXF9MvAtoCfwQGLWWLW7lwF9gYcSM1MjwGPu/qdMxRaPRag6eChTjxMRoLCw8PDnv//97/z1r39l9uzZdOzYkXPPPTflch4FBQWHP0ej0Yx2lSpxS9IhP+g5rqxWxSci9XP3mcDMOucmJ32+AbghRbn5wMnZiisei+qHp4RKY1rGMqVz587s3r075bWdO3fSvXt3OnbsyJIlS3jttZZfyUeJW5ICtbiJSA4Lukr1w1OkOXr27MmZZ57J6NGj6dChA7179z58bcKECUyePJkxY8YwfPhwTj/99BaPT4lbktoxblVK3EQkB6nFTSQzHnnkkZTnCwoKeOaZZ1Jeqx3HVlRUxMKFCw+f/7d/+7eMxqZZpUniseCfY78qPhHJQfFYlEpNThAJNSVuSWpb3Co1uFdEclBBniYniISdErcksWiEvIipq0FEcpJa3ETCT4lbHcEYEf1iFZHcE49FVH+JhJwStzrisYh+sYpITirIi2pylUjIKXGroyBPs7JEJDcVxCJah1Ik5LKauJnZBDNbambLzez2Bu471cxqzOzKpHOrzWyBmb1lZnOTzvcws+fNbFnivXsmYw66GpS4iUjuiedFOVB9iEOHUu55LyJZ0KlTJwDWr1/PlVdemfKec889l7lz56a81lhZS9wSW7rcD1wCjASuNrOR9dx3F8H2MXWd5+5jE1vF1LodeMHdhwEvJI4zRmPcRCRXFSSWNDpQozpMpKX169ePxx9/POvfk80Wt/HAcndf6e4HgGnAxBT3fQl4Atic5nMnAg8lPj8EXN7cQJN10AKWIpKjtN+ySPPddtttPPDAA4ePv/Od7/Dd736XCy64gFNOOYUTTzyRp59++qhyq1evZvTo0QDs37+fSZMmMWbMGK666qqM7lWazcStP7A26bg8ce4wM+sPXAFM5mgO/MXM5pnZTUnne7v7BoDEe69MBq2Vx0UkV2ktSpHmmzRpEr///e8PHz/22GNcd911TJ8+nTfffJMXX3yRr3/967jXPyTh5z//OR07dmT+/Pl885vfZN68eRmLL5tbXlmKc3X/ynuB29y9xuyo28909/Vm1gt43syWuPustL88SPZuAhg4cGDaQcdjEbbtVaUnIrmnIC/4LV6lmfESFs/cDhsXZPaZfU6ES+6s9/LJJ5/M5s2bWb9+PRUVFXTv3p2+ffvy1a9+lVmzZhGJRFi3bh2bNm2iT58+KZ8xa9Ysbr31VgDGjBnDmDFjMhZ+NhO3cmBA0nEJsL7OPWXAtETSVgRcambV7v6Uu68HcPfNZjadoOt1FrDJzPq6+wYz60s9XazuPgWYAlBWVpb2SN0CtbiJSI5Si5tIZlx55ZU8/vjjbNy4kUmTJvHwww9TUVHBvHnziMViDB48mMrKygafkaJBKiOymbjNAYaZWSmwDpgEfDL5Bncvrf1sZr8B/uTuT5lZIRBx992JzxcB/5W4dQZwLXBn4v3ojuZmiGs5EBHJUbX7LasOk9BooGUsmyZNmsSNN97Ili1beOmll3jsscfo1asXsViMF198kffee6/B8ueccw4PP/ww5513HgsXLmT+/PkZiy1riZu7V5vZLQSzRaPAVHdfZGY3J66nGtdWqzcwPZGt5gGPuPuziWt3Ao+Z2fXAGuDjmYy7Q77WQRKR3FSQmJxQpTpMpFlGjRrF7t276d+/P3379uVTn/oUH/nIRygrK2Ps2LGMGDGiwfKf//znue666xgzZgxjx45l/PjxGYstmy1uuPtMYGadcykTNnf/16TPK4GT6rlvK3BB5qJ8P7W4iUiuUoubSOYsWHBkbF1RURGzZ89Oed+ePXsAGDx4MAsXLgSgQ4cOTJs2LStxaeeEOmpnlTY0W0REpC06MsZNiZtIWClxqyMei3DItYCliOSeI7NKVX+JhJUStzo0K0tEcpVa3ETCT4lbHbUVX5UqPhHJMbUtbppgJbmuvQxXasrfqcStDrW4iUiuKtAPTwmBeDzO1q1bQ5+8uTtbt24lHo83qlxWZ5XmosOzsrTyuIjkmNr6S2PcJJeVlJRQXl5ORUVFa4eSdfF4nJKSkkaVUeJWhzZpFpFclR+NYKb6S3JbLBajtLT02De2U+oqraO2q3T/AVV8IpKamU0ws6VmttzMbk9x/VNmNj/x+oeZnZQ4P8DMXjSzd8xskZl9OcNxUZAXUYubSIipxa2OI12lqvhE5GhmFgXuBy4k2JN5jpnNcPfFSbetAj7o7tvN7BKCfZNPA6qBr7v7m2bWGZhnZs/XKdssce23LBJqanGrQ9PpReQYxgPL3X2lux8ApgETk29w93+4+/bE4WtASeL8Bnd/M/F5N/AO0D+TwRXkRVR/iYSYErc6lLiJyDH0B9YmHZfTcPJ1PfBM3ZNmNhg4GXg9g7ERj0XVVSoSYuoqrePwrCwtByIiqVmKcynXLTCz8wgSt7PqnO8EPAF8xd13pSh3E3ATwMCBAxsVnPZbFgk3tbjVcXhygio+EUmtHBiQdFwCrK97k5mNAR4EJrr71qTzMYKk7WF3fzLVF7j7FHcvc/ey4uLiRgVXEItoHUqREFPiVoe6SkXkGOYAw8ys1MzygUnAjOQbzGwg8CRwjbu/m3TegF8B77j7j7MRXDwvSpXWoRQJLXWV1hGv3TJGv1hFJAV3rzazW4DngCgw1d0XmdnNieuTgW8BPYEHglyNancvA84ErgEWmNlbiUfe4e4zMxVfQSzC7srqTD1ORNoYJW515EUjxKKmnRNEpF6JRGtmnXOTkz7fANyQotwrpB4jlzEFeVEqDlZl8ytEpBWpqzQFDe4VkVwVj0U4oFmlIqGlxC2FAi1gKSI5SgvwioSbErcU4pqVJSI5qiAvop1fREJMiVsKHfSLVURyVDwWpUr1l0hoKXFLQV0NIpKr4jG1uImEmRK3FNRVKiK5qiAvSs0h52CN6jCRMFLilkI8FtXOCSKSkw5v26dWN5FQUuKWQoGWAxGRHKXdX0TCLauJm5lNMLOlZrbczG5v4L5TzazGzK5MHA8wsxfN7B0zW2RmX0669ztmts7M3kq8Ls103PFYRL9WRSQnFRze/UWJm0gYZW3nBDOLAvcDFxJsyjzHzGa4++IU991FsH1MrWrg6+7+ppl1BuaZ2fNJZX/i7vdkK3bNKhWRXFXb4qYfnyLhlM0Wt/HAcndf6e4HgGnAxBT3fQl4Athce8LdN7j7m4nPu4F3gP5ZjPV9NKtURHJVQZ66SkXCLJuJW39gbdJxOXWSLzPrD1wBTKYeZjYYOBl4Pen0LWY238ymmln3TAVcS7NKRSRXFcRqu0pVh4mEUTYTt1QbKXud43uB29w95U9DM+tE0Br3FXfflTj9c2AoMBbYAPyonrI3mdlcM5tbUVHRqMBrZ5W61w1XRKRti+fVdpWqxU0kjLI2xo2ghW1A0nEJsL7OPWXANDMDKAIuNbNqd3/KzGIESdvD7v5kbQF331T72cx+Cfwp1Ze7+xRgCkBZWVmjMrDkMSK1n0VEcsHh5UDU4iYSStlM3OYAw8ysFFgHTAI+mXyDu5fWfjaz3wB/SiRtBvwKeMfdf5xcxsz6uvuGxOEVwMJMB344cTuoxE1EckuBWtxEQi1riZu7V5vZLQSzRaPAVHdfZGY3J67XO64NOBO4BlhgZm8lzt3h7jOBu81sLEG362rgc5mOvfYXa2V1DV2JZfrxIiJZE9cYN5FQy2aLG4lEa2adcykTNnf/16TPr5B6jBzufk0GQ0wprllZIpKjtACvSLhp54QUais+bXslIrmmdgFereMmEk5K3FJQV4OI5Cq1uImEmxK3FFTxiUiuOrLllX54ioSRErcUlLiJSK7Ki0bIi5hmlYqElBK3FNRVKiK5LNi2T/WXSBgpcUtBLW4ikssK8iJUqsVNJJSUuKWgxE1Eclk8FtXOCSIhpcQthfjhwb1K3ETkaGY2wcyWmtlyM7s9xfVPmdn8xOsfZnZS0rWpZrbZzDK+60utgpha3ETCSolbCh3yEy1uWgdJROowsyhwP3AJMBK42sxG1rltFfBBdx8DfI/EvskJvwEmZDPGgrwoVfrhKRJKStxS0M4JItKA8cByd1/p7geAacDE5Bvc/R/uvj1x+BpQknRtFrAtmwHGYxEtwCsSUkrcUohEjPxoRLOyRCSV/sDapOPyxLn6XA88k9WI6ojnRfXDUySksrpXaS4riEVU8YlIKqn2UfaUN5qdR5C4ndWoLzC7CbgJYODAgY2Nj4JYhD17qhtdTkTaPrW41SNYB0mJm4gcpRwYkHRcAqyve5OZjQEeBCa6+9bGfIG7T3H3MncvKy4ubnSA8byoFuAVCSklbvXooMRNRFKbAwwzs1IzywcmATOSbzCzgcCTwDXu/m5LBxiPaaiHSFgpcauHKj4RScXdq4FbgOeAd4DH3H2Rmd1sZjcnbvsW0BN4wMzeMrO5teXN7FFgNjDczMrN7PpMx1igMW4ioaUxbvWIx6JaB0lEUnL3mcDMOucmJ32+AbihnrJXZzc6zSoVCTO1uNUjnhdl/wElbiKSezRGVyS8lLjVI1h5XL9YRST3FOQFLW7uKSe7ikgOU+JWj2CvP/1iFZHcU5DYb1ndpSLho8StHppVKiK5Kl6buGmClUjoKHGrh2aVikiuKsgLqnZNsBIJHyVu9YjHouxXi5uI5CC1uImElxK3emhWlojkqnhMLW4iYZXVxM3MJpjZUjNbbma3N3DfqWZWY2ZXHqusmfUws+fNbFnivXs2Yo9rVpaI5KiCvKDFTT8+RcIna4mbmUWB+4FLgJHA1WY2sp777iJYhTydsrcDL7j7MOCFxHHGxfM1K0tEclNti5vqL5HwyWaL23hgubuvdPcDwDRgYor7vgQ8AWxOs+xE4KHE54eAy7MRfFy/WEUkR9WOcVP9JRI+2Uzc+gNrk47LE+cOM7P+wBXAZN6vobK93X0DQOK9VwZjPqy24tMEBRHJNYdnlWpygkjoZDNxsxTn6g4Yuxe4zd3rZkfplG34y81uMrO5Zja3oqKiMUWBpMG9qvhEJMccnlWqyQkioZPNTebLgQFJxyXA+jr3lAHTzAygCLjUzKqPUXaTmfV19w1m1pf3d7Ee5u5TgCkAZWVljZ5hoK4GEclVR4Z66IenSNhks8VtDjDMzErNLB+YBMxIvsHdS919sLsPBh4HvuDuTx2j7Azg2sTna4GnsxF8ByVuIpKjCg73GKj+EgmbrLW4uXu1md1CMFs0Ckx190VmdnPiet1xbccsm7h8J/CYmV0PrAE+no34C9RVKiI5qrbFTbNKRcInm12luPtMYGadcykTNnf/12OVTZzfClyQuShTO9xVqjEiIpJj1OImEl7aOaEeh8eIHFDFJyK5pXZWaZUSN5HQUeJWD20ZIyK5yswoSOz+IiLhosStHkdmlariE5Hco/2WRcJJiVs9NKtURHJZQV5EPzxFQuiYiVti39B2Ry1uIuEW9rotHotqAV6REEqnxW25mf1Pqg3iw6x2cK+2vBIJrVDXbfGYWtxEwiidxG0M8C7woJm9lthKqkuW42p1kYiRnxfRrCyR8Ap13VaQF9XkKpEQOmbi5u673f2X7n4G8O/At4ENZvaQmR2X9QhbUTwvojFuIiEVyrrtkavg1fuAoMWtSi1uIqGT1hg3M/uomU0Hfgr8CBgC/JEUC+SGSYf8qLoaREKqOXWbmU0ws6VmttzMbk9x/VNmNj/x+oeZnZRu2WbZsgzWvwmoxU0krNLZOWEZ8CLwP+7+j6Tzj5vZOdkJq22Ix1TxiYRYk+q2xKSG+4ELgXJgjpnNcPfFSbetAj7o7tvN7BJgCnBammWbrttA2LEGCFrctu7VD0+RsEkncRvj7ntSXXD3WzMcT5sSz4uyXzsniIRVU+u28cByd18JYGbTgInA4eSrTiL4GlCSbtlm6TYQlgaNhQWaVSoSSulMTuhlZn80sy1mttnMnjazIVmPrA2IxyJUauVxkbBqat3WH1ibdFyeOFef64Fnmli2cboNhL0VcGBfsHOChnqIhE46idsjwGNAH6Af8Afg0WwG1VYUaOVxkTBrat1mKc55yhvNziNI3G5rTNnEDNe5Zja3oqIijZASug0M3neu1c4JIiGVTuJm7v47d69OvP6PeiqpsInHoloORCS8mlq3lQMDko5LgPVHPdxsDPAgMNHdtzamrLtPcfcydy8rLi5O88/hSOK2Yy3xvKj2KhUJoXQStxfN7HYzG2xmg8zs34E/m1kPM+uR7QBbUwctYCkSZk2t2+YAw8ys1MzygUnAjOQbzGwg8CRwjbu/25iyzXI4cXuPgpiWMxIJo3QmJ1yVeP9cnfOfJfh1GtrxbppVKhJqTarb3L3azG4BngOiwFR3X2RmNyeuTwa+BfQEHjAzgOpEC1rKshn7izr1gUgMdqwhnncO1Yec6ppD5EW1LbVIWBwzcXP30pYIpC3SrFKR8GpO3ebuM6mz1lsiYav9fANwQ7plMyYSgW4DgsStV5CsVVUrcRMJk2MmbmYWAz4P1K5r9HfgF+5+MItxtQlxdTWIhFZo67bEWm4F/YJkrfJgDYUF6XSuiEguSOdn2M+BccADide4xLnQC7pKNcZNJKTCWbclErd4LAqgCQoiIZPOz7BT3f2kpOO/mdnb2QqoLYnHohyoPsShQ04kkmoWv4jksHDWbd0Gwt7NdIwEDYfqNRAJl3Ra3GrMbGjtQWKBynZRE+gXq0iohbNu6zYoeKvaCKCZ8SIhk06L2/8jmDa/kmDxyEHAdVmNqo2Ix4K8dv/BGjrkR1s5GhHJsHDWbYklQboe2AB00LZXIiHTYOJmZhFgPzAMGE5QuS1x96oWiK3V1ba4qatBJFxCXbclErdOleuBoWpxEwmZBrtK3f0Q8CN3r3L3+e7+digqtjTVtrgpcRMJl1DXbYm13Ar3rQPQWpQiIZPOGLe/mNnHLLGKZGOY2QQzW2pmy83s9hTXJ5rZfDN7K7Ev31mJ88MT52pfu8zsK4lr3zGzdUnXLm1sXOnqcLjFTb9YRUKoyXVbm5ZYy63D3iBx00bzIuGSzhi3rwGFQLWZVRJ0Kbi7d2mokJlFgfuBCwn255tjZjPcfXHSbS8AM9zdE/v6PQaMcPelwNik56wDpieV+4m735PWX9gMBbWJm36xioRRk+q2nNBtIAV7ywE0xk0kZNLZOaFzE589Hlju7isBzGwaMBE4nLi5+56k+wtJvcHzBcAKd3+viXE0WTwvkbhp9wSR0GlG3db2dRtIbOMzgIZ6iITNMbtKzeyFdM6l0B9Ym3RcnjhX91lXmNkS4M8EewTWNQl4tM65WxJdrFPNrHs9cd+U6H6dW1FRkUa4Rzs8xk2/WEVCpxl1W9vXbSDRfRUUcEDLGYmETL2Jm5nFzawHUGRm3c2sR+I1GOiXxrNTjRs5qkXN3ae7+wjgcuB7dWLIBz4K/CHp9M+BoQRdqRuAH6X6cnefktjUuay4uDiNcI8W1xg3kdDJQN3W9iXWciuxCrW4iYRMQ12lnwO+QlCRzeNIIraLYOzasZQDA5KOS4D19d3s7rPMbKiZFbn7lsTpS4A33X1T0n2HP5vZL4E/pRFLk2g5EJFQam7d1vYllgQpsS364SkSMvUmbu7+U+CnZvYld/9ZE549BxhmZqUEkwsmAZ9MvsHMjiMYv+ZmdgqQD2xNuuVq6nSTmllfd9+QOLwCWNiE2NKiWaUi4ZOBuq3tSyRuAyIVmpwgEjLpTE74mZmdAQxOvt/df3uMctVmdgvwHBAFprr7IjO7OXF9MvAx4DNmdpBgMcyr3N0BzKwjwYzUz9V59N1mNpag23V1iusZk7xzgoiES1PrtpyQWMttcHQrG/TDUyRUjpm4mdnvCMaUvcWRffwcOGbl5u4zgZl1zk1O+nwXcFc9ZfcBPVOcv+ZY35sp6ioVCa/m1G1tXiQCXUsYsKOCVaq/REIlnXXcyoCRtS1h7UlBXtDiVqWKTySMwl23dRtI/x3rNatUJGTS2TlhIdAn24G0RWZGQV6ESlV8ImEU7rqt20D6oVmlImGTTotbEbDYzN4ADu/l5+4fzVpUbUiH/KgqPpFwCnfd1m0QPXwHNVX7WzsSEcmgdBK372Q7iLYsnqfETSSkvtPaAWRVYmZpp6oNx7hRRHJJvYmbmY1w9yXu/pKZFbh7VdK101smvNYXj0XYr1lZIqHRbuq2ROLWTYmbSKg0NMbtkaTPs+tceyALsbRJ8Zha3ERCpn3UbbWJ24GNrRyIiGRSQ4mb1fM51XFoFShxEwmb9lG3de5DNXn0rFbiJhImDSVuXs/nVMeh1SEWoUpdpSJh0j7qtkiU7bFeFFdvOva9IpIzGpqcUGJm9xH8Aq39TOK4f9YjayPisSjb9h5o7TBEJHOaXbeZ2QTgpwS7wjzo7nfWuT4C+DVwCvBNd78n6dqXgRsT3/dLd7+3mX9PvXbk96XXPiVuImHSUOL2/5I+z61zre5xaMXzouw/oK5SkRBpVt1mZlGCzegvBMqBOWY2w90XJ922DbgVuLxO2dEESdt44ADwrJn92d2XNfqvSMOueF8G7l2RjUeLSCtpaJP5h1oykLYqHotQqU2aRUIjA3XbeGC5u68EMLNpwETgcOLm7puBzWZ2WZ2yJwCvJbb0w8xeAq4A7m5mTCntjvejmB1wcD/EOmTjK0SkhaWzc0K7Fswq1Rg3ETmsP7A26bic9IePLATOMbOeZtYRuBQYkOH4DtvXMQjLd6w9xp0ikiuUuB1DYUEeuysPEtbtDEWk0VLNPE2rgnD3d4C7gOeBZ4G3geqjvsDsJjOba2ZzKyoqmhxoZWGQuFVvXdnkZ4hI26LE7RgG9exI5cFDbNpVdeybRaQ9KOf9rWQlwPp0C7v7r9z9FHc/h2As3FHj29x9iruXuXtZcXFxkwPd3XU4h9yoWdtuhiWLhN4xEzczu9vMuphZzMxeMLMtZvbplgiuLRhS1AmAlRV7WjkSEcmkZtRtc4BhZlZqZvnAJGBGI763V+J9IPAvwKNNiT8dkQ5dWeoDiJS/ka2vEJEWlk6L20Xuvgv4MMEvzeN5/6ysUBtSXAjAii17WzkSEcmwJtVt7l4N3AI8B7wDPObui8zsZjO7GcDM+phZOfA14D/MrNzMuiQe8YSZLQb+CHzR3bdn/C9LiOdFmHvoePLWz4NDmmQlEgbpbDIfS7xfCjzq7tvMwrO4+LH06RKnQyyqFjeR8Gly3ebuM4GZdc5NTvq8kaALNVXZs5sUbRMUxKK8euh4rjn4V9i8GPqc2FJfLSJZkk6L2x/NbAlQBrxgZsVAZXbDajsiEaO0qJCVFWpxEwmZ0Ndt8bwIc/344GDt660bjIhkxDETN3e/HfgAUObuB4G9BGsWtRtDigtZpa5SkVBpD3VbPBal3Is50KEY1ihxEwmDdCYnfByodvcaM/sP4P+AflmPrA0ZUlRI+fZ9VGkhXpHQaA91W0FeBDB2FY1Ti5tISKTTVfqf7r7bzM4CLgYeAn6e3bDaliHFnTjk8N7Wfa0diohkTujrtsKCYBhzRbeTYJ1BAigAACAASURBVMd7sHtjK0ckIs2VTuJW28x0GfBzd38ayM9eSG1P7cxSTVAQCZXQ122lRUHdNT9yQnBCrW4iOS+dxG2dmf0C+AQw08wK0iwXGrWV3wpNUBAJk9DXbYUFeQzo0YFX9/aDvLjGuYmEQDqV1CcI1iua4O47gB6kuY6bmU0ws6VmttzMbk9xfaKZzTeztxLbu5yVdG21mS2ovZZ0voeZPW9myxLv3dOJpTk6x2P06lygmaUi4dLkui2XDO/dhUWb9kO/U9TiJhIC6cwq3QesAC42s1uAXu7+l2OVM7MocD9wCTASuNrMRta57QXgJHcfC3wWeLDO9fPcfay7lyWdux14wd2HJcoflRBmw5DiQlZuUVepSFg0tW7LNSP6dGb11n1U9z8VNrwNB/e3dkgi0gzpzCr9MvAw0Cvx+j8z+1Iazx4PLHf3le5+AJhGnan27r7Hj+zeXkh6GzVPJBhETOL98jTKNNuQ4k5aEkQkRJpRt+WU4X06U3PIWddlDBw6COv/2dohiUgzpNNVej1wmrt/y92/BZwO3JhGuf7A2qTj8sS59zGzKxKLYP6ZoNWtlgN/MbN5ZnZT0vne7r4BIPHeK41Ymm1IUSE79h1k294DLfF1IpJ9Ta3bcsqIPp0BWMDw4IS6S0VyWjqJm3Fk9hWJz+nsC5PqnqNa1Nx9uruPIGg5+17SpTPd/RSCrtYvmtk5aXznkS83uykxbm5uRUVFY4qmpJmlIqHT1LotpwwuKiQ/GmHB9jzoOUwTFERyXDqJ26+B183sO2b2HeA14FdplCsHBiQdlwDr67vZ3WcBQ82sKHG8PvG+GZhO0PUKsMnM+gIk3jfX87wp7l7m7mXFxcVphNuwIUWdADRBQSQ8mlq35ZRYNMLQXp1YsnE3DDgtaHHzdEaliEhblM7khB8D1wHbgO3Ade5+bxrPngMMM7NSM8sHJgEzkm8ws+MssauzmZ1CsIbSVjMrNLPOifOFwEXAwkSxGcC1ic/XAk+nEUuzlXTvQCxqrNAEBZFQaEbdlnNG9OnM0o27YcB42L8Nti5v7ZBEpInyGrpoZhFgvruPBt5szIPdvToxU+s5IApMdfdFZnZz4vpk4GPAZ8zsILAfuMrd3cx6A9MTOV0e8Ii7P5t49J3AY2Z2PbAG+Hhj4mqqvGiEQT212bxIGDSnbstFw/t0Zvo/17G7VxmdIWh1KxrW2mGJSBM0mLi5+yEze9vMBrr7msY+3N1nAjPrnJuc9Pku4K4U5VYCJ9XzzK3ABY2NJROGFBWyQmPcRHJec+u2XDM8MUFh8YFenBbvFiRuJ3+6laMSkaZoMHFL6AssMrM3gMPNTe7+0axF1UYNKe7Ei0s3U11ziLxoqBZYF2mP2k3dVjuzdOnmvZw24DRNUBDJYekkbt/NehQ5YkhxIQdrnPLt+xmc2AZLRHJWu6nb+nSJ0zmeF0xQGHwWLHsOKpZC8fDWDk1EGqneZqPExIEz3f2l5BfBkh7lLRdi2zG0dkkQTVAQyVntsW4zsyMTFMZ+EqIF8NoDrR2WiDRBQ/199wK7U5zfl7jW7mhJEJFQaJd12/A+nXl34268Y084aRK8PQ32bm3tsESkkRpK3Aa7+/y6J919LjA4axG1Yd0L8+nWMcYKJW4iuaxd1m3D+3Rhd1U163dWwulfgOpKmDu1tcMSkUZqKHGLN3CtQ6YDyRVDigq1e4JIbmuXddvhCQobd0GvETD0ApjzS6iuauXIRKQxGkrc5pjZUfv2JdZPm5e9kNq2IcWdWKnN5kVyWbus247vHSRuSzYmeok/8EXYswkWPtmKUYlIYzU0q/QrBIvgfoojlVkZwe4GV2Q7sLZqSHEhj88rZ3flQTrHY60djog0Xrus27p2iNGvazyYoAAw9HwoPgFm3x+MebPQbdMqEkr1tri5+yZ3P4NgyvzqxOu77v4Bd9/YMuG1PbUTFFap1U0kJ7Xnum147cxSCBK10z8PmxbA6pdbNzARSVs6e5W+6O4/S7z+1hJBtWWHlwTRBAWRnNacus3MJpjZUjNbbma3p7g+wsxmm1mVmf1bnWtfNbNFZrbQzB41s4bG3GXU8D5dWFGxh4M1h4ITYz4BHYtgtpYGEckVWv6/kQb27EjE0AQFkXbKzKLA/cAlwEjgajMbWee2bcCtwD11yvZPnC9L7JMaBSZlPeiEEX06c7DGj/zwjHWAU6+Hd5+BLdp4XiQXKHFrpIK8KIN6Fh4Z4Csi7c14YLm7r3T3A8A0YGLyDe6+2d3nAAdTlM8DOphZHtARWJ/tgGvV7lm6ZOOuIydPvQGi+TD7f1sqDBFpBiVuTTBuUHfmrN6Gu7d2KCLS8voDa5OOyxPnjsnd1xG0wq0BNgA73f0vde8zs5vMbK6Zza2oqMhAyIEhxYVEI3ZknBtAp15w8jXw5m9h85KMfZeIZIcStyYYX9qD7fsOsmyzuktF2qFU0y/T+hVnZt0JWudKgX5AoZl9+qiHuU9x9zJ3LysuLm5WsMkK8qIMKSp8f+IGcN4dkN8JnvsG6AepSJumxK0JTivtAcDrq7a1ciQi0grKgQFJxyWk3935IWCVu1e4+0HgSeCMDMfXoOF9OrN0U53ErbAIzr0dVvwN3n2uJcMRkUZS4tYEA3t0pE+XOG8ocRNpj+YAw8ys1MzyCSYXzEiz7BrgdDPraGYGXAC8k6U4UxrVryvl2/dTsbvOjgnjb4Sew+C5O6D6QEuGJCKNoMStCcyM8aU9eGPVVo1zE2ln3L0auAV4jiDpeszdF5nZzWZ2M4CZ9TGzcuBrwH+YWbmZdXH314HHgTeBBQR18JSWjP/M43oC8OryLe+/EI3BhB/CthXwxi9aMiQRaQQlbk00vrQHm3ZV8d7Wfa0dioi0MHef6e7Hu/tQd/9B4txkd5+c+LzR3UvcvYu7d0t83pW49m13H+Huo939Gndv0c1CR/frSveOMWa9m2LSw7AL4bgL4aW7YU/mJkWISOYocWui04cE49zUXSoiuSQSMc4aVsysZVtS9xhc/N9wcB/87XstH5yIHJMStyYaWtyJHoX5mqAgIjnnnGFFbNlTxTsbUqxHWXw8jL8pWB5k/T9bPjgRaZAStyYyM8YP7sEbq7e2digiIo1y9rBgiZGXl9XTHfrB26BzH/jDdbB/RwtGJiLHosStGcaX9mDttv2s37G/tUMREUlbn65xhvfuzKz6ErcO3eDjD8HOtTD9c3DoUMsGKCL1UuLWDKdpnJuI5Khzji9izqrt7D9Qk/qGgafBxT+Ed5+Fl3/UssGJSL2UuDXDiD5d6BzP0zg3Eck5Zw8r5kDNIV5b1cBwj/E3wpir4MUfwLK/tlxwIlKvrCZuZjbBzJaa2XIzuz3F9YlmNt/M3krsy3dW4vwAM3vRzN4xs0Vm9uWkMt8xs3WJMm+Z2aXZ/BsaEo0Ypw4O1nMTEckl40t7UJAX4eV3t9R/kxl8+F7oPQqeuB62r26x+EQktawlbmYWBe4HLgFGAleb2cg6t70AnOTuY4HPAg8mzlcDX3f3E4DTgS/WKfsTdx+beM3M1t+QjtNKe7CiYu/Rq5CLiLRh8ViU8aU96h/nViu/I1z1O8Bh2qdhw9stEp+IpJbNFrfxwHJ3X+nuB4BpBJsrH+bue/zIQkKFJDZqdvcN7v5m4vNugtXJ+2cx1iYbn9i3dM5qdZeKSG754PHFLN+859gTrHoMgY9NDVrcfnEO/HYiLH9BG9KLtIJsJm79gbVJx+WkSL7M7AozWwL8maDVre71wcDJwOtJp29JdLFONbPuqb7czG5KdL/OrajI3grgo/t3pWN+VBMURCTnHHNZkGTDPgRfWwQf+i5sXgL/9y8w+Wx49y9ZjlJEkmUzcbMU5476eebu0919BHA58L6lus2sE/AE8JXa7WKAnwNDgbHABiDldCd3n+LuZe5eVlxc3PS/4hhi0QjjBnXXBAURyTnH9+5Eny5xZi1rYJxbsnhXOOsr8JX5MPF+qK6ERyfBypeyG6iIHJbNxK0cGJB0XAKsr+9md58FDDWzIgAzixEkbQ+7+5NJ921y9xp3PwT8kqBLtlWNH9yDJRt3sXPfwdYORUQkbWbG2cOKeGXZFmoONaLbM68ATv403Pg3KDoeHrsGtizPXqAiclg2E7c5wDAzKzWzfGASMCP5BjM7zsws8fkUIB/Ymjj3K+Add/9xnTJ9kw6vABZm8W9Iy/jSHrjT8LR6EZE26Ozji9m5/yAL1u1sfOF4F/jkNIjkwSOfgP3bMx+giLxP1hI3d68GbgGeI5hc8Ji7LzKzm83s5sRtHwMWmtlbBDNQr0pMVjgTuAY4P8WyH3eb2QIzmw+cB3w1W39Duk4e2J3O8TyeX7yptUMREWmUs44rwgxmvdvEscDdB8NVDwe7LDx2LdSo50Ekm/Ky+fDEUh0z65ybnPT5LuCuFOVeIfUYOdz9mgyH2Wz5eREuPKE3zy/exMGaQ8SiWtdYRHJDj8J8xpR04y+LN3LrBcOa9pBBH4CP/BSe+jw88+9w2Y+DNeBEJOOymri1JxNG9+HJf67jtZVbD8/UEhHJBZeP7cd3/7iYJRt3MaJPl6Y9ZOwnoWIpvHovrPw75MUhGoNILJjUcO43YMCpGY1bpD1S01CGnHN8MR3zo8xcsLG1QxERaZSJY/sTixqPzy1v3oMu+Dacewf0PSlY+61z3yBp2/wO/HoCvPpTbVgv0kxqccuQeCzK+SN68fzijXz/8tFEI+omEJHc0KMwnwtG9Oapt9Zx2yUjmj7cIxKBc287+vz+HTDjS/D8t2DVy3DFZCgsal7QIu2UWtwy6JLRfdmy54B2URCRnPPxshK27DnAi0s2Z/7hHbrBJ34Ll94Dq2bB5LNg/h+C3RdW/A1WvBisBbdPdafIsajFLYPOHV5MQV6EZxdu5PQhPVs7HBGRtH3w+GKKOhXwh3nlXDSqT+a/wAzG3wgDToPHr4Mnbzj6nng3uOj7wRpxmtwgkpIStwwqLMjjg8cX8+zCjXzrwyOJqLtURHJEXjTCv5zSn6mvrGLLniqKOhVk54v6joGbX4GNC8FrEvudOhzcB7N+BDNugfm/hw/fC0XHZScGkRymrtIMu+TEPmzcVclb5TtaOxQRkUb5+LgSqg85T/1zXXa/KNYhmGE68PRgKZFBZ8BxH4J//XOwrMjG+fDzM+Clu7Wor0gdStwy7PwRvYlFjWcXanapiOSWYb07c9KAbjw+r5xgLfQWFonAuH+FL86BEz4ML/4A7h4Kv/kw/ON/YeuKlo9JpI1R4pZhXTvEOPO4Ip5ZuKF1Kj4RkWa4clwJSzbuZtH6Xa0XROfecOVUuPHFYFP7fdvgL9+En50C/3tqMDt1zetwqKb1YswGd3jlXiif19qRSBumMW5ZcMnoPtz2xAIWrd/F6P5dWzscEckwM5sA/BSIAg+6+511ro8Afg2cAnzT3e9JnB8O/D7p1iHAt9z93hYJPA0fHdOP7/1pMX+Yu7b166/+pwSvC74F29+Dd5+FpTNh9v3BmnAdi+D4CVA0DKp2QeXOYOmRql0QzYf8TlDQKXjv2AMGnB48Lxpr3b+rPm/+Fv767eDv+vyr0DkLk0Qk5ylxy4ILR/bhjukLeWbhhtav+EQko8wsSrC38oVAOTDHzGa4++Kk27YBtwKXJ5d196XA2KTnrAOmt0Tc6eraMcbFo/rw9NvrueOyEyjIi7Z2SIHug+C0zwWvyp2w7HlY+gy880eo2gkWDRb7jXeFeBeoqYYDu6FqN1TtgUOJPVTzO8GgM2HIB2HIudBrZNuYwbpjDTz3Teh3crADxRM3wGeehkgb+feXNkOJWxb0KMzntNIePLNwI/920XCsLVQKIpIp44Hl7r4SwMymAROBw4mbu28GNpvZZQ085wJghbu/l81gm+LKcSX88e31zFywgStOLmntcI4W7wonXhm8aqqhen+QkDVU1+7dCqtfhlUvBWvJLXsuON+5Hxx3AQy7MEjk4l2DLtiqXUHSd3A/dOodrEWXLe7w9C2Aw8cfgvdeDfZ9fekuOO+O7H2v5CQlblkycWw/bntiAc8t2siE0X1bOxwRyZz+wNqk43LgtCY8ZxLwaKoLZnYTcBPAwIEDm/Do5jnruCJG9u3CD2cu4fwRvenaoY12LQJE8yDa+dj3FfaEUZcHL4Cd5cHCv8ufh8Uz4J+/C1rtYh3gwJ6jy8e7QrdBQctf576QV5DYj7UA8vKD7s2eQ6HH0GBXiMb8YJ87NUgoP/yT4PndBwU7TLx0dzDjdsi56T9LQk+JW5Z87JQSfv3qav7rj4sT+5jqn1okJFL9P3KjZiKZWT7wUeAbqa67+xRgCkBZWVmLz3KKRow7P3Yil9//Knc/u4QfXHFiS4eQfV1L4JRrglfNQSifE+zicGBf0NVa0BkKugTJ2e4NsOO9YJxdxVJYOQtqqqC6ipT/0xd0CfZq7VoSJHld+gYtez1KoeTU93d/bl8Nf/nPIDkbd92R85fdA+vmwRM3Buvede6d3X+PWpuXBOPsInnQe1TQldx7dBC7um3bBGUTWZIXjfD9y0dz5eTZ3PfCcm6/ZERrhyQimVEODEg6LgHWN/IZlwBvuvumjEWVYWNKunHdmaX86pVVXH5yf04d3KO1Q8qeaCxo2Rp0RuPKuQfdqtWVsGcTbFsZLFmybUXwvnVF0D1bufNImcJeQavf6CuhpCzoIrUIfPR/399Kl18IH/8N/PL8YJeJD98bJIPZGnrjDm9MCWbsxjpAYXEwEcQPBdc7dIerHobBZ2bn+yVtStyyqGxwD64cV8KDL6/kynH9Oa5XGs35ItLWzQGGmVkpweSCScAnG/mMq6mnm7Qt+dqFx/Pswo1848kF/PnWs9rORIW2wizRVZuYvdpzaDBWrq4De2HXBti0ABZND2aPvjEFOvaEfVvhoz+DbgOOLtd7ZNDy9vQXg6VQOvSA/uOCVrs+o4Oxd4XF0KlXkGw11a4N8PQXghbHYRcFSWTn3sH4voolsGkxvHovPPxxuGY6DGzKyIBjfH80P+jOlmOy9rDWWFlZmc+dO7dVvnvLnirOv+fvjOrXlUduPE0TFURaiJnNc/eyLD37UuBeguVAprr7D8zsZgB3n2xmfYC5QBfgELAHGOnuu8ysI8EYuSHuvjP1NxzRmvUXwItLN3Pdr+fw1Q8dz5c/NKzV4giVyl3BjNiFTwRJ10d/1nBLWsVSWDMbyucGr4olHNVFm98ZOhUHY+0Ki4MkqLBXsMXYgNOOXlrkUA1sWgSrX4FZd8PBSrj4+1B2fepYdm+EX18KezbDZ54KWguba/t7wXe/9WiQuJ3xJTjzy0ES3M41VH8pcWsBv3vtPf7zqYXcd/XJfPSkfq0Wh0h7ks3ErSW1dv0FcOuj/+TZhRuZ+eWz1HPQFlTugq3LYE8F7N0cJFN7KxKvLcFrX+LdE4sUdxsUJHBdS2D9m0ECWDsJo38ZXDE5WA+vITvXwW8uhX3b4dqng6VLmmLnOnj5Hnjzd0E3cdl1QewLnwhaEc//Dxj7qSNj6g7sDcYC1hxo+nfmGCVurVzx1RxyrnjgVTburOSFr3+QzvE2PENLJCSUuGVOxe4qPvTjlxjeuzPTbjqdSEQ9Bzmh+kCw7+va12HNa8H73groNSro7hxwevDedUD6Y+d2rA2St8pd8MnHgq7baJ1RV7WteeVvwPq3oHJHkHxV7Qnety4Pxs6d8hk4++vQtX9Qbu0ceO6OoFzxiKBrePuqYHJIrTFXwaX3BBNIQkyJWxuo+N5eu4PLH3iV684o5VsfGdmqsYi0B0rcMusPc9fy/x6fz63nH8fXLhre2uFIU7gHrVZ5Bc17zvbV8OvLYFd5MPu0S3/oNjB47SwPZsPWtubVdt3mFx7ZxaLbQDj988F7qhgXPxXsjJEXDyZk9CgN3iveDbpWuw2CK38VJI21Dh2C914JxhAWHQ8nX5PTXa4N1V+anNBCThrQjU+fNohf/2MVl43py7hB3Vs7JBGRtF05roQ5q7dx39+WM7JfF61PmYvMmp+0AXQfDDf+LdiCbMeaYKmUHWuCyQ2FxXDS1UG37IBTgySrMWO7zWDUFcErlSHnBrtK/OoiuODbwSLMbz8adLtuXxUke9WV8PcfBuP1Tvtc07YO274a3psdTAQpOq7x5bNILW4taE9VNRf/ZBYFsQgzbz2beEwztESyRS1umVd5sIZJU17j3U27mf6FMxneR+PdpBXs3w4zvhRsd1Zr0FlB1+sJH4HNi4MWu3f+GCz1csJHIK9DsDVaZeJlkWCdut4nBjN0e48KkrUlM4NlUDYtPPLskvFw0iQY/S/BsigtQF2lbajie2XZFj79q9f53AeH8I1LTmjtcERCS4lbdmzaVcmHf/YKHfOjPP3FM+nWMb+1Q5L2yB3m/x62rYIxnwiWYqlr20qY/QAsejLY4eLwXrZdg1a5TQuD5ViSWSQY+zfismDNulUvBy16mxcHM1+PvxiGXRxsk9YlabLhoUPB2LxFTwUtjwNPh3O/ESy+3AStlriZ2QTgpwRT5h909zvrXJ8IfI9gunw18BV3f6WhsmbWA/g9MBhYDXzC3bc3FEdbq/i+8eQCfj9nDU98/gxOHqguU5FsUOKWPfPe286kKbM5fUhPfnPdeKKarCC5yD1Y5mTTwuBV2CtIzAqLjr5v4/xg2ZLFTx2ZLNFrVJDAVVfBOzOC89ECGDA+mAwSyYMPfCFY4iTetVGhtUriZmZR4F3gQoKVxucAV7v74qR7OgF73d3NbAzwmLuPaKismd0NbHP3O83sdqC7u9/WUCxtreLbXXmQi38yi44FefzpS2epy1QkC5S4Zde0N9Zw+5ML+OyZpfznh0/QGpXSPrgHrW/L/xq83psdLFty3Idg5OVB4hfvErQE/u37sPDxYHbsOf8PTr0+7TGGrTU5YTyw3N1XJoKYBkwEDidu7p68k28hR1YUbKjsRODcxH0PAX8HGkzc2prO8Rh3fmwMn5n6Bj99YRm3TdB2WCKSWyaNH8iSjbuZ+uoqqqpr+K+Jo9XyJuFnlhgbNypoSTuwFzDI7/j++3qUBjNfz7gFnv82PPeNoPu0/ynNDiGbiVt/gtXBa5UDR+2TYWZXAD8EegGXpVG2t7tvAHD3DWbWK8Nxt4hzji9m0qkD+MVLKzj3+GJOG6KtPkQkt3z7IyPpkB/l539fwY59B/nxVSdpWyxpX/ILG77e72T4zNOwcUGwi0UGRDLylNRS/fQ6ql/W3ae7+wjgcoLxbmmXbfDLzW4ys7lmNreioqIxRVvMHZedwKCehXxm6hs8s2DDsQuIiLQhZsZtE0bwH5edwJ8XbOCzv5nDnqrq1g5LpG0xy1jSBtlN3MqB5F1zS4D19d3s7rOAoWZWdIyym8ysL0DifXM9z5vi7mXuXlZcXNz0vyKLusRjPH7zBxjVrwtfeORNpsxaQXuY5Ssi4XLD2UP40cdP4rWV27h6ymts2VPV2iGJhFY2E7c5wDAzKzWzfGASMCP5BjM7zhIjWs3sFCAf2HqMsjOAaxOfrwWezuLfkHU9OxXwyI2nc+novvz3zCV886mFVNccau2wREQa5WPjSvjlZ8axbPNuJtz7Mn9dvKm1QxIJpawlbu5eDdwCPAe8QzBjdJGZ3WxmNydu+xiw0MzeAu4HrvJAyrKJMncCF5rZMoJZp+9bYiQXxWNRfnb1ydz8waE88voarn9oLjv3H2ztsEREGuX8Eb156otnUty5gBt+O5fbHp/P7krVZSKZpAV425hH31jDfz61kP7dOzD50+M4oW+4N9IVyRYtB9J6qqpr+OlflzH5pRX069aBH338JE3AEmmEhuqvbHaVShNcPX4g0246nf0HarjigVeZ/s/y1g5JRKRRCvKi/PuEEfzh5g8QjRiTfvkatz0+X2PfRDJAiVsbVDa4B3+69SzGlHTjq79/m289vZAD1Rr3JiK5ZdygHjzz5bO58ewhPPFmOefd83d+9coqDmocr0iTKXFro3p1jvPwDadx49ml/Hb2e3ziF7Mp376vtcMSEWmUjvl53HHpCTz7lXM4ZWB3vvenxVzy05d56d0KzaIXaQIlbm1YLBrhm5eN5IFPncLyzXu47L5X+Muija0dlohIox3XqxO/ue5UfnVtGQdrDnHt1Df42M//wYtLNiuBE2kEJW454NIT+/KnL53FgB4duOl38/ivPy5W16mI5Bwz44ITevOXr57D9y8fzaZdVVz3mzl89H9f5blFGzl0SAmcyLFoVmkOqaqu4b///A4PzX6Pk0q6ct/VJzOo5zG22xBppzSrtO07WHOI6W+u4/6/L+e9rfvo2zXO+SN6ccEJvThjaBHxmLbPkvapofpLiVsOembBBv79ifkcrDnE1y48ns+eWUpeVI2nIsmUuOWO6ppD/HnBBp5ZsJGXl1Ww90ANBXkRzjyuiEtP7MvFo3rTOR5r7TBFWowStxBWfBt27uc/n1rIX9/ZzOj+XbjzX8Ywun/X1g5LpM1Q4pabqqpreGPVNv62ZDPPL95E+fb95OdFOH94Lz46th/nj+illjgJPSVuIa343J2ZCzby7RmL2L7vAJ89czBXnTqAocWdSOwkJtJuKXHLfe7OP9fuYMZb6/nT/A1s2VNF53gek04dwLVnDKake8fWDlEkK5S4hbzi27nvID985h2mzVkLQFGnfE4b0pPTh/Tk7OOKGFykcXDS/mQzcTOzCcBPgSjwoLvfWef6CODXwCnAN939nqRr3YAHgdGAA59199n1fVfY6690Vdcc4rWV23h0zhqeXbgRd2fC6D5cf1Yppwzsrh+rEipK3NpJxbdm6z5mr9zCayu3MXvFVjbuqgTg9CE9+PTpg7hoZB/y8zQWTtqHbCVuZhYF3iXYK7kcmANc7e6Lk+7pBQwCLge210ncHgJedvcHzSwf6OjuO+r7vvZSfzXGuh37+e3s1Tz6+hp2aPDLlwAAFbNJREFUVVZT1KmAnoX5dO0Yo3vHGD0K8zl5YHc+dEJvehTmt3a4Io2mxK0dVnzuzppt+/jzgg08+sYa1m7bT1GnfD5RNoBPnT6I/t06tHaIIlmVxcTtA8B33P3ixPE3ANz9hynu/Q6wpzZxM7MuwNvAEE+z8m2P9Ve69h2o5sk317GgfCc79h9g+76D/P/27jw4zrs84Pj32fvWtbpsWZYtOUpichBCbshBA4EWmE4PEqAcpQ2kCQWGKZAyZegxQ2mnHWC4Gs4pVzpDCYVwJEwgFzmJcxA7PmT5lnWfu1qt9nj6x/takRTZlh1Jq1d+PjPvvPv+9t3Vsyv58e99f9fo5DQDE3lGJgv4BF7dVssbtjZxdWc9qUiQoF/w+4Sg30fI78Pnszt1ZvU5Uf4KrHQwZmWICBvr4vzNNR184LXtPLBngO89dpCvPrCXOx7s5o9fuZ5brmlnc32i0qEa4zXrgUOzjg8Dly7ytZuBAeBbInIB8BTwIVXNLm2IZ4ZYKMA7L9v4knJVZXvPOPds7+Xe7X3809074O6Xvr4hGeZ9V23iHZdtJBG2/w6NN9hf6hnA5xOu7Wzg2s4GDo9M8vWH9vGDJw7yw22HedN5zdx6TQfnrktVOkxjvGKhWzSLbboI4PR7+6CqPi4inwc+AfzDnB8gcjNwM0Bra+vLCPXMJCK8Yn0Vr1hfxUdf38m+wSxP7hsmXypTLJUplZVCSflt1yCf+cVOvvSbLt5zRRvvuXKTNa2aVc8qbmeYlpoYn37LVm67roNvPLyP7zx6gJ89d5RYyE86ESadCJFOhFlXHeWK9jqu6Ejblagxcx0GNsw6bgF6TuG1h1X1cff4hzgVtzlU9Q7gDnCaSk8/VAOwKR1n0wKDtG65pp1nD43y5fu7+MKvu/jaQ/t4w9ZGrtpSz2u2pGlMRSoQrTEnZv8jn6HSiTAfv+FsPnB1Oz9++ggHhycZzOQZzOQ5MDTJw12DfPuR/QR8wkUba7j6rHrOaU6iCmWFsiqqcF5LlfWXM2eaJ4EtIrIJOALcCLx9MS9U1V4ROSQinaq6C3gdsONkrzPL54IN1fzXX1xMV/8EX3twH/ft7OPHzzj18C0NCa7sSNNUFaE6GqTq2BYLUhcPUxMPEg7YnHJmZVnF7QxXFQ3y7ivaXlI+XSyz7eAID+we4MHdA/z7PbuO+x4Xb6zhzRes403nNVOfDC9jtMZUnqoWReQ24B6c6UC+qarbReQD7vNfFZEm4HdACiiLyIeBc1V1HPgg8D13RGk38N6KfBAzR0dDks/+6fmUy8rO3gke7hrgoT2D3PnkQaYKx18bOhEOUBMPsr46ypaGJFsaEzP7dMLyoVl6NqrULMrARJ4jozl8Aj4RRKBUVh7aM8hPn+1hZ+8EPoErO9K87dUbbOoRU3E2Aa9ZCqrK5HSJsVyB0cmCu59meHKakew0Q9lphrPTHByepKsvw0S+OPPadCLMuetSnNucmtlvSsfx20hWcxI2qtS8bPXJ8IJ3085vqebWazvY3TfBT57p4a6nj3Db95+emXrkpkta2VD74uzmhVKZ8VyBeDhgy9YYY1Y9ESEeDhAPB1h3km4hqkrv+BR7+jLs7pvghaMT7Dg6zte7uimWnZsk0aCfs5uTM5W5tro4iXCARCTg7MMBYiG/TShsjsvuuJklVSorD+4Z4PuPH+S+F/pQoKM+MXPFmnGvRkVgXVWUzfXxmY7Dx5oXGpJhS1rmZbM7bma1yBdLdPVn2NEzzo6j4zP7ianigucnIwE21sXYWBenrS5GS02McMCH3+fMQecXobk6ygUtVZYr1yi742ZWjH/W1CNHx3Lc+cQhtveMk4oGqI6GqI4FSUUCjOWK7BvMsG8wy13bjsxpXkhFAmxpTLKlIUFHQ4L2emdbXxO1JgZjjOeEA362rqti67qqmTJV5dBwjp6xHNl8kcyxbapIz2iO/UOTbD8yxi+f76VUXvgGy4UbqrnlmnauP6fRJhI+g1jFzSyb5qooH7n+rJOep6oMZPJ09WfY05dhT/8Eu/sy3Lujb2b9VYBQwEdTKkIyEnC3IMlIgJbqKJvq42xKJ9iUjlMVDS7nxzLGmJdNRGiti9FaFzvheYVSmf6JPIVimWJZKatSKiu/2z/MHQ918/7vPEV7fZz3X91Oe32Crv4JN49mODQySWdjkqu2pHlNR/1Jf5bxBqu4mYoTERqSERqSEa5oT895bjg7TfdAhr0DGbr6M/RP5JmYcq5KDw1PMp4r0Ds+xewL0tp4iLa6GG3pOJvq4rSl49TFQ0yXyuSLZaaLZQqlMhduqLaVI4wxq1rQ71twyqVzmlPcdEkrP3++l6/cv5eP/fC5mefCAR/t9Qk2pxM8fXCUXzzfC0BrbYyL22pIJ8JURYOk3OlNmqsidNQnqLHJhz1hWStuInID8HmcIfNfV9V/nff8O4CPu4cZ4BZVfVZEOoH/mXXqZuBTqvo5d+2/v8ZZNgbg71X158v4MUwF1cZD1MZrubit9rjn5IslDg3n2DeYnWl+3TeY5dG9Q/xo25ETvv/rzm7gr16zmcs21870FVFVuvoz/GZXPz2jU5zfUsWrNtbQWhuz/iTGmFUj4PfxlgvW8ebzm3m0e4jcdIktDck53UpUlb0DWX7bNchDewZ5pGuI0dz0glOcpBMh2uudLiqb0nFaamK01sbYUBslGbGWjNVi2QYniIgf2A1cjzNb+JPATaq6Y9Y5VwAvqOqIiLwRZ+HmSxd4nyPApap6YP6izYthnXvPXLnpEgeGs4xOFggFnEWlw+40JXc/d5TvPnaAoew0W9el+LNXtdA1kOE3Owc4MpoDIBL0zSS4dCLERa01tDckqI05/fVqYiFq4iFaaqI2qGKVscEJxhzfVKHEeK7AaK7AkdEcXX1Oq8ae/gm6+jOMzxs4URsPsaUhwdlNSc5qSnJ2U3JmlK26k7KDMwWKzRjw8lVqcMIlQJeqdrtB3Am8lVmzhKvqI7POfwxn6Zj5XgfsVdUDyxirWaOiIT9nNy28DutHrk9yyzXOyhFff3gfn/7pDmIhP1d2pLn12g6u7qynKRWhqz/DUwdG3G2YX+/snxnaP+dnBf1srIvRVhenpSZKKhqcGeafDAdoqopwTnPKkpoxpuIiQT+RoJ+GVISzGpNc29kw85yqMpYrcGg4x8HhSQ6NTLJ/MMuuvgl++NRhstOl476v3ydsSsfpbEpyTlOS9voEU8USw9kCI1ln/juAC1uquWhjNZvTCRtYcYqWs+K2Hjg06/gwcOlxzgV4H/CLBcpvBH4wr+w2EXkXzszkH1XVkZcTqDlzRYJ+bryklT+/eAPdgxk21MZesoRNZ1OSzqYkb7/UWexbVZnIFxnNFhiZdCbfPDwyyf6hSQ4MZekayHD/7v4FmyKCfuGc5hQXtFRzfksVkaB/ZiTZRL5IsVTmotYaLm+vIz5vjVhV5cDQJI91D+HzCZ2NSc5qTBINWUXQGLN0RITqWIjqWIjzWqrmPFcuK0dGc+zqnaB/Io9PnOmdBEFRjozkeKF3gt8fHuNnzx2d81qfQE0sRKFU5vuPHwScWQRe2VrDeeuraG+IO33z6hO2RvYJLOc3s1AVesF2WRG5FqfidtW88hDwFuD2WcVfAf7Zfa9/Bv4D+MsF3vNm4GaA1tbWU4/enFF8PqGjIbmoc0WEVCRIKhI84SitQqlMNl9kYsrZDg5neebQGM8eGuWup4/wncfm3kQWAb8IxbIS9Auv2ljDa8+qpzEZ4dHuIR7dOzTThDv7Na21MTobkzNTp3Q0JNhcH7c+KcaYJefzCRtqY3MmVj+eTL7I/sEs8XCA2liIZCSAzyeUy0r3YJZtB0fYdmCEbQdHeLhrcM60J02pCBtqo6yrfnFrqY66fe+iBPyLW5mnXFamS+U11dKxnH3cLsfps/YG9/h2AFX9zLzzzgfuAt6oqrvnPfdW4FZVff1xfkYbcLeqvuJEsVgfEbPalMvKvqEs5bI6TamRILGgn0K5zFP7R3hgzwAP7BpgZ+8EANWxIJdvruOKjjSXb67D7xN29U44W984u3onODA0OacJtzoWxDevz11tPMTWdSl3q2LruhTVsYVHkuWLJY6OTnFkNMd0qcyGmijrq2OeucNnfdyM8Y7pYpmDw1m6+rPsHcjQPZDlyOgkR0ZzHB2dmpPbgn6htTbGpnSCmliQXKHEVKFErlAiN10i414wZ6aKZKaLqMK6qghnN6fodPvndTQkaEpFqImFVmVT7Yny13JW3AI4gxNehzO44Eng7aq6fdY5rcCvgXfN6+927Pk7gXtU9VuzyppV9aj7+CM4gxZuPFEslviMV/WNTzGcnaazMXnS5FIolTk4PMne/gx7B7L0zLs7pyi9Y3l29IzRMzY1Ux7y+0hEAsTDfhLhICG/cHRsiv6J/II/J50Isb4mRmMy7I76dbZ0IkxbOk77KrnbZxU3Y9aGUlkZzOQ5NDxJtztrwL6BLN2DGSamikTd/nrRkJ9o0E887J+Z5zMZDhDw++geyLCzd4K9AxkKpbmVwIZkhMZUmIZkhHTSyWX1yTB18TDhoI+gz0fALwT9QjQYYH11lFQ0sKyD0SoyOEFViyJyG3APznQg31TV7SLyAff5rwKfAuqAL7tfQPFYoCISwxmR+v55b/1vInIhTlPp/gWeN2bNaExFaExFFnVu0O+bWWXiZIaz0+zoGeeFo+MMT047V6buVep0qUxnU5L11THWVUdYXxMl5PdxeCTH4ZFJDo/kODQyyYGhSbYdHGVkcvolM7s3psJ0NCTYUBOb6QTtJFffgqtfBN3RvqGAj3DAj6L0jefpHcvRMzZF79gUjakwX37Hqxb3xRlj1gy/T2Zy4YmmhlqM6WKZ7sEM+way9I1P0TeRp29sit7xKfYOZHh8X56RycJJ3ycRDtBSE2V9dZSGVISqaJDqmDMvXlU0SMAnM33Djt0fu7Kjbkkuam2tUmPMy1IuKxNTRQYyU+wdcJo59vY7gzR6RnNMFUrkC2WmSy8drHEykaCP5qoozVURtq5L8ck/PHfRr7U7bsaY01EolRnKTDOYyZMvlimVlWKpTKGsZPPOkmTOhaxzMTuYyTOWK8y5k7eQez/yWs5qXHRfalur1BizPHw+oSoWpCoWPOEAj1JZmSqUKM26WBScW+fFkpIvvljBK6vS5F7F2tx4xpiVFPT7aKqK0FS1uNYOcEb9T06XGMsVGMsVZlohjqUvwemXtxSs4maMWRF+n7xkihNjjFkLRJz8Fg8HZiYmXi6LG09rjDHGGGMqzipuxhhjjDEeYRU3Y4wxxhiPsIqbMcYYY4xHWMXNGGOMMcYjrOJmjDHGGOMRVnEzxhhjjPEIq7gZY4wxxniEVdyMMcYYYzzCKm7GGGOMMR5xRiwyLyIDwIFFnp4GBpcxnOXm5fgt9spYq7FvVNX6lQxmOZxi/oK1+/tc7Sz2ylirsR83f50RFbdTISK/U9WLKx3H6fJy/BZ7ZVjsa4uXvxOLvTIs9so43ditqdQYY4wxxiOs4maMMcYY4xFWcXupOyodwMvk5fgt9sqw2NcWL38nFntlWOyVcVqxWx83Y4wxxhiPsDtuxhhjjDEeYRW3WUTkBhHZJSJdIvKJSsdzIiLyTRHpF5HnZ5XVisivRGSPu6+pZIzHIyIbROQ3IvKCiGwXkQ+55as+fhGJiMgTIvKsG/s/uuWrPvZjRMQvIk+LyN3usSdiF5H9IvJ7EXlGRH7nlnki9pXgpfwF3s1hlr8qy6v5C5Yuh1nFzSUifuBLwBuBc4GbROTcykZ1Qt8GbphX9gngPlXdAtznHq9GReCjqnoOcBlwq/tdeyH+PHCdql4AXAjcICKX4Y3Yj/kQ8MKsYy/Ffq2qXjhrCL2XYl82Hsxf4N0cZvmrsrycv2Apcpiq2ub087scuGfW8e3A7ZWO6yQxtwHPzzreBTS7j5uBXZWOcZGf4/+A670WPxADtgGXeiV2oMVNDtcBd3vp7wbYD6TnlXki9hX4bjyXv9w4PZ/DLH+taMyezV9ufEuSw+yO24vWA4dmHR92y7ykUVWPArj7hgrHc1Ii0ga8Engcj8Tv3qp/BugHfqWqnokd+BzwMaA8q8wrsStwr4g8JSI3u2VeiX25rYX8BR77fVr+WnFezl+wRDkssIwBeo0sUGZDbpeRiCSA/wU+rKrjIgv9ClYfVS0BF4pINXCXiLyi0jEthoj8EdCvqk+JyDWVjuc0XKmqPSLSAPxKRHZWOqBVxPLXCrP8tbLWQP6CJcphdsftRYeBDbOOW4CeCsVyuvpEpBnA3fdXOJ7jEpEgTtL7nqr+yC32TPwAqjoK3I/TT8cLsV8JvEVE9gN3AteJyHfxRuyoao+77wfuAi7BI7GvgLWQv8Ajv0/LXxXh6fwFS5fDrOL2oieBLSKySURCwI3ATyoc06n6CfBu9/G7cfperDriXJp+A3hBVf9z1lOrPn4RqXevVBGRKPAHwE48ELuq3q6qLarahvP3/WtVfSceiF1E4iKSPPYYeD3wPB6IfYWshfwFHvh9Wv6qDC/nL1jiHFbpznqraQPeBOwG9gKfrHQ8J4n1B8BRoIBztf0+oA6n4+Yed19b6TiPE/tVOM04zwHPuNubvBA/cD7wtBv788Cn3PJVH/u8z3ENL3buXfWxA5uBZ91t+7F/n16IfQW/I8/kLzdeT+Ywy1+V37yWv9w4lyyH2coJxhhjjDEeYU2lxhhjjDEeYRU3Y4wxxhiPsIqbMcYYY4xHWMXNGGOMMcYjrOJmjDHGGOMRVnEzniAiJRF5Zta2ZAsJi0ibiDy/VO9njDGzWf4yS8mWvDJekVPVCysdhDHGnAbLX2bJ2B0342kisl9EPisiT7hbh1u+UUTuE5Hn3H2rW94oIneJyLPudoX7Vn4R+ZqIbBeRe91ZxY0xZtlY/jKnwypuxiui85oa3jbruXFVvQT4IvA5t+yLwH+r6vnA94AvuOVfAB5Q1QuAi3BmsAbYAnxJVbcCo8CfLPPnMcacOSx/mSVjKycYTxCRjKomFijfD1ynqt3uws+9qlonIoNAs6oW3PKjqpoWkQGgRVXzs96jDfiVqm5xjz8OBFX1X5b/kxlj1jrLX2Yp2R03sxbocR4f75yF5Gc9LmH9P40xK8PylzklVnEza8HbZu0fdR8/AtzoPn4H8LD7+D7gFgAR8YtIaqWCNMaYBVj+MqfEauXGK6Ii8sys41+q6rEh9WEReRznQuQmt+xvgW+KyN8BA8B73fIPAXeIyPtwrkxvAY4ue/TGmDOZ5S+zZKyPm/E0t4/Ixao6WOlYjDHmVFj+MqfDmkqNMcYYYzzC7rgZY4wxxniE3XEzxhhjjPEIq7gZY4wxxniEVdyMMcYYYzzCKm7GGGOMMR5hFTdjjDHGGI+wipsxxhhjjEf8PwzeewYpRzPXAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 720x360 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, axs = plt.subplots(ncols=2, figsize=(10,5))\n", "ax = axs[0]\n", "ax.plot(embedder._history['loss'])\n", "ax.set_ylabel('Cross Entropy')\n", "ax.set_xlabel('Epoch')\n", "\n", "ax = axs[1]\n", "ax.plot(embedder._history['reconstruction_loss'], label='train')\n", "ax.plot(embedder._history['val_reconstruction_loss'], label='valid')\n", "ax.legend()\n", "ax.set_ylabel('Cross Entropy')\n", "ax.set_xlabel('Epoch')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.1"}}, "nbformat": 4, "nbformat_minor": 2}