{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Custom embedder for parametric UMAP. \n", "This notebook shows you how to run a UMAP projection with a custom embedder. "]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2021-04-16T07:27:12.938019Z", "start_time": "2021-04-16T07:27:12.933922Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["env: CUDA_DEVICE_ORDER=PCI_BUS_ID\n", "env: CUDA_VISIBLE_DEVICES=1\n"]}], "source": ["%env CUDA_DEVICE_ORDER=PCI_BUS_ID\n", "%env CUDA_VISIBLE_DEVICES=1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### load data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2021-04-16T07:27:14.606390Z", "start_time": "2021-04-16T07:27:12.939606Z"}}, "outputs": [], "source": ["from tensorflow.keras.datasets import mnist\n", "(train_images, Y_train), (test_images, Y_test) = mnist.load_data()\n", "train_images = train_images.reshape((train_images.shape[0], -1))/255.\n", "test_images = test_images.reshape((test_images.shape[0], -1))/255."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### define the encoder network"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2021-04-16T07:27:15.551864Z", "start_time": "2021-04-16T07:27:14.608092Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model: \"sequential\"\n", "_________________________________________________________________\n", "Layer (type)                 Output Shape              Param #   \n", "=================================================================\n", "conv2d (Conv2D)              (None, 14, 14, 64)        640       \n", "_________________________________________________________________\n", "conv2d_1 (Conv2D)            (None, 7, 7, 128)         73856     \n", "_________________________________________________________________\n", "flatten (Flatten)            (None, 6272)              0         \n", "_________________________________________________________________\n", "dense (Dense)                (None, 512)               3211776   \n", "_________________________________________________________________\n", "dense_1 (<PERSON><PERSON>)              (None, 512)               262656    \n", "_________________________________________________________________\n", "dense_2 (<PERSON><PERSON>)              (None, 2)                 1026      \n", "=================================================================\n", "Total params: 3,549,954\n", "Trainable params: 3,549,954\n", "Non-trainable params: 0\n", "_________________________________________________________________\n"]}], "source": ["import tensorflow as tf\n", "dims = (28,28, 1)\n", "n_components = 2\n", "encoder = tf.keras.Sequential([\n", "    tf.keras.layers.InputLayer(input_shape=dims),\n", "    tf.keras.layers.Conv2D(\n", "        filters=64, kernel_size=3, strides=(2, 2), activation=\"relu\", padding=\"same\"\n", "    ),\n", "    tf.keras.layers.Conv2D(\n", "        filters=128, kernel_size=3, strides=(2, 2), activation=\"relu\", padding=\"same\"\n", "    ),\n", "    tf.keras.layers.<PERSON><PERSON>(),\n", "    tf.keras.layers.Dense(units=512, activation=\"relu\"),\n", "    tf.keras.layers.Dense(units=512, activation=\"relu\"),\n", "    tf.keras.layers.Dense(units=n_components),\n", "])\n", "encoder.summary()"]}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2020-08-16T22:12:46.790121Z", "start_time": "2020-08-16T22:12:46.759185Z"}}, "source": ["### create parametric umap model"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2021-04-16T07:27:18.628794Z", "start_time": "2021-04-16T07:27:15.554550Z"}}, "outputs": [], "source": ["from umap.parametric_umap import ParametricUMAP"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2021-04-16T07:27:18.633924Z", "start_time": "2021-04-16T07:27:18.630297Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/mnt/cube/tsainbur/Projects/github_repos/umap/umap/parametric_umap.py:129: UserWarning: tensorflow_probability not installed.                 Setting global_correlation_loss_weight to zero.\n", "  warn(\n"]}], "source": ["embedder = ParametricUMAP(encoder=encoder, dims=dims, n_components=n_components, n_training_epochs=1, verbose=True)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2021-04-16T07:51:13.054044Z", "start_time": "2021-04-16T07:27:18.635059Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParametricUMAP(dims=(28, 28, 1),\n", "               encoder=<tensorflow.python.keras.engine.sequential.Sequential object at 0x7f5761761790>,\n", "               optimizer=<tensorflow.python.keras.optimizer_v2.adam.Adam object at 0x7f56401bd8b0>)\n", "Construct fuzzy simplicial set\n", "Fri Apr 16 00:27:18 2021 Finding Nearest Neighbors\n", "Fri Apr 16 00:27:18 2021 Building RP forest with 17 trees\n", "Fri Apr 16 00:27:20 2021 parallel NN descent for 16 iterations\n", "\t 0  /  16\n", "\t 1  /  16\n", "\t 2  /  16\n", "\t 3  /  16\n", "\t 4  /  16\n", "Fri Apr 16 00:27:30 2021 Finished Nearest Neighbor Search\n", "Fri Apr 16 00:27:32 2021 Construct embedding\n", "Epoch 1/10\n", "7798/7798 [==============================] - 137s 18ms/step - loss: 0.1203\n", "Epoch 2/10\n", "7798/7798 [==============================] - 140s 18ms/step - loss: 0.0989\n", "Epoch 3/10\n", "7798/7798 [==============================] - 146s 19ms/step - loss: 0.0957\n", "Epoch 4/10\n", "7798/7798 [==============================] - 141s 18ms/step - loss: 0.0943\n", "Epoch 5/10\n", "7798/7798 [==============================] - 143s 18ms/step - loss: 0.0934\n", "Epoch 6/10\n", "7798/7798 [==============================] - 132s 17ms/step - loss: 0.0930\n", "Epoch 7/10\n", "7798/7798 [==============================] - 133s 17ms/step - loss: 0.0926\n", "Epoch 8/10\n", "7798/7798 [==============================] - 132s 17ms/step - loss: 0.0922\n", "Epoch 9/10\n", "7798/7798 [==============================] - 132s 17ms/step - loss: 0.0918\n", "Epoch 10/10\n", "7798/7798 [==============================] - 132s 17ms/step - loss: 0.0917\n", "1875/1875 [==============================] - 2s 1ms/step\n", "Fri Apr 16 00:51:12 2021 Finished embedding\n"]}], "source": ["embedding = embedder.fit_transform(train_images)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### plot results"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2021-04-16T07:51:13.059804Z", "start_time": "2021-04-16T07:51:13.056667Z"}}, "outputs": [], "source": ["embedding = embedder.embedding_"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2021-04-16T07:51:13.242523Z", "start_time": "2021-04-16T07:51:13.061738Z"}}, "outputs": [], "source": ["import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2021-04-16T07:51:13.937323Z", "start_time": "2021-04-16T07:51:13.243687Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x576 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots( figsize=(8, 8))\n", "sc = ax.scatter(\n", "    embedding[:, 0],\n", "    embedding[:, 1],\n", "    c=Y_train.astype(int),\n", "    cmap=\"tab10\",\n", "    s=0.1,\n", "    alpha=0.5,\n", "    rasterized=True,\n", ")\n", "ax.axis('equal')\n", "ax.set_title(\"UMAP in Tensorflow embedding\", fontsize=20)\n", "plt.colorbar(sc, ax=ax);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### plotting loss"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2021-04-16T07:51:13.941059Z", "start_time": "2021-04-16T07:51:13.938425Z"}}, "outputs": [{"data": {"text/plain": ["dict_keys(['loss'])"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["embedder._history.keys()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2021-04-16T07:51:14.037995Z", "start_time": "2021-04-16T07:51:13.941968Z"}}, "outputs": [{"data": {"text/plain": ["Text(0.5, 0, 'Epoch')"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots()\n", "ax.plot(embedder._history['loss'])\n", "ax.set_ylabel('Cross Entropy')\n", "ax.set_xlabel('Epoch')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [conda env:umap_dev_new]", "language": "python", "name": "conda-env-umap_dev_new-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 2}