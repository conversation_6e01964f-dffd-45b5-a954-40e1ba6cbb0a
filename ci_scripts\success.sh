set -e

if [[ "$COVERAGE" == "true" ]]; then
#    # Need to run coveralls from a git checkout, so we copy .coverage
#    # from TEST_DIR where nosetests has been run
#    cp $TEST_DIR/.coverage $TRAVIS_BUILD_DIR
#    cd $TRAVIS_BUILD_DIR
    # Ignore coveralls failures as the coveralls server is not
    # very reliable but we don't want travis to report a failure
    # in the github UI just because the coverage report failed to
    # be published.
    coveralls || echo "Coveralls upload failed"
fi
