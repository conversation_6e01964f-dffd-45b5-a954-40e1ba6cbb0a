% Test script for UMAP implementation
clear; clc;

% Generate test data - Swiss roll
n_samples = 300;
t = 3*pi/2 * (1 + 2*rand(n_samples, 1));
height = 21 * rand(n_samples, 1);
X = [t.*cos(t), height, t.*sin(t)];

% Add some noise
X = X + 0.1*randn(size(X));

% Test UMAP
fprintf('Testing UMAP implementation...\n');
tic;
Y = umap_core(X, 15, 0.1, 200);
elapsed_time = toc;
fprintf('UMAP completed in %.2f seconds\n', elapsed_time);

% Plot results
figure('Position', [100, 100, 1200, 400]);

% Original data (3D)
subplot(1, 3, 1);
scatter3(X(:,1), X(:,2), X(:,3), 20, t, 'filled');
title('Original 3D Swiss Roll');
xlabel('X1'); ylabel('X2'); zlabel('X3');
colorbar;
view(3);

% UMAP embedding (2D)
subplot(1, 3, 2);
scatter(Y(:,1), Y(:,2), 20, t, 'filled');
title('UMAP Embedding');
xlabel('UMAP 1'); ylabel('UMAP 2');
colorbar;
axis equal;

% Compare with PCA
[~, Y_pca] = pca(X);
Y_pca = Y_pca(:, 1:2);
subplot(1, 3, 3);
scatter(Y_pca(:,1), Y_pca(:,2), 20, t, 'filled');
title('PCA (first 2 components)');
xlabel('PC 1'); ylabel('PC 2');
colorbar;
axis equal;

% Print some statistics
fprintf('\nResults:\n');
fprintf('Input dimensions: %d\n', size(X, 2));
fprintf('Output dimensions: %d\n', size(Y, 2));
fprintf('Number of samples: %d\n', size(X, 1));
fprintf('UMAP embedding range: [%.3f, %.3f] x [%.3f, %.3f]\n', ...
    min(Y(:,1)), max(Y(:,1)), min(Y(:,2)), max(Y(:,2)));

% Test with different parameters
fprintf('\nTesting with different parameters...\n');
Y2 = umap_core(X, 5, 0.01, 100);  % Fewer neighbors, smaller min_dist
Y3 = umap_core(X, 30, 0.5, 100);  % More neighbors, larger min_dist

figure('Position', [100, 600, 800, 400]);
subplot(1, 2, 1);
scatter(Y2(:,1), Y2(:,2), 20, t, 'filled');
title('UMAP (n\_neighbors=5, min\_dist=0.01)');
xlabel('UMAP 1'); ylabel('UMAP 2');
colorbar;
axis equal;

subplot(1, 2, 2);
scatter(Y3(:,1), Y3(:,2), 20, t, 'filled');
title('UMAP (n\_neighbors=30, min\_dist=0.5)');
xlabel('UMAP 1'); ylabel('UMAP 2');
colorbar;
axis equal;

fprintf('Test completed successfully!\n');
