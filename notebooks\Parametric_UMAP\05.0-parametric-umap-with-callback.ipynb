{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Adding a custom callback for keras during embedding\n", "This notebook shows you how to use custom callbacks during training. In this example, we use early stopping to train the network until loss reaches some desired plateau. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### load data"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T06:03:48.706532Z", "start_time": "2020-08-17T06:03:48.366292Z"}}, "outputs": [], "source": ["from tensorflow.keras.datasets import mnist\n", "(train_images, Y_train), (test_images, Y_test) = mnist.load_data()\n", "train_images = train_images.reshape((train_images.shape[0], -1))/255.\n", "test_images = test_images.reshape((test_images.shape[0], -1))/255."]}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2020-08-16T22:12:46.790121Z", "start_time": "2020-08-16T22:12:46.759185Z"}}, "source": ["### create parametric umap model"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T06:03:53.254841Z", "start_time": "2020-08-17T06:03:48.708919Z"}}, "outputs": [], "source": ["from umap.parametric_umap import ParametricUMAP"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T06:03:53.292492Z", "start_time": "2020-08-17T06:03:53.256551Z"}}, "outputs": [], "source": ["keras_fit_kwargs = {\"callbacks\": [\n", "    tf.keras.callbacks.EarlyStopping(\n", "        monitor='loss',\n", "        min_delta=10**-2,\n", "        patience=10,\n", "        verbose=1,\n", "    )\n", "]}"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T06:03:53.323141Z", "start_time": "2020-08-17T06:03:53.293677Z"}}, "outputs": [], "source": ["embedder = ParametricUMAP(\n", "    verbose=True,\n", "    keras_fit_kwargs = keras_fit_kwargs,\n", "    n_training_epochs=5\n", ")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T06:08:43.351200Z", "start_time": "2020-08-17T06:03:53.324273Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParametricUMAP(keras_fit_kwargs={'callbacks': [<tensorflow.python.keras.callbacks.EarlyStopping object at 0x7fd30942f7b8>]},\n", "               n_training_epochs=20,\n", "               optimizer=<tensorflow.python.keras.optimizer_v2.adam.Adam object at 0x7fd30942f668>)\n", "Construct fuzzy simplicial set\n", "Sun Aug 16 23:03:53 2020 Finding Nearest Neighbors\n", "Sun Aug 16 23:03:53 2020 Building RP forest with 17 trees\n", "Sun Aug 16 23:03:55 2020 parallel NN descent for 16 iterations\n", "\t 0  /  16\n", "\t 1  /  16\n", "\t 2  /  16\n", "\t 3  /  16\n", "\t 4  /  16\n", "Sun Aug 16 23:04:05 2020 Finished Nearest Neighbor Search\n", "Sun Aug 16 23:04:08 2020 Construct embedding\n", "Epoch 1/200\n", "724/724 [==============================] - 13s 19ms/step - loss: 0.2263\n", "Epoch 2/200\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.1717\n", "Epoch 3/200\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.1561\n", "Epoch 4/200\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.1463\n", "Epoch 5/200\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.1397\n", "Epoch 6/200\n", "724/724 [==============================] - 13s 18ms/step - loss: 0.1374\n", "Epoch 7/200\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.1339\n", "Epoch 8/200\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.1329\n", "Epoch 9/200\n", "724/724 [==============================] - 13s 19ms/step - loss: 0.1285\n", "Epoch 10/200\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.1282\n", "Epoch 11/200\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.1263\n", "Epoch 12/200\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.1247\n", "Epoch 13/200\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.1255\n", "Epoch 14/200\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.1241\n", "Epoch 15/200\n", "724/724 [==============================] - 14s 20ms/step - loss: 0.1226\n", "Epoch 16/200\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.1224\n", "Epoch 17/200\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.1220\n", "Epoch 18/200\n", "724/724 [==============================] - 14s 19ms/step - loss: 0.1208\n", "Epoch 19/200\n", "724/724 [==============================] - 14s 20ms/step - loss: 0.1206\n", "Epoch 00019: early stopping\n", "1875/1875 [==============================] - 1s 705us/step\n", "Sun Aug 16 23:08:43 2020 Finished embedding\n"]}], "source": ["embedding = embedder.fit_transform(train_images)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### plot results"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T06:08:43.992558Z", "start_time": "2020-08-17T06:08:43.352974Z"}}, "outputs": [], "source": ["embedding = embedder.embedding_"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T06:08:44.165383Z", "start_time": "2020-08-17T06:08:43.994231Z"}}, "outputs": [], "source": ["import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T06:08:45.670161Z", "start_time": "2020-08-17T06:08:44.166884Z"}}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAckAAAHnCAYAAADTmaY3AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4xLjEsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy8QZhcZAAAgAElEQVR4nOzdd5Qk133Y+++tqs65pyfH3dmADdhFWAIgMgEwiVlMsmQfko9UpqT3bFmSrUClZwXLkmjxPcqkZYEUJIoURVEkzQwSGQtswGKxOU2e6Qmdc1e4/qN6iMFgZgMwg11g7+ecPjN961bV7dtd/au6oVpIKVEURVEU5cW0y10ARVEURblSqSCpKIqiKKtQQVJRFEVRVqGCpKIoiqKsQgVJRVEURVmFCpKKoiiKsgoVJF/jhBBDQggphLj/cpflaiOE+IgQ4lkhRLn1HvxhK31BCHHkcpfvSiWE+Hirvt53mfYfbu3/G5ewzttb6/zqsvT9Qojy2pdSeaW85oOkEOLu1of3ofPkWQwko8vSP9xKl0KIhy+wvrOY9wLl+c0l29x6nnxL9734aAghRoQQ9wshtp9vP6+EVcp43sflLvMrRQhxH/C/AC/w/wG/B/zgshZKUZRLZlzuArxKWMCdQoitUsqTKyz/GCBa+VatUyGEAD4KyFb+nwZ+dbX8Lc8CX239HwPuBj4EfEAIcY+Ucu8F1p8CtgGFC+R7KQ7hfvkvNYRbvjHg/nXY56vF21t/PyilPHxZS6JcTu8FfJe7EMpLp4LkxfkG8G7cYPgfly4QQujAR4B9QA/Qe57tvAnYgBs83gp8SAjxn6WUzfOsc0hK+btL9ieAv8UNRH8EvOF8BZdSmsCJ8+V5qaSUh3AD5Y8IIe5ulW10abmvQj2tv9OXtRTKZSWlHLvcZVBentd8c+saOQo8iRvUPMuWvQ33C/GzF7Gdn279/Szw90AKeM+lFES69xH8/1tPb7pQ/tX6JFtNtrK1/GeFEM8JIepCiFkhxGeEELFLKddLIYS4XQjx1dY+m0KIMSHEp4QQHSvk3d/q2/MKIX5XCHGu1fw8JoT4AyHEi074hBD3CiG+JYSYauWdEUI8LoT49RXy9rde93irLLNCiC8JIXatkPdHfWZCiHcJIR4VQhSFEKXFZcD7W9nnlzQ3py5QH0EhxO8IIY4KIWpCiIIQ4odCiHcty5dqNe9/Z1l6XAhht/b1nmXLfq2V/oHzlWFJftFqTn9ECJFvfTaOCCF+ffkxIJb04QkheoUQDwgh5lvv18NCiJtb+aJCiE8KISZa78dhIcQ7LlCO9wohnhZCVIQQGSHEF4QQQ6vkDbfq7zkhRLX1fjwqhPjxVfL7W5+d0VZ5zgohfgdYfowvXadPCPF54fYrV4UQB4QQP3Ge/C/qkxRL+i+FEDcJIb7Teq/LQojvCyFuXGVbA626XbrvD4pV+kOVtaGC5MX7LNAOvGtZ+k8DZeAfz7eyEKITeCdwSkr5BO7VIMDPvISyiNbftejj+9PW41ncvrMp3Nf0L2uw7VUJIT4OPALcA3wP+MtWGX4BeLpVXy9aDfgK7hX9D4C/Bmzgt4C/WLb99wLfB14HfAf4b8DXcevsZ5flvQY4gPu6jwF/1tr+e4C9Qoh7V3kZHwL+GcgAn8ats6dxm6CPt/L819bz3wOq56mPAPDDVj4b+CvgC8Au4KtCiP+8mFdKuQAcBm4XQniXbOZunj+ml5f5ntZr/+FqZVhSFtHa998C/cA/4Z6YVYE/bpVnpe+ODtyTyS3AA8DXgNuA7wu3D/0R4I243QcPAJuAf1npRKTl37XKcQb4JHAQ+Anc92RoWZnbgad4vp4/29pHP/DPQojfWJZfa5Xvt4A68N+BbwMfZ5VuAiFET+v1/Tvc+v8k7vv8OZ4/Ab4Ut+PWiWyV97u479NDQojBZfvua+37p3Bbbz6Je/L+OdwuHGW9SClf0w/cLw4JPHSePEOtPKPL0j/cSv9DIIjbr/edJct7cfshP9t6PknrYm+FffxGa1v/aUnaAcABNq2Qf3Hf9y9LF7gHhgQevIjXP7TKdu5vpY8DA0vSDZ4/cG9ap/q+rlVvR4COZcve0Vr/75al72+lPw7ElqRHW/XeAOJL0r/Tyr9S3aaWPX+slfdXlqXf13p/pgHfkvSPt/JbwF2rvMYvt/KkVli2ABxZlvb/tvJ/GdCXfcZmcAPndUvS/7yV/64laX+F+4X/CHB8SboX90Tu2Yt8Dxdf3wPLXrfADfoS+OiS9HArTbbKJZYs+/lWehb4IuBZsuytq7zXi/t3gDcsW/abrWX/ukp9/+Ky9CDwcOu92rIk/Wda+R9cVqbO1udJAt9Ytq1/aKX/wbL0O1pllcCvrvC5LS9Le/uS+nrfsmX/oZX+p8vSv9hK/+1l6be0XtuL9q0ea/O47AVY9xe4RkGy9fzTrYNhqPX8t1kSTFglSLa+XM60vuh6l6T/Umv9P15hncV9HwJ+t/X4C+CZVnoVuOUiXv/ia7t/Wfr9rfSPrbDOR1rLPr5O9f1Zln3BL1v+PaDGC7+gF4Pki14z7lWiBO5ekvad1nvVd4Hybm2tewLQVlj+L63lP74kbfFL/O/Os91LDZIzgAkMrpD/V1rb+u9L0t7WSvv9JWnHcL/0f721rKeVfmfr+Z9f5Ht4GqgAwRWWeVvLfrAkbTFIZgH/svxBng94nStsbw54blnaYv3+6yr7n8Q9ltpbaX2t7f9wlddzW2t7v7Mk7clW2utWyL+4/28se40mML9KvSy+35cSJL+9wnYiy4+fVpoJzC6v39byL6y0b/VYm4cauHNpPgv8HPBRIcQncJs5Dkspn77AevcAw7hXoVNL0v8Bt2nvw0KI35buIJvldrce4B4oM8Df4QbWYy/9pfzI/hXSJlp/E2uw/ZW8vvX3jUKIlQYexQE/boBfOprYwT1JWG6l8v497kCpQ0KIL+I2Mz4upZxZtu4Nrb8PSSmdFbb9A9xBW9fjNvUudaH3/aIIIbqBLuCkXHmgx+LUkeuXpD2CewVxL/A7Qogu3FHMDyzJfy/uZ+WeZds5X1lSuM2gU8CvuS2vL1Jt7Wu5o1LK+tIEKWVVCFEASlLK2RXWmQYGVynOi6ZdSSmbQoi9uKNGd+M2qd+CeyLqEUL87grbCbX+Li3z9UBNSrlvhfwPrZB2LW4ryz4p5UrN5g+1ynQpXnTsSSlLrfpa+lne2dr3geX12/IYbjO0sg6uhiC5+MV3vv7XxWUrfUn+iJTyoBDiIO6V1l7cg/uXLqIMi/2O9y/bXkYI8XXcg+tduGejy31OSvnhi9jHS5VfIc1q/dXXaZ9trb+/eYF84WXPa1LKxgr5XlReKeXnWwMm/m/cPshfAGh9wf6GlHLxC3hxgNLy4Mmy9PgKy9LnL/5Fu+QytL5M9wE3CSEiPN8H+SBuM36e54Pkvbh19MhFlGXxvekFPnGefCtNkF9tmpF1gWWrDZRZKajC8/W+WG+LZb6t9VhNGNwBO7jTMkYvsP2lFvd1oTJdipWOPXDrZOmxd6F9r5aurIGrYeDO4sHZdp48i6MOV/vQLvUZ3C+Qv8ZtEnzgfJlbAwre3Xr6BfHiyfWLZ58vZQDPq1UBt3nII6UU53kceDk7kVJ+RUp5J+5Z+ZuATwE3At8UQmxcUhZwr+RW0r0s3wt28XLKt8RLLcMPcE9078INhEVgf+uK+GHgXiFECLi5lV68hLI8eoH3JnKRr+3lWGnwFjxfT4Vlf//gAmV+B0DraqxxEdtfanEfl7LOWll831bb92rpyhq4GoLkSdwDYosQYrVAudj89+xFbO8fcPtk+oB/klJeKLB+CLcf5QDwN6s85oH7hBAbLmL/rwV7cZvHznfWv2aklCUp5feklL+E268bxB1lCc83394lVm5bXGwOPriO5ZvBvRIZFkL0X0IZHmz9vZfWqEgppb1kWR/uqEvPkrwXKksa9wrreiHE8iv5V9pdyxNao3lvwW31WTxeF2+occclbPsZICCEeN0Ky+5eIe053Cu81wkhghe5zlpZ3PeNravg5W5fx31f9V7zQbJ11viPuGfc/3X5F2FraPXiDQLuv4jtlYC34E4P+K2LKMLHWn9/QUr5sZUewP/ADRofW30zrymfxB148amVTgxa89deVgAVQrxRCLHSnU4Wz7qrAFLKE7iDOLbx4qkhb8BtBUgD33w55bkIf4sbzP5k6fSK1rSDxZHRf7tsnSdwR7P+JG7T/9JAuNj/+J+WPb8Yf4HbNPnZVlPuCwh3nubuF6+25t6xQp/1f8RtyfnfUsp5ACnlKO4Aq7uFEP9+pekpQogty05AFuvyj8WSeZ+tqUcvmC7S2kcZdypMiufrdHGdO7jE+c6XovWd81XcKTbLb2ZyM8/PyVXWwdXQJwnusOrX4fYlvl4I8T3cJoxB3L7ACPAnS/qpzktK+djF5BPu3We24o7eO98gj7/B7Z/7iBDiE1JK6zx5X/WklM8IIX4Bd+7dCSHEt3BHVAaAAdzRmGeAPS9jN58GEsK95+4oblC+Gfdq4xQvnAf6Mdz+uk8LId6JO6J4CHgf0AQ+tMqAibX0h7hXt/8G2C7cGwVEgA/gdhV8Qkr5gkFLUsqGEOJxXtgfubjsqBAijdsMWMcNqBfrr3AHNH0It8n2e7hThVK4A9Bux51XeDEtLy/H14HvCCG+jPse7sGto1ngl5fl/Wncu1n9N+BjQogncEcR9wA7Wq/nHTw/yOt/4gaX+4DDwr2ZeQC3vp9k5Ttn/Sru5+e3Widxe3E/r+/HvSvXO1/2K17df8Ct998XQtyJe4evvlZ5v457MnfeMRXKS3NVBMnWAJmbcQ+s9+BOrwjgTgJ/GPi0lHI9rhQWJxj/zwuUb1QI8X3cL4B3sM4T+a8EUsrPCCH2A/8et1ntx4AS7mjHv8cd1v5y/B5uXd6A2x9p437R/y7wV60rg8WyHGvd5eS3cVsJ7sPtg/oa7vSfQ6yz1ijQu3GvFD6IO+2jidss+Ekp5fKRtYsexA2Ss1LKo8uW/QD3KvOJSwnyUkqJO+L6a7h95W/GnY+awb0n7x/hDghab38HfB53Ssu7cYP9F3EHXo0uK3NGCPF63AFaH8QNHl7cgHoSd4DdY0vyO60Tot/EvTnAL+NOLfkU7klCdnlhpJTTrX38Ee7n9fW4024+hDuQad2CpJRyXAhxS2vfb8YNmIv7DuDWz8X0OSuXSLjHg6IoivJqJIT4JG6Qv11K+fjlLs9rjQqSiqIorwJCiB4p5fSytNfhdhVkcW9E8ZruqrkcrormVkVRlNeA46152kdxm5634t7aD9zb8akAeRGEEL+C2xUmcG8p+pfnza+uJBVFUa58QojFvtAB3NHHOdwBWX8q3R9NUC5ACLETd7bDTbh9/t8Gfl5KeXrVdVSQVBRFUa4GQoj3A29uTb1DCPHbQENK+aerrfOanyepKIqiKC1HgDuFEG2tm0L8GO7Pqa3qiuqTTKVScmho6HIXQ1EURQEOHDiwIKVsX49tb73rDbKSfdFMm5dt6sjhxT7bRZ+RUn4GQEp5XAjxJ7i/NFTGnet73r7cKypIDg0NsX//Sj9KoSiKorzShBAr/SrNmqhks/zK17695tv9tY09dSnlqjcikVIu3g4UIcR/wZ0fu6orKkgqiqIoynoSQnRIKeeEEAPAj/P8vbtXpIKkoiiKcjX559aPXZi4U2dy58usgqSiKIpy1ZBSXsqvxajRrYqiKIqyGhUkFUVRFGUVKkgqiqIoyipUkFQURVGUVaggqSiKoiirUEFSURRFUVahgqSiKIqirEIFSUVRFEVZhQqSiqIoirIKFSQVRVEUZRUqSCqKoijKKlSQVBRFUZRVqCCpKIqiKKtQQVJRFEVRVqGCpKIoiqKsQgVJRVEURVmFCpKKoiiKsgoVJBVFURRlFSpIKoqiKMoqVJBUFEVRlFWoIKkoiqIoq1BBUlEURVFWoYKkoiiKoqxCBUlFURRFWYUKkoqiKIqyChUkFUVRFGUVKkgqiqIoyipUkFQURVGUVaggqSiKoiirUEFSURRFUVahgqSy7qZyVU6mS5e7GIqiKJdMBUll3emahv4SPmlNy6FQM9e+QIqiKBdJBUll3XXF/GzqiFzyeiMLFZ6bLKxDiRRFUS6OCpLKFWtLZ5hbNiYveb1j0wXKDWsdSqQoytVGBUnliiWEwGi10xZqJnXTvuA6pbpJpWnTtJz1Lp6iKFcBFSSVK5JpOxydLuA4kul8la/sn+DbR2YoVE3ShRpSyhetU21a/OD4HNu6Ipi2s2IeRVGUS6GCpHJZjGXKnJ0vk600f9Q0arUCW6Vu8cxYju8fm2UqX+PwRIHeRIDdfTGOTOX50v5xnp3Mv2B75YbFZLbKm3Z0oWsa+0ayVJo2ddPGcVSwVBTlpTHWYiNCiP8FvB2Yk1LubKUlgS8CQ8Ao8AEpZW4t9qe8utmO5MhUkUy5wbaeGGGvweaOED//wAG290SJ+L10Rn30xgN86/AME7kyhq5x08Y2vIZG1bTRhSCdr3E8XaIt7KU7FuDwZIEz82V298a5Y3OKg2M5mpZDT8KPT9cwbck13dHL/fIVRXkVWZMgCdwPfAr4/JK03wAelFL+sRDiN1rPf32N9qe8iv3LM5PsPbvAtb1xeuIBTqdLDLYFMR2bc/NlfnzPAImAh1LdJFNpcF1/kmrTIlNpcHy6yFPnsmga3LShjVPpEplynXu2deE48PdPjtF2n5dCze2bvHNTiq8+M0VfMsj2HhUgFeVKYdsVcrmnLncxLmhNgqSU8hEhxNCy5HcBd7f+/xzwECpIXtXqpo3P0Lh9U4pK3aQ3EaBcM5GO5OB4jndf30/Cr1OoNPjG4Wlu3diGI6FmWmTLJv/41BhjmSpj2RrtYQ/PTuS5pivCufkKhj7H1q4w92xrp1gzaVoONw4miQY8NB2HI5MFDkyf5k1br+GG/nY0TVzu6lCUq1pKb/Kx+Niab/eP1nh7a3UluZJOKeUMgJRyRgjRsVImIcTPAD8DMDAwsI7FUV5JI/NlYkEvyZAXy3bIVps8dnKBqmXxtmt7eOu1Pfz9U2N87dAUYZ9BMuRDIvEZAl1o3HtNB/dt6+J/PHSGH5xbIBbyMTZfwmfofOT1g3zqodPkKk32DMQZTAXRNI1zczUajk3dlKQiPr58YII7N6WoNx3y1SYnFvLs7snjyBQaKkgqytVICPH/AB8DJPAc8BEpZX21/Jd94I6U8jNSyj1Syj3t7e2XuzjKGinWrR9N2Zgp1Dk8UWAkU8G2JdWmxUyhxvaeGL/21m381C2DVCyLA6N5Ah4PppTc0B+n2rRoj3oZz1WJ+TR0XaNYaxLwa2xsC3PHpjYePD7LsYkcg+i854YedvVECXp1emJ+dvXGOTlb4ltHZig2THrCvaSCSU7MFNk/mrnMNaQoyitNCNEL/DKwpzV+Rgd+4nzrrOeV5KwQort1FdkNzK3jvpQrhJSSbKXJ7v44R6YKOFKSCHqIBgzeeV0PU9kKn/z+KTqjfspNiy3tYWZtm+dKVcYXihQONrnnmhR/9K0TNEyLiN9DyKOzdyTHbKGG39D46x+eJVtp0hXxkak0qHgM/vX4DF8bm2cgFeZMukB/W5BS3eL6wSSdER9v2dZFUzpkK03OzJWJ+D2Xu6oURbk8DCAghDCBIDB9vszreSX5NeBDrf8/BPzrOu5LuULkqyaPnFogXagR8Rv4PTrfPzbLF54eI12oYUvJRLbKgbEs6XyNTz98hpmpIjsiQZIBL5WGyWNnFhhdKFOpW5xKl4n4PAwkg2xoD9EdD1KsWQS9GlPFBkJoeAzBiWKVsVyFkzMF0ARj2SrZSoPHTs9jOQ73PznCeKaKZUsSQQNNuH2kJ9NFHtg7hq2miSjKa0VKCLF/yeNnFhdIKaeAPwPGgRmgIKX87vk2tiZBUgjxBeBJYKsQYlII8VHgj4E3CiFOA29sPVde4xIhLzt7o5yaLTPYFiIV9nHThiQfu30D+0ayjCxU2NYTY3dfnJDXYK5UZ+/ZDNd1RPgv79jJG7Z10LQlHl0j6DOwpORMpsyNgwnCPgOvLogGdIoNB4GkI+rHkRKvobExEcCRkpDXwKNpbOipEwyYCKGRqTY5OlNk77ksX94/SblhUayZBDw6Qa/OfKlxuatOUZS1sbDYhdd6fGZxgRAigTuodAPQA4SEEP/2fBtbq9Gt/2aVRfeuxfaVK9dT5zL0JYP0xgM/StvcGUHXBPlqk6fOZbltc4qnRrMEfDpbu6IkAjUeOrXA5q4wliPJVZscPJ3hWZHlTLlGrWHRFvJgS4mUkphP57HT80zla0T9HmqmJGTo3DAUo2ZCplRjoWzStB3GMlWals22nhjdoShvvSaJdDROzZaZKdbIVusMpUIcmSywUKzzb2/dwGAySK7apCvmv4w1qSjKK+A+YERKOQ8ghPgKcCvwwGorrGefpHIV6Ir5SQRf2L8npeQbz04T9RtM5urs6ouyrStKV9SP7Ui+fjhNw7LYYAZ4844uHj87z77RLJlKE00I+hIBhBCMzFfY3Bmi3LA5MlWkPaQzW67z/hv6qDRsvrhvHEPTaAv7qJkWbUE/HkNQsySnZkt890iNG4bKeDSN2VKT7qi7PFszaZg2J9JFvAja0OkYiFJP2fg9+mWqSUVRXgHjwC1CiCBQw72Q23++FVSQVF6WwbYQ4AbGk7MlhtqCnJguUaibhLyCQrXB73/9CLpuUKk3mS02kdJhY0eYJ89mGMnWsG1JyKMRSARIF2rM5GuAIOzX6UuGyJYanPOAz2OQLlb5/rFZqg0bTcDWrgh10yZfMzk1V+bavhid0SDPjC1gAKPTBbb3RbltOMHs8XMketo5Ot/EZ8DmjgiHZ0q8c3MHqYgPz0v50UtFUV41pJRPCSG+DBwELOAZ4DPnW0cFSWVNOBIypSbFqsl3j84Q8hncsjHF0yNZnpsuMpQM0RPzkauZYAtm81WKTYuNHp3T2TK7B5KU6hY+wyDi0zk2XWAwGeT0bJl60yYZDpCt1DElzJbqNE0H04aRTIVEwKBhmthUmS8bGLpGyO9lZ68PQzr4PHBitkKlUCOvF9g82MWJdJnueICuGFy7rY3uWPByV6GiKK8AKeUngE9cbH4VJJU1oWuC2zanKDcsNneG2T+a5YmRLF2xIF3RIF8/PIXjhDBNh4hfMD89S90XZe+5BSxbUq4F8Rg6ewZjJEJ+chWTqukwV2ygCcmtwykOjOUQWoOucIDeuJ9HzmawbAfLcnAc0HTBTGWScrOHfNkh5NVIRXyMTNcYjDXYdG0HmpEk6DPY2RcjVzGpmyZ/9q3jvOXaHt64o/tyV6OiKFcYFSSVNRX2GZwr1vjck6P0J/wMd0SpNW3aw16a0sGybDqjUQaNKPszgtlKg6jfw+uHUzw9lmUiU+XcfIX+ZJBNHSGkI9k7liU9WYa6TdxnMNQepmGaJMNeSpUGJUtyy3CKozMF6mYQ2xJYNlRMh1s7wgR9Hoqlk3SGJHU6ODRVwK9pOEAq4mU6V+fzT45y9zWdqslVUZQXUEFSWXNHJouUaiaVkJf7HzlDKOChLern+GyJzoCHp89lifoNZit1hIRq0+SvHz4LQlBpWhiaoNowefyMwO/R6EsEaTQs6rZDrmFSOLeArgvawz56oj6KdZuQz2B3b5TJXJWZQoMtnQH3Hq/RAOlig/pChGcrIfCWKVUbxBIhSg2LM7MVbhlOEvIZjMyX2dKlboKuKMrz1GmzsiaqTYvpfI0nz8xTaVps6YyyZzDBjRuSaELgLzWI6RqWI5kpm5xcqNNogq5D3YSo3yCdqxDz6wy3ufMdHUcyV2xSqDWIdEcIBHWifi9106ZuOtyxpZPRXB5blDg6mefJc1lqJgQ8HmbLJlUTvn8iTbVpMmMJLCBXbVKsWcyVapimQyxosCkVpivqJ+xX54yKoryQ+lZQ1kS6UCedr/Gl/RMcnS7Qlwgy1BamPeLjyMRRJnIN/GEfBWkT9ULTgkhAp9608dhVTs1AxYKxTJV0oUHCb1CxHOo1sKWGXxeczdTY1ROnaUuSIT8np/O0h72MZKs06jCYCtKXCGIIga4Lfnh8jnLNIur3MOb1c9/mLo5N59FxODtfY0dvBEPTeGIkw2imyk0bU/TEL3dNKopyJVFBUrloTsNG8608j3BjexhdCHyGxs6eCCGfh288O8VMqUa+7lBBY0fcT6FYJxowiAW8RAMGI3NVmvUGpuGOLq2Y4Dg2uZpN1ANDcQ9vv7aTkUwNnwZ9bQFy1TrpYpWdvRHCgQ4GEk3MskUk5uXMfI1jMwWQkAh6kAI2dUR4664eTs4UydUsEqEAgVKT+VKdbT0JrumKcNtwG9d0RRjPVKmbFgNtITVnUlEUFSSVi+M0bGrHMgR2tKF5Vw4e//D0OAfGcpTqJlu7IkzmapSqDUI+D/GggSUdHAmlmkW6YOHRoSPqo+RP0BbyErQc5soWDdu9NX/FhGjFZupsgUbQ4AM3DXJ4Mo/Po7MxBl1dY5QKgzx1popethlNl6n6IRH00RExEEKjULeQwONn5tk3kiMZ9LC1N0KodYs7Bzg4kSNoGJQaNg3LpiPip1S3uHEo+UpWsaIoVyAVJJWLovn08wZIx5F8YE8vtUaTrz4zyYHRLN2JEEPtETrDXh47l6VUd29MnjWhIwIWHjy6RNcEc/kmXq+7rc6IQTLopykdvLZDVZfMFuscmi2wYEjiVYcPbu1DmllmcyaOA6O1GrYUvPfaPop1976ss8U6QadGqVzmdLrCeKZMruLBMAzCXo2K6TBTqBH16QzGAzxxZoFspcE7r+vl0dMLbO2KEPAa6OoHmhXlqqUG7igXbbUAaVo2/3RgggePzxHye7h9UxudUT8BHXKVBmfmChTqNnMVm1pTcssGDxvCz5Et1ZnKNbEsSVvUSyzo5bq+CNf1JzAdm+FUCFsIDqZL5KtNisU68Zok7NV54NCjjMwYHJsuMZ6r0B0PkAx7mMrXW82kEp9HULFrnJwtM9s8i63PUKpZnJkvsrs/zkfvGMKybTZ1hHj4TIaAoWFL2On18I7N7RyZLvDcVOGVrWRFUa4oKkgqL9vhqQLxoAVzZLoAACAASURBVIe+RIDD43mGO2IIDc6lKywU6mSqDjrQFgSvAfsnTEbL25Ho6AK290S4b1sHvYkg1/fHOZYuMVeq8+xkjtmKSTzq5317+tjc4QPbYa5Upy8leWb2BJWGSWfET29S0LQcpnJlQh6dbV0xPJrOUE+K6WoZzWzDSwrLgYAm2Hsuw+eeGKNmOYxmKkS8HpqOw4b2MIam8eCpWQ5P5BlKqjvxKMrVTDW3Ki/bjp4YALWmxb8emuJLByaoN0yCQQ0hHfJVC12Dah0yVQsNcJoaNiA0CPk9nJ0rcWyqSG8sSCLso2paRH1etrb7mShW+e6xSbrDeQaSAxyeLHNX/y388NgktoS7tnUwmp/i2v4g5ZpOqWHy6NkFMqU6qZCPhSIkwj52drnbPjZd5OxcCQPJUDJMzbQpVBuAyafedSPJYIhrdcHJdJGQ36AxVsRIBdBD6oeaFeVqo64klYsiHUljsoS0nBct83t0/B6dhVIDj9AIGDpeDfJVh7kaNAHHgZrjnpV1eMBjVQnoEPN76I4HODVXoWlLHj01i1+DmM9LPORF1zUChsapdJ19E2EiQR/v29NHveElW/EyVjb54Yk50rkQuvAR8Rkcny0wm6/QHvIyUagS8vsYaIvREQ/xi/dsoS8ZwkHgCJ1667cr+9sC+IIZ/vHQ43z3WBqvoXHDYBKPriE8GkJX/ZKKcjVSV5LKxZFA00HaErHCp+Y7R2c4OpVjbDRLyTKRjoVHNzBtd7kF7OgKYEtBQIeGDBGTgnyjybeemwEpCfl0TEcS8AgQ7m9J5moNmrYk7NNoj/jZezbH23f76Yn7KNRNakCh2iARr3B4wsemjjCOqJJMFLh5qBfvRAGfoTNXqBP2aIxlqhTrFv3xgDunUhO87bpeHnhyhOlCinNH8kzHx8iUGvzYrm4EgmjcS8xvsFBukAx60dRAHkW5aqggqVwUoQt8G2OrLt/SEaFWNzlow7gFaAbXdIc5NVcm4NGYr7gT+C0bBLC1O0w87KFuW5Sk+4seqYiX3b0xYgEfZ+dnmCvVifg8aEjQBPm6RcBrsKEtxPePpQkYOrGgRq1u49F83DCQRNdAyhhxb4R9owX8hmBTR5hDE0VOTpeJiRnu3pJiJFPlmfEcVdOm0GiiCY1yHbIFG71SZKbisLUrzMOn5on4DV63PcPcQopbNwzSs+QHphVFeW1Tza3KmtjQHubYdJFnTJOiDWUTDk+WKTdhvuI20WpAR1CgCaiaFgFDo1K3SYWgI+xBOpJy06Et6mW4I8LNQ21Umg73bOvkrm0+SrZDf3uA7x2fZd9olsG2ENf2Jgj7PPRFOqiZFs9NFRldqJIMBdE1gS0lZ+YXiISrJPw6j42P88zEAgNxPx94XT/hYI3J/DyOY7OjJ0o01UE4GKRpwUMnZxnNVJjI1fDSxtt3blQBUlGuMipIKi/b46cXOD1bZDxTpmK7LbMAHt1tqvACQQOiPp03RiLEgjoBjw5Cw9DA0G0CPgfbcaiZFiem8swWGiAEu/qiBHxwJl0l4tXZ3BYmX21QaVrc1BHj/df10RbxcXiqQMOyuW1Tint3dKBrMNQeYipX4+ychSGDzDdNgrEmYb/ND07Ok62Y2LZOPBih2LB5djJHyKtx6+Z2tnaH+M6RNKW6xVAqSEegn2fGS5exlhVFuRxUc6vysu07t8B8ucEjZzL4NHeQjlcDwxDUbDdk9ka9FEybB2aLSMB26sRDXmwpSEQSFKoOAY/N2bkSXl2jPeLjmakpCrUGJ9JJGqaXG5Mhnjw6S12HO7Z2oDuSv3n0LAPJALPFJsWaSa5mMjJXJuI3ePuuHs7OFkmG/NRNk1LNpG4mkOEgdbPEQrnGu3Zv5uBYlnShSr1pk6822TuSJREwQGhE/B5iAS+1pk29aV3eilYU5RWnriSVl2UiW8Xr0SnUmjgO2A4EveDzQqEhEUBAB9MGy5b4DTeA1kybsKGTCHmZy5uMzJYpmzaJkJe3XddLw7KYzYFl6Xg1wabOCFrQQ01KSnWHwXgQbyrAqYUSJ2ZLbOsNsXFwDI+nTCrk4b7tHcRCHjzBaRwtT1csxE/ePMCGZJDZUgOA8YUqupBUmjYRn06iJ4LQYFtniIDP4G07O6nUTaazbv9lrtbkweOzFGrm5a10RVFeMepKUnlZMuUmP7arh/u2d3JqtsS5+RpNCxqO28TaFfWRrTQRGnQGfUw2atQd8APTxQa6Jgh4NNq9Gr0JP8mwn3ylScjn5dbhEFa5yVAyxPbNbTQsh3Slya6YjyfPZTClJBb1kwhZjBXSWEaNhN5gR387B8YK7OqD9nCCZj1AvtJgXJOMZmrsGUqwvTfC94/NcXy6QMij0efx8WC5QtyRPHkuAwIsO0q51uRbR2b4pXu28M5dPYxmq4R96rBRlKuFupJUXpbrBuIMpUJs6YryoVs3EPQJfF5BKqhh6AJN16hbkqDHQFoOuibw67ClK4TlOET8HhIhg+2bU2zsiOJYDhO5Gv2JIKAxazucLjeYKdR58myG9pCXU+kSE4Uafo9GKuTlLTt70GWASmYnc7kwcZ+HqUKNdLGO1Uhybq7JWK7CvrFZNnWEqJsOASZI+WfZe3aBQ+N5bCDVgLdu6yLi91BvOmhAfzLMhvYgJ2eLfOqh00xmay+4l6vjSCay1ctV/YqirDN1SqysmVTEx5bOKALJiZkijgQcm6jfwG7aFMsN/BEvXQEfC+U6NwzGOTlbIVOqM7DFy4mpAp2GzsZUiBuGktSaNrad5OB4jkdOzVGomfTEgqTCPgJenaH2EDGfh0NjdSYzks6IxubOGA5w81CSa7pCTFUbXDOcZGvUy0h2nuFknG8eSXNuVqBpQXxeA48UXL81hTWm8/RIjsFkkJ64nw3tYQ6N5xlqC7JvJMtNG9uIBNxDRkrJ947NsiEVYmShQlfMj0dX55yK8lqjgqRyyZozZYRXx9P2wukQXbEANwy1UW+aJIJepgt1kv48fsPAkh4aOnT4DSzbZq5ocuBsjuGuIFL60HMNbumOcmSqRKFhsX80y56+BKZl4/VoZMtN0qUaW7sjlKpNTs+VKZgLDLcnGJsTGEIylauTr1ncMBDHaDrEpuu8fVsXR6aKHJkqYmhhpvMN8hWTzZ0JuiI+pnNViqbDd4+mSQa97O6N07Adjk4XgDKJoI87N7cT8BoEPRpHJ4tkSk229UQY7ggx1BZmc2fk8rwRiqKsO3Xqq1wy4dHRPC/+6OzuT/Bzdw1zeDLP8XQJXcBozsu5DJTqNptSIfrCATrCfoba/OxJhhkSHobbI2zrjjGVrXJdX5h0vkqlYfHwY+NMnVzAp2t0J72Eo3McmphlPFujVCuTLlYYTqbobwuSqTQo1upIx+HsfJkHxzJ8ea7AbKHO7RsTxAIGliXpiQfYGQrgFYKFSpN0qcFCqUnFdBjP1zkzX+Ln7hom4tdBSuqWxWShxsOn53jizAI3bUwyUT7H4fQYhqahbr6jKK9tKkgqK8pXm+wbzSKlfNEyTyqAHvWtuF4y5OXn7hgm4BHkaybpokM8oBENuD9qfDJdpNRock1PnJpP45lSjduHUwhgPFfl0dMZTs6UcKTEbDPYbzXZ3h3hfTcMsT15DfFAlIYDG9oT3NA3wImZCsdniiA0hKZRNx3SxTq9MT8TpRqn0yXOLFRZKDcZzVY4OZ1n90CceNBHXzLEUFuIsE9HCKg1mlQaNl/cP4blgGHo7t2CSg2u749zTU+Mct2iLRjnjk19zBbrnEgX1/mdUBTlclLNrcqKDF0j6Fn59yMvpCMeYFNnnKNTOa7tCTNTapKvNZktWnh1yFRMxjI1OqJeeuMh8nWLsUyVrSn3VnVHJ0sIodFwBBO5KgvlBl97bpreiJ9IwIfISBqWpFgVNOw6s/kaiZCHOzd3MJ6tMpYtEzA0bhpu4z3X9RLweeiPB/irB09zer5CNOSjK+7l4ESBiKER9XtwLJuAR8dybB46uYDAYWN7mI3tISSC99zQz2On53ni3DwDiRhRb5TNHU28hjrPVJTXMhUklRWFfQY7ele/V+v5bGwPE/fpbGsLEw57MGWFjW0h9o1m6IkF8OiC2VKTDfEQdsWkUK5xbqFCVyTAh28foGlPMJgKMpWtcmpO8o9Pn8MUFn3xbvJVm5+8eZCzsyUaFvQmAuwNLpCvN9A0cKREExpT2SqFusXJuQXetFvjyWMBSk2Lvpifg8dnmWnYdCf8TBfqVA2dmmUT8urs7ovTHfVzIl3ke8fS9MUD7NnQRrpYpda03F888bqBMRHyrmWVK4pyBVJBUllz8aCX+4bamJ4u8lghh2mWGJ8uc21njLGyRZvHYK5UZ7gjxKlSnTNHq/i8OtlSha/sO8tM0aJUa5KKevEJHa/fIGrUieuC6brJV/ZPojng1QXHZorcvCHJt46kqTZtbtqQoiPs46GTM5yaq1KtS/73M2X8wkvEq3NuoYLPkgy2BRiIe6kYFnYwRMN0cCQ4EspNm3jAR8BnsLU7wDPjOR47NU/Ia9B0HH71zddwbKaAtBaYL2tc299DW3jl5mdFUV7dVJBU1sU9r+unUDPZPjPJl/YX2WSEmFuYJ60n0TWdwbYgAY/BG4aSNGdKnAvqmNkM+56bp72/n31jWdrDfhaqTaSUWH44OVshEQ5yZDpPt2bg9Wm0J4NEyyav747y3tuGqFo2J2eKzJdNtvfEGF2oMBxtZ3tvjIdPzeNrODhhnQ/fMUz65FlsvUK1t5dEwMdzU3nu2Jzi2EyBsUIaTXOYqGXJVHr5wJ5BxjMlpkqzdMQ0Yn4v1UqOtmCEsF8dRoryWqWObmVdOI7k9GyJZ6dsHK2Hd985xHf2HmI04+NkusB80aRSN+lLBGl6BNlsjWzNoEiUfp9BMuhFAEGvwLQ1eroF5xZmaWaT3Loxie5tMDVrcWAkwzNC4/039PGxz+/H0MFv6Pi8Otu6okR8OvmaybPjeXy6RtinoWkaI/MV9uY9xEPdtFdMDo3lCXh1dE2jVLfoikTYP1LBJzawpz/CwbEc92zr4NZYie64QdwfgPgeJnNVchWTrthL679VFOXKpoKksi4sR5ItN+lPhtjVG8fy+OnsN9AzBaqmQ90GEJycK9MbC2DqAkuDrrgfQ0A04GEyX6PadDAtOJOGUiNAzA8nZotI3ySDZichW1LE4uh8iY0hL7pHwxPwki5V+N6pEeL+KDgCjw6VhkW6VMdn6AR9Gtf2Rfnm4RlMK0si7KOrM0u27uPj927h8dMLlGtpkODgcG1vjHfs7iXiHwLce9Z2x/xUGjYe3bmcVa0oyjpSQVJZF15D4007uwA4N18mXahxTf9u3iLz2ExTb5rcOtzJgYkso/NVyg0LywGfoYEQ6JrGju4oM/kaNdPi2p4YxabDUCrI4fE8PtHP8WqTzf1xIn4vT55bYMjQ8To6r9vRzrePVDEMm198wzB//u1THJsps6UrjKEJMuUmX3xqnJ+6aYAAAunRuGEgAbrOPz+dYe+pBl0xP8W6zW3DbRQch3nbIuL3AO7ddg5N5DG0BFu71I0EFOW1TAVJZV00GrNUa2nKzmY2tocBGF2ocGymxHBHmLdGczxXyRP2GdQtG0c6OA5U6g32jdSI+D2EfQYCQbFucTRdIuA1yFVNbt2cYjAe5LOPneXMbInXD6fY1RdjIltl3rI5+ug43UmbvliUoMcgFfExnq2Qztep1G2kAxG/zuPHMnT6vOQ1yWypznwRgl4vpak5OmoGw+1dnFso81N3b8Tjff5QEUIQ8hrUTPtyVa+iKK8QFSSVdaFpXmqmlxNzRbpjfgxdoz8Z5N6tHRys1Jg2IhxNZ6g1LTrCXiZMB4HJQtnBBj56ez+Pnl5grlhjuD1Me8jLXLlBLOhnZLbCV/aNoXlKJHxtHJstsa0Phro1GrUkJ9MlDF+O8VyGB/aO88S5BfyGwJGSqikJ6NATD3E0XcQwimgY7BncysHxPBGvhm77yQE/e/MAI5kqbYaHHV0vnA5z++YUHv3Ft9tpjBcRHh1vd+iCdeRUKqBpaIHABfMqymtNs9lgZHTkchfjglSQVNaFx5Ogpz1BT/vzabom2NgR4eSpGhs649ibDL68f4KBtjDhgMcNbjjkqjaVpsl7r+vl04+eJVNuslBqUG6adFiS/kQAjw7xqENnwE+jCYfHKzgSruuT9CWCTOU05gs1JhdmCRmCWCiAgYMeF4Dg3HwZISUhXwBNQHc8gH+mxKm5Mh3xABtTIb55ZJZCLkehWMbrLeLzGGTyfnb0xla9iYCR8CNWCJ4rqZ88iTAMArt2rUGNK8qrTRxNvGsdtvvJNd2aul2I8oop1Eyemyzw7u1dNJoO/ckguwbi3LY5xU/fOcxP3jyIFDper84jpzJ86eAktgOagFzVpFQHn9FE6kU2dyWw6h00TI3uZADbtmkLhvB7dSqmBRJevzFJLODB6/ege6Bq2QgBA0n3xgKaJmgLJOiMJBnLVPjx63vIVhtMF6e4b5dASslwoMjN7U0OjGc4PJnl9HwJ21l9oI4e8aIFPRdVH4HrrsO/c+daVa+iKOtAXUkqr5iwz6A/GSTsM9jVFwcgEvCQKTe4pitCVyzAXLnBmZki/W1BqqZDZ8zHaKZKZ0hiOw7pcoXhXoPpaoh0vkG9KWlaTYa7IaR5mS/UOT5TojviJ12o4zE0KvUmuapJveYQD+nYaDQtB48uaNo2vYkQ3zg0zal0kev7kgy1dzM2EyJdyPOBe27j1FyZN2/toJqd4/D0KB69DwC7XEFogpqcIRDoR9Mu7Q48QlPnqIpypVNHqfKK0TXBpo4wQggKNZPvHk3TE/ezZyhJptLkocNnuWcwwDW9ca4fTLK5M8JgMsQHb+yj6cDGVJiYJ0FQDmBogr6kn9cPt1Gt2+SLUQJeP+lijZhfx+PRCPsN/q/bNhLQNDyAzwMbIn4GdI2Iz6BQtZjIVHEcSbHR5Mh0EY/HvV/suYUaddNmQ3uQZMjLY6cXsH1hbt29CaP1u5GN06eonzpJvT6DZZVe9HqdWu0VrmFFUdaaCpLKZRHxGWzrjuJtBZxT6RIRv59o0Mdbd3Zj2pKIz8N8uUlH1E8g4KEpBcmwlydGsqQLNSJBH7Ggh7olQQhM0yZfNQl4PLSFvAymwnzwpgHeuKMHj65hmiB0gR402D2QYCAZJOzT+eHJOXb2xLluIEGuVqene5yBNoM7N7cjhM5dWzu4ti9GLBwi1tH1o9cQ2L2bwK7dtLXdjtfb9oLXZ2UylH7wA6RlrVsdNkZGqD777LptX1EU1dyqXCaaJuhPBn/0/ObhNsLeTjRNkC7U2D+aQxPwph1dvG1XDyfSJfaPZNg1kORMuoQtHWIBH6dmy3g9Ops7A/RFA8yVgtwy3M5Ctcmtw20slBvcuCHJd49MEwvpnMnVOJGpsK0rSthvUKpKenAQhTn6O/ox8OHUtuIJ+7Adwb7RLLdtSjGUevFo1fM1l+rJJOG77kIY63eIGYmEGhmrKOtMBUnlihBtTdQvNyy+ezTNDQMx2iM+iu6teXjTjm76kkFOzJSIBg38HoMbBhM8eXaB9+3p5+GJR/nmwQYxo52bh6/h0HiOx88s8LVD04R9Hu7d3kXdtDg5W6Y/HuTwdJ6ARycV9mJRo1xrMJGpcnyhin9OI+Txs7EtSG/cz7ePpHnLzi70S/iFZSEEeji8LnW1SI/HUTfDU5SLJ4TYCnxxSdJG4HeklH+52joqSCpXlLDPIBbwMJ6t8fpNz88f2dgRpj8Z5I3bu8iUG5i2xO/RqZs2tYbN9e27sAoltnTFKVRNZgoNDk6dY3d/hGIpRl8iyJauCEG/wRNnMu7zzgjjmQq2L8XrujdwcDJPf1xjMO7wvj19bOqIIKUkFvReUoBUFOXKJKU8CVwHIITQgSngX863jgqSyhXnjTu6MC35grTJbI3pfA2PLuhNBHCkQyzoYXNHhK8dmuJNO7u4461DzBdrPDWSpS3sZVtnDzu6o0z7HL55eIZ37OqlULW5aShJW8hHrtrg3u2dPHR8jol8lRu6Atwy2MVMDdj3FPZte9DjcVLqZ7AU5bXoXuCslHLsfJlUkFSuOEGvActmU2zviZKtNAh6DTZ1PH+/1ICusX8sS6Zm0m5a7roSNrVHODCaZb9Vwe81aA/7ma80KFQa3L6pl+8eS3N0sohEI+g3cBxJTJe0Gya337SF5jmJFnq+H7JWm8JjxNGNIEKoq0pFeQ34CeALF8qkgqTyqtEdD9Ad8//oubVQQ6bL3L6pnRnbwgroFGYqDLaFuH1LimfGczQdm/aQj3dd18vesxmG2sK8aUcnhybybL8pyvaeGPGAh55EgL97YhQj1YMQAt/w8Av2Xa9PUZo6RzS5HX/P8yNZM+UGQgiSoUubI6koyrpJCSH2L3n+GSnlZ5ZmEEJ4gXcC/+lCG1NBUrkiSSmpVs8SCAyyb7RIZ9TPcPsLB8LoCT+hgMGNIQ9SSqSU/B/23ivYrivN7/utHU/ONyfkRBBgAHOHITtPj0bTrVGNZEszsqokP6n8KldZD/aD/ahRlccuta2ZsSxPaI00PZJ6RHY3e9jMJEjkDNyLm9O5J5+z897LDwcNEE2AuCABECD2j8XCxQ5rr32w7/nvtdb3/b8Pmx5PTJVIGRrfPThK0/JRBCzUbXYOZnHDiBOLLbZUUvy3z0xxZL6JF/TTTf7hc1sZyt14arVYfJoN5w2CdA24JpLLTQdVIRbJmJj7hw0p5aFbHPMd4IiUcu1WjcUiGXOfEuE4KxhGhYlSisINrN6EKlCSGkEQ8NbqWxysHOSpLaWr+weyJobaryLy3UdH0DSFhbpFx/EZyJromspoIcnsRo+G5TFevJaSImV/TfSjU6vl4S99bKr10fHrjc9jYmIeCP4+m5hqhVgkY+5ThFApl78MwGjh5sd5821QFbbkt5Axrh9p6t0aSrONmhpCBCFoytXczH2jfXGbKKWuy9e82u7clWoeY9fajNciY2IefIQQKeAbwH+/meNjkYy5Z8hIgpQI9c4ZPekjaRCCKaMfzLPUtMkmNHIJnWQuR8qbZVBRcWckqX3lW7T2kXYHU7DJah4xMTEPDlJKi4+umdyCWCRj7hneShfpBCS2F+9Ym4p5/SO80rQJs4m+SGayCG0bQugYI58wHL1Ru5us5BETE/PFJhbJmHuGMZTujybvIoc+siYJkEiM3PC4MHRw3RVSqa13tT8xMTEPNrHBecw9Q2gKivH5GKmdWWmx3nGu/r3aqlJrL34ufQHwvA2arWObPl7KkEbzfYKgdxd7FRMT86vEIhnzwHN2pc3xhcYnHiOu/PdLljppOuEBwrs8sr1pf4SOqt6OOblAVRIIcevJn97hw3gLC5++czExMVeJRTLmgWckn7gufeNG7B3JMZC9lgP55FQJVVV4d7p2t7t3Q3Q9Tzaze9PHC6GQyx1AVW9tkWeMjqKVNx+kFBMTc3PiNcmYB55C6tMl8m+tpBn+iIPP3cC2F9D1Epr28VJbQRTgRz5J7c6Wu9LHxu5oezExDzPxSPIhJXIC3JnmXQ+kuZ9YazvXrUsamkI+eXejWB13Fd+/8VTwdHOaY+ubX5f8rEgpiSz/nl0vJuaLQDySfEgRqgKmCg9oKuCNHHFuRa3roSkwmL390aNr9bBaTYojtzdKKxaeuum+HYUdBDK47b58WqKOhzvbJvloJTZGiInZJPFI8iFF6ArmWPaB/bK8uN7lndtcT9w3mmP7YBo3dG/7ep5tY7Vbt33eJ6EqKuYm1hjv2PVyJsl95Qf23zwm5vMgFsmYB5LJUoo9I7kb7osil3b7NFJGH9t3qXmJI2tHPrb98OU6a23nY9t/SbZcYWz3vk/f4Y9gtZrMnjhydTT8aYksi7Dbva1zhBb/ysfE3A7xdGvMA0lCV0noN865jCKfIOggZYQQ14vCjsIOvMj72DmljEHavDe/DkYqTa4y+JlHdO7MDJHnkdi1GyUd17mMibkbxK+VMQ88YdsjbF+bQtW0DKXSsyjKx0VPVdQbRpNuH8iQuSKSH659SNWq3rX+arpOaXT8U58fWRYyDEk88gjJRx+l+/ovCDc2+vt6PaTn4S0v46+t3/B8KSW93iWiG7wsxMTEXE8skjEPPKHlEfQ8ut0LSBl+5vaswKLjdu5Az26Nv7JC9403buuc1suvYJ84gRACRdfJvvgiaqUCgHXiBO7ly0SdLlGv785jnzyJv7p69XwZeLQvHibw7809xsQ8yMQiGfPAYwxn0AYNXHeVMLz5uuJmGUuPkdA+ff5k5HnIYHNRq1qlgrlt2033+9HHUzb08XHUcpnIcZC+j5JMEvUsWq+8AgjUYhF1oII3P09QraJkswjzIwFCXkC2NwlrPTp/89pt3l1MzMNFvCYZ80CyUO9haiqDub6YqapJufyVO9L2tsLNRWsz2MeOoaTSJPc/cstjha7fNPl/obPAxcZFXpp86brtmaf7aSW9998n1DSC4UFSXYvW2iLG2bN4a6t4ly+jaBrmvr2YW7YQ1OuEzSZqoYCSTpN+/nmCtTUSe653/ZFS4s3Ooo+MoCQ2/6IQOQ69N98k9eyzqJnMrU+IiXlAiEUy5oGk4wZEHw9evSWfJr/yk4hcF+l5qNns1W3JgwcRyvWTNGu9NQZSAyhi85M3Y5kxcsa1CF4Zhv16nJpG0GiQeOQRLKtHd3GBZK7AjFpj15efJ5kZILIslFyO3jvv0otC1FyOzltvk3vpRdLPPIO3tETzhz+k9Lu/d901rXffw1teJvOl1G2JpDBNzN27UdIfdxaKiXmQiUXyIWVttkV+IEUifeccZ86utIgieGQsf8favBn7Rj7dNc7UzhBEAQcHD96RfrgzM0TtNulnnrm6TTGvz32MZMSJjRM8Pvg4lWRl020rQiFvXrtP5/RpQssi9cQT1F79Gc1UgrHhMYqWTfv9Egp13AAAIABJREFUD9k22yQ8+WOi73+P2p/8KVG7TfF730MqgnCjhr+wQL3dAlXFOnoMv9HEuXgBY3jo2jVLRYLTpz52D7dCCIExMXFb58TEPAjcdZEUQswCHSAEAinlobt9zZhbI4TgTmcMDGQTfMbUv7vOVG6K8A4E9/ySxJ493GpIqwiFr09+/Zaj13bNJgwkxaEbm7Wbu3djHzlC60d/heI65Moleh98QO6rX8WYnKDzk58gEgmab75FVKuBaeLOzOCcO4exbRuh4+CdOEHddVGKJcJ2m+7PXiX7wgtIxwEhELqOVqngzs1h+D7awMCn/mxiYr4I3KuR5ItSyo17dK2YTTA4deNE/M9CJXNv3GOklJ96ujRj9NfLvNCj4TQYSg/d4oxPRggB6q1rZG66v58guIppknziCawzZzEmJ8ht3U7jyDGq/8f/ibe40DcXuHwZb2EObBvS6X6kK+AsLoJtIzQNf3GJ6Nx5tOFhkt/+FvaJE8gwRKgaImGiDw2hJJOgxRNNMTFxdGvMA4V76RLW++9/5nZWe6vMtGY2ffzKpQuszVy6btvFtQ4vn1qh49wZ0/BcOUlx5JODXhTTxNwyhYwiwm6PzDe+ibFjO6EfQCYDQoAuIZdBHxsj9Dz89TWYne2LXhQRmSbZb34DbWqS9is/YfVf/j6R7WCfO0vv7XewjhzBmJpCKxbvyH3FxDzI3ItXRQn8RAghgX8tpfzBPbhmzBcUfWwMtVT6zO1M5iaZzE1u+vjC8Mh1RZsBem5A2tQwtVuPJD8tketeXR90Fxdxz58n7HZp/fRnKNks9pnTRMsr6Nu3IVWtL4RNG1QF//jx/shU6ffbqS4jNI3c1BRYNqk9e2n85V+ilcv03nsP5/x51HIZc+dOvJkZtIFBOq/+jNy3voVaKNy1e4yJuZ+5FyL5gpRyWQgxCPxUCHFOSvn6L3cKIf4p8E8BJic3/6UV83CiJJP9qcB7TDKT/di2xyY/eaTVdnwMVbnOPi9yXZByU5GjkW3T+dmrZF78NRTDoPaD/4vuiRNkn3sOb34OJZFENU0iXceqX0RvA2oCggC8K3ma2Sy6okCziSIFqmbinjsHVo/2a69h7NiBMAyUVApteAjVNJFBgDM9TTKdRikWUXI5wm4PYegoxqer3RkT86By16dbpZTLV/5cB/4SePpX9v9ASnlISnloIA4SiLmHhKF701qPd4LTSy2mq9cbkDtnz+GcObOp85VkksS+vdgnT2IdP07u9/4BYT5D85VXCGlh7N+F9ANwXTQ9CYUcdDrQ64Fh9EeR7TY0m0RhiJpMoaJg7txB5nd+hzCZJGy3wfNw5ueJLJvO0WPYJ07gnL+Ae+kSmeefp/P+YTb+4A/ovvnm3fiYYmLua+7qSFIIkQYUKWXnys/fBP6Xu3nNmJjNYttzeF6NUum5u9L+01vLKALOLLew/ZAnp0okH93PZkKAw3Yb+/x5Wi+/QualF3HefIu65tH1WmTCgKjWwT5/nqheBykxx/fjnTlD4PtYYwPkGl2cdBLPdUlZDtbwAOHAIMXTZ3EvXGT9tZ9jZDOI6Wn84WE4fx41myVcWcHrdPAuX6b3/vv9dJF338NbWyN47TXMXbswhoZwLl0isWsXQlVZ663RdJvsLvWNCSLHwTpyhNSTT952KklMzP3G3Z5uHQL+8kpknwb8iZTy5bt8zZiYTZFO7ySd3rmpY+2LDfSBJFph8wn26pW1wPFSiiC8YmLwCZGwlm9hqiaqouLNzuLMzhKpPtojW/BffgXeeYeMDEk+eoBgeRklkSPIu4RSYi9cRs0k0KqQanWhUCCRTKGoCtryCtmOhUhZkExCp03qzbcxd+0iUlVYXwffR9u1EyWfQ83l0XfuIGqu0ph+m8zoOOb+/XReebnvxlMqETZbVyJiVVRFRfuImbzQNLRSCRFHx8Z8AbirT7GUcga4M1nbMTF3mNtJI9HLSdTMp1uPyyU2Z9hwdP0oAzLFkGWRGt6JOHGC3rnDOP+mjrLmEdU26JRS0Oti7NxJZs8e3LNn6B49TKRF0OiiDg8TNhr4tTpJWcPI5xG5HOrICGG1SihlXwSbTaKTp2B8HFot0t//HuGlS/gbNfyVVeh2UUdK6FqyX3Fk6xb8vXsJ1tdp/fSnGFNbUAyDoFoltbJC5cCBq/chNK2fPxoT8wUgTgGJifkIs61ZOt7Hq2NopcRdL1h8aPgQw65K7Uf/nvp/+itEsUjxG38f8cEG3vnzKJpOvuPBuXP458/Rff11At8HXUdf3ECtNqHRwDRNElEEYQj1OtJxCE+dwquuEyXNfmCPohAUQny5ir5rF9YvrrRld1HSafxkkt6ZadSuiVooEtkO+D5Bq0339TdQC30nIKHr+Gvr9A4fvnofq9MXaayuABB2Orgzl+/q5xYTczeJ50NivhA0LY9a12X74MejUG+G5Vscrx7nyaEnMdT+KLHhNkjpKbLGbbTTatJtNBjcsvW2+iyjiKjdRi0UCGo1/AsXSGzditnO0vjZX2COj+OvrCDpe6PKjY1+iocaIRtN3OoGSiaDIlQi/4qLkBCYe/YQ+h7BpWnskRHag0Pkzp8hiCTZIIRUiqisIQlBjfCnp8EwCGrLIBzy3/8e6xcvoJ4/j3X0KEIREIUoxSK573yH5PZtGFci0ZVcDmNsFOfUKVKHDiGEIKHp6Feid8NWi6BRx6T/2XTfegtjcjK2sIt5YIhFMuYLgeNHdN3bs5vTVZ1SonTdetrjg4/f9Pj60iJRFFKZmLpue980/fbd1oPVVexTp8h985uIdBqtUsG5cBFveRnZbOInE0hVRSgKURDAyDBsbIBlIcbGCOdmodeFQhFlcoKo1SKx/1G85WW6jRr+yBC5KCTl2AQ7d8DMLCQl5qF9uKdOoHQFxvbdeO4CRCGUhiFUcA5/QLrXQ336aczBQQq//dsoQuDMzKAAma/0q60EGxu0fvITnIsXyb74Is6ZMyT27UObm0cbG4NCEe/yLPpHvGHN7dvvSJ5rzIOPdEPcmdbn3Y1bEotkzBeC4XyC4fytg2qCoIfvN0gmx9EV/WpE5mbQkwlk+HExTBeKpAu3706jj45e9Ub1l5bwV1YIlleIkChDQ6BphE4H0Wyjjo0SdrqQSoHjIldXUUpFaLSg1yP59FMES8skxsfIffvbhL//L0m5HvrgIEG9ge776LpOFAWEa13Su5/G3LeX+p/8CaxXQdNQR0YJa03s8BIYOsnJCdRUEmEY9N54g8iy0bJZtCsFntVSicyXvoR9/ATe7BzOqVPoW7YQNBpEQYgMArTREYypay8V2tAQp5Za7BxSr8sfjXn4SBspnpu8+Uvp/UIskpskbLlIAWrG6E8/xdxxPk05qdvF9xs4zjLJ5Phtn5stbb6Cx2aQvn/VH9W9cBGiEG9hASWRRPo+1d4afq/NcE8jrNXAcUHXQdMQY2OYhQJOOA3VKr233yH33V+n/E/+CfU//n8wxscpfO1rLLWbOIc/YOLZ51BVDfvyDPbb79AzF1DPnKKXNMmPjMDqKuHFi5DNou3Zg7++hj86jJYt0HvrLVKP97/M1FQKd3oa69Rp9KFB/OVlzEf3oxomQgjCjQ2MLVtRUkmEoqBks0S2jdB1lEQCZ36R9koPv5KORTLmgSAWyU0SNGzc+Q7m1jxKQkMtmHTeXAJVoBcT6CMZFFNFzcaOJJ+GSEac3DjJY4OP3VY5qdslmRz/VAL5afBDH129eWRr6623+dBN8uSXHyNcW8PdqOLPXkaoClouR/7cMqEHpAxod8BxruZYymoVMTKCKBaRtRqzmknh2EnC//V/QymVkIZJ0Gqj12r0zp+ls1HH2L4d5913IQgwy5NQa5DzPUSxhBQCej2UfJ7C44+Tfvop1EwGZ2GR5v/7b3Hn5si9+BJC16n/8IfoY2OY27cRBSGKmSD93HMY41eKR18/G03v8AcoqSQyCJC+z6HhCuYmI35jYj5v4ujWmyClpPvhGgszdY7+h7M4C21EQkPNmvROVum8tUTY84j8CCVrIN3wWvBEzG2jCIWvTX7trgrkrxK5Ic5084ZTqJvFdasEwcejYS3f4qdzP8XyrRueJ6XEKBTYeWAnKRVy3/8e/vQMQacDjos2MoIxPo4J/dFmEFwzITBNaLexj53obzNNMju2kYxCoiCg6/TYMBRCyyL82c/JoBBJSdCo94U2CDDHxvEEOEGAu7iAbfZf7tLPPoP0XKwPj+AtLPRLqiWSJJ94guaf/xnL/9O/AMOk8ru/S2r/ftIHD5L9ypevCeSvfj4zl9GGBkns24dz6jTS8wg7XTb+6N9inT7H6sv/FXmDyieRZeGcv3C1SHZMzOdFPJK8CUIIRBghNizo+Bj7ktiLHTi7gVZO0vtgFb/ewxjMknqkTGQFJMY+uYLDw8xmylt92vJXnxahCkRC5ZMKawaR5M1ml8eySQr6x39dbHseTcuR+RVv15Se4oWxF0jpN64NGVkWp1ePMjRgMv2LGXLTcwS9LjKSBI6Dnk4jARSlP8WaycBGFRQV8nnUgSGk6xG16uC6VKYvgaritJukv/IVhLdKsLREcvt2/NlZkJLg1Om+wIYh7odHUHyPSCh4ikLkOiR9H+vYccJOl7DbQFu/QP6JF8k8/zzZp55CN038Wg1+ZTpc2ja9w4dJPfHEx8wSpIBwZYWw3SH9wvMIRUEGITJM0j1yirpfx3znbYovfOn6z8d1Cdv3f1BHzBefWCRvQtD1UAdTjIymKRdSKAUTsWJhTddREwb+Rg/sgMALsM7UMAdS2BfqJHeVmLddql7Ak/n0530b9w2vnl1n32iO0cK9Nye/GUJTMMc+OdVDFTBsaqTUG0+6FApP3vTcvJm/6T7n7Fkyvo56/jKF2UXckyfxV1bxVlewigXyi4vQbEI22/djbbWulcKKIiKrS2L7dqQ7iHPhAgQBwjRQdAP7/cOEnkdPFYjBYZRKmajThUya7O/9Izqzl+D4abSZGTTAp1+qh3SSsF4jzOcRExWUUCFsNkkdPED39TdIPfM0hu+DohA5DkoigXGlbFfU7d3wZcMYGaH+2muE7Q7F3/47BBsb6AMDlH/3twjqNfS33yY5METkuqxenqYytQUjmUIrFtGeeuoT/21iYu4FsUj+CqszTZxeQNlUoOYQOj6dny8QKhLZcCChwYgCugpuRLRu4QpBckseNd/3qczexdJJDyqPjufvWVHmO4kQgj3pvrB333wTc9cu9MHBz9xu8sABxm2boFaDsTGcD4/0R4itFolaHbFjJ7LV6gukEJBOIyoV/Lk5HBVS1Sp2r0f5H/4DEk8/TeuHf46UEW7SQDSaqJOToCjU2z065UFyfkQ5nabz4WG4PEfymWewLQtWV9H37IJWh2WzS8I02fv976OWijT/7M+JDiaxVleILk4jwwC1UCBsNCCSZL/+Ndo//mtEOkX66afx5+fxq1XSHxE3JZG4EsUboA8NYYxdm5Y1hofRf/M3qdoBa68fJm1XkZNTN/q4YmI+N2KR/BV0UyMMJNIPIZQ4My2UkklwqQmKBMvHv9hCLSaQowZRvYc2lMS93MKcyqMVEyQud8gNpeD+GTR97gzlNu95er9iTEyg3aG6iophkNi5E0c3sN57F9lsIleWUTQNUwjkygpYV9Yz02koFpHr66i+j0yaiMExsFxq/+YPUXI5AtEiMgSiLZC2DUePQTpDKZ3CSJpoCkTtNr5toXe62G+8TrS2jlIsQm0NHJ8ol0JRVJo//CGiXEL2etSnLyF0ja2/898gVBUllSLyfdwzZ4k8D2EYaPkChCHa4CDiBiXAknv3og0N3dDLVWgaqgghX2Ty0H60VH/2xXcdFs+eYnzP/qvGBDExnwexSP4KxYEkbtfDa1p0fr4IpoC0Dt0QMoAHCAibFoQSpMC91ETdP4A920QkFKTtI5LxR/tF46P5fp+GnhugyAhdAe/cOdy5ebSBAezpafwwBDOB26qhDBbRfdFfPyyVYG0N5uYglUIkdcItabxtRXIradRyCevMWdSGRHEsREYD/0otyYEyqBqFyQmCWp1gdAQn8DBME391FQkoQQC2i0ylGZMlQj/CXZlGtW1Se/eS1RPkXnoRNZe7tmbsB9jNOnoQkHrqKaJuB6dQpB2EqHaPZHWd3EB/tL02c4lkLkfuI9VA/KUl3Pl5Ms/1q6+UMyZJxUE6NtB/CVFVjQQKKzMXSWazZJod9PFxtOLt56PGxHwW4m/yj+AutIncEH+th7PcA+9KtKp15Uvno6UBXdmPDY4k6kQCcyJP0LBxplsktheI3AChiLvu9xlz72i5LRa7izxSfmRTx0e2jTBNhNJ/Bk4utXA2VtjqNygmdfy5WbrvvYv91tvgujAyguL5iAhELos2Po6Wz2F3u2DbqBNjePUaChUSqV0Uf+sQvdNnwHFwEylMVe/XkJQSMTaOtPqRrPaHR9CHhkgXiljnziNXltEMA7Zu7U/lZtOE+RxpI0nU7SJGR5CHnqBTr5PfuxdtyxSr0xcZ2rodRVVRM2l6QwOE1TUKZpLQt6l6AWueT35pgWwyTbYygBACxfNwL00T5Atoej+CVilVMK4ErbqWRXV+lqHJCYL1KvrICADO4gLz/+U/waOPcOClbyG6Nk6vC0gyxdixJ+beEYvkR9EVvMtNpBsSrPcgrUE3AEFfEOWV/39JToFOhAwjQj+gtdajdGgExVDxlrvopST6wI2jG2M+P8IoRFVuvG4sI4k330YfyRDognUvYDzR/3K/3XSE3vvvY0xMYG7bBsCTkwWikopzqkHr/Bv4S8sEp6ZheLhfriqVQi9WoNlGdh38Xge77mDs30d05Bjh809xKpjheXcM+60PCLfuxdyyhUslnZUtFZ5tJJC+j1utImdnrwpmqEAI+AuLhIoCiUTfxq5Wg1YLu51AX15GPPEkxo5tNHWNYiJFIuGQ3P8IURTRPXaMfBCRulLdY3zPPoRQ6PUu4hp1tqZMKnaTy2KOxFKOYGwCfXiY4tAIc9OXOPe//z6P/cb30COJc6FG8oUDtE6dIsznqC0tEHba5COonTnF6V+8SoQkMTlBSkpqSwukiyXqy4soqkKmWMJzbIxEvJ4Rc/sIIQrA/w3sp/+N/o+llO/c7PhYJOl/+QVVC+dyC2+5R+LRAbSVHn7d7h/wy/RHE3C5JpSdCGU0hTaUpvvmEkJKwrUunq6SfKSCcpOIyJjPj8XOIhcaF3hp8qWbH6QqCAXWvIDpnnNVJAuJAoXE5tck0089dd0anVxdwT17jtzXv4Z+Jk/18B8TZTKEMzP9AJ3z5/vpHtu2gabSuHSBlAyIrGkoFqHr8GuHfgOt3qb3819Q+3f/DrIZipeWKJaGCGo1It+HKMLzfXRAGAam5yGmZ+jkMwhNI4gkWhjC2jqEIevbdqC6HSoXTiF6U2QiibBczBefpNZcg6bO1pe+jpK8JkquZRG4LuniNghzXHr/HYZ37SQ/OEBm5wHcmVm0gQHW1ldJ79nDjtFx0pOTRO02l+3jeG8u4c5cxjcNIk3HGx6hN7+IsnsnC+dOAQoT+x5h68RWgjNnae3cTmttldHd+5g7dZyF0yd44rt/m1Qmd7uPQEzMvwJellL+thDCAD5xJPPQi2TYdumd2kCtJIjaLjKKsE9XCXtBXww1+sIIkNHACa6dGwLLFp4XkXq0gl5JIQwVNakjFIGMZGxhd58xkh65ae4igFAE5kQ/LWRCV5lI3NxByd+wUbMGinltVCqlJFhbQx8eRkldfx19bKzv1RoEBEurZJ9/HiJJ/c//HN/3+4n+igL1OiQS6KaJomX75gLjA5hSoGy06L75JmqxiD46inP8OIbQ0Js2olwmOHOGVhDgJ3TyocTPZYiiiEy9SQIFPYgIDZ2w1UYmk4QKDEQRncoAiVYNUmncVIr83r1s/PEfsq6YiL/3jznw9NMovQ6y08JIpbHbbVyrS7ZcQVMzWO0WURAxtfPXiTyPMNPE6nbIVgZoLC8y+Vjf1u6dn7/M5WMf0lpbJVUoMrR1GxszF1F0HXVqnGwuz94vv0SvWUO6Aa+//CMi12VgaQ5N0Tn5Nz9leMdu5k4cxbMs9n3lJcIgQFEVRnbc3IfXarcQQpDMxqL6MCOEyAFfAf4RgJTSox9pclMeWpGUUoIEv2YTdl3QFcJA4rVdaLr9dUgBttLXyhTAxjWBjEQ/bocQ1LaHBPylLtpYGiEhWLcIGi7JPaVNJdLH3BtURaWUuPWaVrd7kTC0yecPfGyf0+2yPnuJgeQkiq7AR0QyarWwjx1De+klhHG9wAohEKaJfeo01vHjqFdSP5SBAfR8Dk1REJkM1vnzYHdI7smiN3PohQKZr3yZqLqBc+Y09vQ0uC7mgQOgqWi792Du30/31VfBdbHHJkgvzKEnEmg9i9B1QdfQuz20AwegXsMeqmIIE+3xrzJtd0n5kvzzB+lePE9vbo7w2WcpPf0NFKtHFY21Votc0H+J9ByHgckthKHNuR/9BZUt2znw9W8DUF9eRAJL60t0z51gz5d+jbmTx2itr6FoKhffe5tOrUa6XMTttPEtG0UIAsemNLWV+ZPHqC0vUT15jGTHRu7chpkvYEQm6xenaTQW2Zi7jNANli+dp7a4QKZSZsvBQ/SaDbLlASoTU/3PWlEQQhD4PrPHj5DK5dly8InP9gDFPOhsA6rAHwkhDgIfAv+DlLJ3sxMeSpEMOh7O+RoylKg5g6jtk9hWxD1Xh5YHmgpRfySpZhQcQ4Il+1nXAAooEaj09ys5E2+hS3Iii6ppJHcVQVFQ0jphx8Oda5PcV45HlQ8Qplkhivwb7tMMg2SuQGKi+LGXH7VQIPutb33yS5Gh01paIpyZxut2MU+dRo5m8fePkKhKzG3bkHYXZ+MyMpNBKRbovP02qm70TdHDEFQV69TJvvuNAG/6Etg2YmKCXd/9ddr/+gcwPIxoNtFsB2R/zSA4fx5yWczBvYTLp1FOHCWzZYoR3aT96s8wBgaZ/Po3UPN57OFhtu/czVS9RmJqCnd2llD6zM3PYiQNet57dLpVZDXD2i9eJb3/CWpdiwlTI1MsMbxtF6XhUR7/5ndZn5shCkP2fulFLn3wDgNbttJYWSE/NESyUCBdKFJfWmTp3Glsq0fHdenqgoxjkytX6HgNyCnk1UHqy0sIoFVdJwoDUvUN/E6H3vwCQT7L4NRWMsUSozt2UxwdY2N+jvryEkNbt9+pxyPm/qYihPjgI3//gZTyB1d+1oAngH8mpXxPCPGvgH8O/IubNSbuJ2/EQ4cOyQ8++ODWB35G7IsNpJR4a13c2SaK0DAmMkgFrMNroAki24dO0F+HtLg29RrQV0dx5WcTSGsIQyd9cIDsC2MEKxbGaAahK8hIEnY8tPyDl0i/GbyVHmpWR81szti95/douS1GM6N3uWf3F/bx4+hTU1jHjtF+/Q06P/oRYmCAtoDsRo3ICInG8uipMnK9QWrPQaJmE0dX6QUBLCyg79qF2u7QWF2iXG3gygivWCBXb0I6wYWxMuWlGilNJ9mz+76uUYQfhehBX1gJQ9qVCkY+T2JhFtI5UBR8z2W1UqCydTtJz8d7/ACFL3+FjGzTnKuzvDjP1seeBKFgabBy7ixCgy0HnuLcq6+RGx2imkzjLq/w2O6dlLZvZ23mEq7VIz80TOD5TB89zNLZU/iei6GbKKpGIptFKgGje0aRQYXawiyd6hqaYdJpNvB7XYxkkjCMGN65E7fTpb62jNvtIhSNRBihqwquppGJJC0FAt9HCMFj3/p12mur6EaCoR07SWVz7Hjq2Tjg5zYQQnwopTx0N9p+fNcB+dof/Oc73m7hm1tu2mchxDDwrpRyy5W/fxn451LK796svYcysiSxvUBia56o6RK1A4xtOZSUjle1+6kbCZX0c6OQVPqjygRXho1AWUOMp5Elk0CX/dlsTUN6IWHPw7nQwF/rIWXftFko4gsrkABE8vqI31tQd+qs9Fau2/bB6gcsd5fvcMfuH4JGg6DZwl9epv3yy3Rffx2CAHVokKJhooyMoIgU2oqPXFyCRBLr9GmCICCdTJMrV0iOjaNMTdHzfKJIILJZEo88QiaVAt+HVhs5qSHViGQY4rs2vm1DGKL7ASgKIQGhlFQzGY6n00yXB7ELuX76STpNrjyIkQlIfPUpBvbtp5BMsTF3lvRQhfXqGh+++wYnj77Hpffexg8CfNtj4dRx1FBhZMsu/NNHGIl8jrz2U068+gq+5yKB+ZPHmT7yHtXZy8hIUhqbQtF1PN9n7fIMjZUq8ycvcu6Nn7N64SzlUoXHvvkddh56hnSphIwkmmnSWa9iRbCenkDXk0jfRS/kcVwX1+5hpxNkywMQSaTvcf6dt7C7Hdob6zRXV5k++gF2u/15Pw4xnyNSylVgQQjxywXsrwFnPumch3K6NbIDOm8uYW7JE9QdeifWQVHQSwlE1iDq+vTeWwFNoT+3KtEfLeIvdFGLJghoz3WxNBjKqeB6oChIK0QxVEI3vC3heJAxNmnqHlk+ftVifHKciezEdfuG0kMUzDvjZHM/EjabCF0j6vYwtm1Hvv4G6BqJRx7Beu0XREEA2SxBvY6WLcHCMp4f4ueziPku5vbtJKemcN56mzOOSyaMGKzXAVDyeZqlEnkp2fU3C4hEElwXGUoE9KuHAAiBlzWppfMUoxCluoauKCQLJbRCCUtKzEIR8/FRRn7rb7Hyi2OsLS6x/uzf4+mwhzR0ssUyumng+WsE0QXC3ggTjz6Gvltjefocumaw9cWX6L32ExKZLLMnjrL98acZ3b2Ptdlpuo06vm2TyuYgCqmdOQlhSLpQwO1GaIZBFPj0ajXOvP4a7foGqqoSBgFSCJqtJqHvk9QzqLqOmirgJLLYZg/V97DqdexOD80wCAIBSJYvXCCRTmN3e+imztK5M/iOTRRFZCvJx1utAAAgAElEQVQDJDOf7N0b84XknwH/35XI1hngv/ukgx86kYy8kLDrI9IqQdMj88wIrTcXCVd6qKMZUnvLOLMtpJSoSR1/tYc2mscYTuMvdyECxVTIqJCSgB32p12ToBYNzB0FFOWhHKB/MqpyU2OFXxXNBwV/bZ3IsTFv4cQjslnm/ut/JlMZIjc6Apk0qCqpbdtx3n+fqNNFvxL1SiJJc0cF17URdg3d1em88jIJBO2JUTJ2h6FWkyCnoHW7tKKQS1u38ujGIqavY/sWRiUNXQXR6uFF4OYLmKrAiwJ0yyfV2MBUBUohD+truLaNsXUbIpdFdsu0PpihePBxwsV5Bv0ul2emKQwNE4Q+u554AU2Hk7/4j6QrgyRTGcZ27WHlwgVqS/Osz84Agk59g8snjlJfXOSJ7/wmvXoNzUjgew5rMxeZ3H8Q33XwnX7ouN1pYaRTjO3dx8zRD4kWZ0mXB9jy1LPMnzmBZ9tkyyUyuSF86RLYNlJIrF4XLZFACEnkhaBAcXQM37Jori4B4NkWk/sPsv3xJxl/5FGaayt4VpdMqXy3H5GY+xAp5TFg01PID923edTzCRo2iqHhzjXx6y7Si8CPcJY7OJdbRN0AVVUIGw5YPsHFBr13l6HpEq628Zc7iEigqqI/JVvUIaHSO17FOlHFW71xoJS/ZuGtdG+4734g8kL86o3rH35WFFPFGMt+oaJ8pe8hHffG+6Qkijy8pSVaP/4xajpLrdvC2dggsWUr+D61P/1TpKZhjI7gr69frRdp2CbFOpQ7Aa7dAyRks+hDw+RyWUQiIjAC3DAgqaocclqYwgDfJ0x5BG4Dw7LRDIPmtm3YgU9bQD6dY0iCIMLSNawgJMxmccfHiAKf6OgRnIUFRD5LFEXMnzrK3OG/wm5soJsJmsuLREHE7PFTtFc9oihi5eI5/ux//h/JD40wsmMXKxfOYbVaKKpKYXCEdKlIMp/Hs21EFGJmMghFYDXqPPHtv8XQ1u3kKgPkBofwLZvKxFYKQ6MkKwMIJCuXLoDj4lkuK44GvZDQctCSJqEfgO+hejbl8XGEpiEiSWViK7mxHMnBgExliGQuh91pEQYh6Xye1uoyiqqTyt28SktMzC956EaSasEkOL2BSGgktxTwVrtIPwJDJarbEIBSSRASETWuBD8I+vZd5QSCCLlyJa0mrYET9f1dLR99JIOTmENGFTRrAiV1rfp6UHdAEaj3cUV2aQeEHS92Cdokxvj4TffZ9iw96zIFfT+JyUlGPI9ISup//Md4rTaiVCKs1SCTwgkDtJTBgrCZ7HTQygMEGw0UGdFO6JRyBZLPPYs2cxkznSeY1ZBKEm/7GOrcLEGthlQVTM0g4xeg0aDvgBFSXF7m8sAgRdfG63RoHHqK4vvvoqaT+EFIz7a5NJCjWR4muxQxFjpkfI/2qWMk8lmUxCLr52r0ljYoS4X1t15nem6awvg4oe9hLzUJu13mj7zP4NAI6ckppo8cxkxnqIyOUxgZY3LfftrrqyyeP4MMJaHvo6fSVGcvs3jmJBLJ3i+9yNzxo8ydOEY6X0Q1dHzbxrMsFEWlXBmg2+ix2rhM6FpUpnaQzGSx2y2yAwMYpsnOZ7/MyvkzVOcu0aktoRg6ZjJBslBCCFi+eAY9YbL90HMkM3Ht15jN8dCJpPQi9OE0kRcidQXF9tErKTwvQuQ18CSR5YGngC/7QqiqIMAYy+IvdmAkSdB18ZFEdkQ68CCnIwPQRRE1MnGXuiR3XjNjDloOWj5xtZzW/YiaN+/r/t2PNJofoqlpstk9121PJqfQ9TJKL8Kdm0MfGsJ3XfxOFz8M0MMIFIXE3kfQkJDScaINeG+WxtwKGctGVSTJfA4FH/vwB7C2xsZAkVQxh/RD9IV5EpaDC/hhhPQCQsvCp/+LLQDftjEVjd7AGFUlYC0MOKSplPbsZX51CavdQkmnKJoq3hNFFuwu2eVFZBBgNdq4SxpOq0V+aoqh4TFyZorg0nkCy2Zh9jLFgUEmtu3AajS5eOE1hg4+gdPrUjZMmhvrdBsbrEyfY2N+lvzAMJliicbKEvMnj2GmUqiGgZQR9eVF/MBDiQKiKKBXr+O0WwhdJ5FKg2EwoilkSzuYmz+G1ayhJ5JkikXygyOsXb5EGPjIKKIwNgpCEPUs8qUyG+urFAYGyA8MY3fbeHaPVC42FYjZHF9okQwaDkHdIbG9gAwlQd3um5j3Arxaj6jlEzkhQd0CXUHW3L5DQKo/rYMHDAgCp4HaTuEt1KEeou0tsU5AYPkUoR+kU1AJNnqEM1mSTxZQCtePGBNbv7iBKQ8z6dRWFOXj6S9CKOh6juab/4XID2i/+Rbrw2WyTzyGOHsONmoQBKwd+xBlqEJp9xiPzAWo4xNUzl8AVeAnTJT1DULPI0CgJUzKjQ5KGOJpGjVDpWQaJIIQUwgIAiTXArEBUFUqC5dZGt/CQMJgT7fDXDJNc7BCvrqKk8pCJkd+ZZXEwASzvsvZn/2EHTt2kRwa4um//XdJZDIcf+WvEek0PU3jsW98k1ShjO2dwmnCk9/6Pd7+iz9lpV5laXYaI53mwrtvENg2erHC4tnTCKARrfLY179NeXyS5fNnQAh836M8OEx1YR6320YzdMpjW7C7XRRNRzMMsuUKuXIFFQNNarBk4DkugetRHp3CX7exWg1WvQ2GcoNY7RVQA0RqBqcmMcoFIk2hU9vAtx2aqysUhkbu3UMS80DzhRZJJa2jRf11nsj28as2yX0VOu8t41cdwo6DWkmhFEwiOwAv6LunOCEUDWgFhMtdZDpEJFXwBGQMgqUOA00fGV5Z1BUgpx1EEkIvwD5TI7GtgLbJ3MGYBxfD+GT3HmNqCn1iEiWZYOvgIJ2FV5m3HXKjI2QbTZpJFc+10N97m25UwnrxG0yGIcFQBePUGYzZOexykZaMSBo6mfU6jiJAVUgIgaYr+IaAjksEzA6PEWgq+xbnETt2YFbXaXbbbJ2bQVcFViGPzGVQjx0nv3cfza9/h+iv/wPC8lish2RnV4h0QWnrdirbtuMuLHBxeZ6lC2fJqzUWV9qYFYN0LsvA5G4y+XECr7/8UBoZJTs4xMrJY4TZWUxzgkhRyORL5AYG2ViYY/rIe0ztfxxJRGlkgkuH36VXrzG1/wCu61JfnKc6N4Nmmmx5/Ek6Gxv0Wi3CMKI6N0NuYJBEIolmmrhWDykDciNjFKMx6G2QtwOUpo1mZvDVYcziGP6gRv3oDKaRIjc4iKJpLF86T6daZfdzX7oHT0nMg8wXWyQNFVFM4K/10AZSpPaVCVsuatYgta9E7+QGihQETgQt99raYy8AO4AEqK6JKk1Q+wLJmgUBiLKOaPr90abPlXB7cE9W0Z8ZxxhJf743H/O5I6OIqvRxrB7DLzyPqG6QGB+D+RmyX/0S/Pv/SMYP8YZG0dIVEnNzRG++jttpYVeKVPJ5gkQCoaoUml3MSpogaWApKqluj4Ij8RXoJUHVVTxVJdttkJYSV9dpWy6VVpu0pmAPl/HwUc005dEc3UjAl57H+tGfoYUe68UC81bEI6UCrtPl8twl/CDg8tHD2EIyte8gwc/P4bU6GLmdZIo7OfbXb5IulrDaNr1mA9e2qJ88SiqS4G9FSxYRQBhFhGGIoqqURidxe11qiwuEXkCmMoAMI6pzc6AIokhidVokydGprtGuruN5Hr1WEyGgvbFOtlgiW6qw7Vu/zvl332R9fZpUMcWOZ36DuQ8+YOyxQ3gywEw+hUDiWD2mlQtYzSqV8ZcY272P+vIiqn7/xgfE3D98oUUSQAYRQdNFLSYQhkoURoQ1B20wSeGrE3TeX8EYTeF6AQRh37M1AV4PjNSVUWWP/igzCPpm5wkAgRhNIVctyBtXCzCLhIZE4i73IIhIbH84p1mllGzYGwykBj7vrtxTZBQRRSGqpoMQGMk01bNnMY+eoNbrkPMCxjoWiQ+Owd495FqrhNY5AqNA5uB+MhcuEto26rHjBH4Itk2iXCZyPaKwRG+8QlJKjLkF6grkNQPp2li5FIWuhW/bqMkk3XyWCyMDbCRMtrk2Si5NkM9R3bWNdKmDWG7Q+cM/wtAg8ZWvUNuoMuFLGBxGs1p0azXOrq9Tmiiiex5CU9H2Pk56fRlNS9NYXgdVozg8TBT2Le+Gd+6hvbbC9JHDlMbHaVVXGZjcSmFgALvXpdesc+nwO+SHRxic2kZhbALpe6zPXcZut5BCoVCpoKoKUkpcx8Lu9U3U+85gArvTRgiB57ssnjpFbX6edLFMogyuvUqiWGRlbhrPddiy/zG0RJK0rrPt6cdIJgZBKDRXlxmY3MLA5JbP9VmJeTD4woukYqgkd/enxGQoCZsOYdPFs33yzwyDAHetB5roF1IOgQiMnIY2kiYIOgS9COGDqgFXXj799Q6u7pNJ5EAKegrIySz5tk8URHgLLbTKFydKtNrppzoMZPuBPb+0M7xZSkfX7/Lh2oe8OPkipvpwBAMFnseJV18mWx5g59PPIYRgaGorZTNJ68c/RsvmMbdtY+3cWYJSgcxXH8NeXEd9VyFYWAFzluSWbdjDdTLTC+AGhIC6uNiv7726itZqYBaKCEVFI0LzfEqKTiPrYpbKaHUXLZFGQXJgdZVGKocb+CTrrf+fvTcJluS+7/w+/9wzK2uvevX2rbtfr+gGGjshCtxEkZK1ebwodLDDER57Iubik0NhhyMcPs/J4YPt48ihCDtmIjwSJZmUyBmSAEECaAANoPfut2/1al9y33yoZgOtBggQhECg2Z8+vPeysqoz/5lVv/pt3x+eLKMftklpcGg3ENUb6IaB40dYuoEoaNy49g5WpcLc177FlTd+QnH9bfSTp9i7cR2yBC/wUHM5mrt7jKOINMm49tIPkRWZYDDEyhdIk5hxrwdZxrjXpbFyjM7rP0XWDFTLJBiPiGSf1s4WmqFTX1wl8DxUTSVfnaK2uIw3GrL17mWQZBRVI1fXCZwhklwGeYg3cnH7AxRVpbY4h91w6e+OkGQVzbSYPn6CnXffJJNdVi98BU2uUazPohkGafobovTxiE+Fh95I/pzwwEHoElHLI+y6JC2fcQr5p2cZvbxLEkSkGeCGk9ipJog3B5DXSdMAOWJiQDMgnKixEWUwbcA4xl6wyd7pkOiQ5jXEvI2++PBU0LXGPgJxz0i+evgqW8MtThefwZRqHJ96r6T+lf6YVdPgm8vfRBK/Oa24sqqyfP4iuVKJNI3o9V/FGtbYf/lVKn/yhxRLFe78H/8nSqlCoMlI0XWkXkZrcZVa10foJt4bb+CpKUmxRGmqCFtbEIaQpkiKgq3IpGsr8M51MiER5HLogx45xSM+bDEs5InrNkXFZqpapdxqk+Q0/PklXh52mNrfY8o2qVdqiJJNV4bYHTI3NU2306JuF1DKNUZX3uG4nJEOLNobHka+iF0uMWy3iKMIXZGIhcSo2yFfKVOamqF55QrV+hSpLBHH0cQ7TBK23rmMhEDTNbx+Hz2XI1+2KZ8+S/PObfZvXsOu1ckXS4y6LWRjAVUzQAjy5SopGYPDDqWpGTLaCFkm8yuEQYCq6Zh2ASOXJy7DwqnzCCFI4oiZP/kDEvoY6jIH65dI6TF9/LdQtd+ML22P+HT4zTGSeyMkRcI4XgZTxnn9CGHJjF7eI9Ml0k4EdR2ccBI+7YWT0KocoJkqFGVipw8igjSPmugY5TKYCtkgINvoTfa3ZfyjLokToVVMgs0h5lr5Q9Vmviicmbm/8Xo2N8u1zjV0KUcQJ/c9VlMVbFkijTKuXd6ictxgptz4LA/314IQgsrcpHcyy1IST2MsFCpf/zqjTpvW5iatW9fItzroj/8OYX/IePuvkfKzjP0xiuLi1m309pCC0BGGBlIyyZMDKJN2pLB1CUUSGIMMJXQBCa1jMdIiNi2P3LxCPhNE69voJ0/Sah7yg0aDlXaT2XKV7cYC7TDilLtHab5I4eSX0BSV9cuXMHI2/uEBI8+hMjtP7YknWCiWuP7KS2xffQu7kaFZJSrzZ8jimO7eLm6vj6xqFJaWGbkj+s0mcRJiF8sce/IZLn33O0iSjKxoIEkEnkuhVqd5+ybF6Vn80QA1Z+GRUCxXScIAfzzEtGziMMDQ84jUYuHUE2y881OcXkCxXmTlieO0NjcZHB0xvXae+eNzZJHB6sWnSdMUx72GJE1jGscoTJvIivTIQD7il+Y3wkjGXR/jXJV0GKJWTdIwIZ6yJiXoTQekbJJT3HPB1kESE6k5hcnMyXQSZVXmDNJAIB1MtmdHLjR5byKIBtpKlVGuRbo7xr3cxn5qelL08wF093cp1BsoX8ACgrpV5w+O/QFVs0rH67A92GahsEDH77Cg26gS7Lv7XOMtVoMFZnj4jeT7EULCMlfxxkPK07MU6lMEgwHV//ZfMrh6hfW3XkdSBGHjLEt5BaZU+kkXVxYY45j06Ih0e5O4KKOgMDBKGCpoIkU+zMgygSm7oJkI0yILQ7Qgoxra9KIGg4pC7DXhRz8iVFXmZIlj4zHG736T/ZlZHisUONq5hjDWSHoDnjhxmvixi6wf7rK4dpqN628jJImNt97EH4/IVauoqk4S9TncuIG4vUfoe8iGjj8c0tvaoFCuEBkaYeCRBgEj1SIKPYrVOv5ohGoa+GOFNA7pHeyTpilp4FOdXWAYjejd2cC3C8yvncIZ9DELRQZHh8weXyNOEu68+TqqDZWZZVafPcuwvU/9eJ5jT3wN3/GYWzuDbkxy4LIkUSg8du96lGtrv65b4RFfcB56I+leaREduiAgdWLEl2YZ/WQXf32AsFWksk46DpAqJuEoYJjGVFoJ5CQIUlBlpDgBQ0d2cqh2mSg/hm40Cb/CvTwlGsRth2p5HnFawTpRRp2yCHdHKBXjPgWeLMsYto8wcjZK8fNd3BMlEQfOAYuFxXvbLh9dpqAXqJpV2l6bl/de5htL3+Bm7ya2ZtN227y0+xLn6+c5VTnFMBxS0Cbh5yAJ2OhvcLJy8gsvU+cmKTcdj/N5C+l955IlGUZmYS9O9EHjrW3Cy29T+vbvkhRVpkIXtdWmvPwU3Ssvke7uEBUkrFaP1EsnhWKWhVKpEw9HDPMlpN11mvkcds6ibQxRvYS5Vg8hZ6h+jKHr5E8+hjjaRJ85j7e0iHn5Cl3TolypIlUqvHGww4FsMP23/x+SplKemyfZ3+P6lTeYL1d49dJPkXSDeMYi6sWkSYRdq1KqT1NsTFNqzHDpb/9fwigg8n0i38Moloj6faIoJFNU0sBHtmxMVXD7pz9DyDLjTgujWEKVZaJMpjK3yKB1yLDbxiiVsJUc2fHTrFfnsI62GB4dYdemUAwTzcrhHu7T29+jvKjQOLnE8Qtfp9fcpb29wdTC46hagTQNiaIBqvpe1CNJfHq9n1EuP40sPzw1Ao/47HjojaRQFZAF3rUuxd9dJu56ZEmKsFQyJwZNAj8mzUKUMKEg3U3qOylYAtwEWZWQJIFcMUn9u8ZRYvLTAIo69ANwId12COsmRsPGd9o413cxjAZxL0B7n5EUQrB8/vM3JT1wHTTTus94DcIB26NtFvIL97ZfmLpAlEa823qXvJYnyzL+/Ed/ThAH1MwaW8Mtnp99nq7f5a3WWzSdJs/OPout2lxqXkIWMifKJ5CFTJREKJLyhTSYaZYRfkAhyGTqiTepqpYErqkTZgkH/+HfkDX6UA9o7R6SvdvniuJz5oXnqLcPaVZH6KMEbgA5GwYOwhsyPW+R7UiUwgwrcElyPtlMHsUuEBbLjDpjKrOzzD5xEf+dAclLPyVXmqV3fAm91yLrXieYu4DW7VCWTdxBm6WlFfR6Ha5dZ1eVuCxkXKEwXr9D/fxZfGlEJkWc++2vs/7WJa6+9EOWH7vA1NIqR1vrCFHBG3Qx7QKqquO5DuViEW/UJ1+tMup1iXwPWZ7c97IkY03XCLvOZIwXEPo+wcghVypy7MQJ1LGHe9TErtUQWYqqq2imyYmnv4TT72EZRcoz04zdKyyc+grzaxfoNw+xKyphvEMQHFGtvtf7KEkahjmLJBmfyf3wiIePh95Immtl1Lo5GRV0OEY2FJJ2gFI1iG71yJx0MhJrHEJRRckp0PYhzCaFObKY6LoejAi3RhPDqAPm3Z8eEGcTLzIEpaIQ3hkR7Y2RminKlIz92DLhkYOcBMifo0rP9jggSTMahckHSJombLx1iYUzj5ErvSepVzNr/Nbc5IPHj31eOXiFZ6afQRYybuTy490f8277Xbped+IlDjaIiAjigLpZ51+9+q8QcY2Bo7Fcy5GR8dzsc8jSRBfmlYNXWMgvsFJc+ewX4VfEVmSeKT2oAyrnNcxTk6rq0Hfodt4kW6gx2D3g1Pwf4wQ/wQuvw+Ehq9MuSqAQhYLCdYHkxYRxjNxv0VM08n5AsrGFkq/j1SpIB/vYuwr508chG5E5MR1Au36TMA4IApADGa8wpN0fk1+u0l1tcP3WLqXeAGWwz6aasd69RemnOk9LGsIZ0Lx9nVwQI9frSEIljVPSOMP3PMbtFmkcsXfzGlahTOh5TB87TndPZtRp0VhdQ1EUnGEP1TBJ05RoPAJAqBoICc/zCAKfOI5glGLaNoVqHW/YZ9hpsXfzBkvnLiDXKkzNLzJq32Ds+WiaQRx4nPvq73C0eYXSgkalfg4hBEKWObxzk2lOoBbAtu8PqwohYedO/JPfB494ePliV5N8DFI3QsgSkOG80WT05hHxKCA6ciFO7+YL73owWQLbHugS2Ar4TDxFN4KSCqoADeRZG5ZSpNPW5KmDECSFgS6IajakCdk4JtmVEYc24d6IbK5Lt/PKr20dPoieE9Ia+ff+liSZ4089d5+B/Mfoso6cyfy72/8OP/F5ef9ldke7BHGAF3m0gzYRETWtxg+3fshfXvtL7gzv4GdHvNH6PldaV8irecbhmCiJuNS8RFkvs5hf/ND/84tMlmWsv3kJ3coxdN7GCe5QrM6ROQHGxVWix5dR93U6L7/OoZwh5csMpxNGtsBPMuQkIq5At2DQtDMswyaYniYSMnGrReiMkcsZxmKOaHaWo/19DgyDo1OnuBLb5LUCd6bPo2nHMLUQqWKjrnTJjBhVzbHlR/xo2CYqFlgoVpg7ewazVKK9sU5zv0nkBdx8/TuoJY/l54uousGgdUiaJoyc29ROSAjNpbW9ztbbb9DZ2cF3HJxe994axL4PWUrquyRhiKrp5ApFFFXDH4+QdQ3TyhGOh2xfvUzk+5xYm2PRSsiX5pg9eRqExNzaaZ75j/6UauMCmla79/qFWh3NMMnS8NdxiR/xkPPQe5LhoUMWJZCCUjFJ+wGkKUIIMltHlFQyL2KsAonAtlLkmQKECclwCG42mSGp6ejP1InjkNH6IcbYRNjypCWkqmGeqlE/cHHCFigqaihAVUnGAck4xEhmiXcksmqG+JBCns+aE40HB84q2kRK72bvJmW9/IAYgBCCU7VTHHgHaJLGjc4NtvpbuInLKBrd28+PfMbZmHAUUlAL+NmIa4N3WB/eBOBv7vwNo2jEn53+M4p68Z5X+bAhhGDlwpNopoUqz5Azm+xs/D132jt4r11Bn85RWzhBNHaxvYjI7ZOcUkhSBWXsoSgZwpCRwphRQaZx7Z1JhxIynqEzGAf0hzHXTqxQliLUxKFv6KyN+jAzz74zZDfwcQ8cCq0hxdPnqLZVaoGLWFghPxzii5Ty8iq3Nm5jRgb+7hGxSPAzCXv1BHo4pGDUSV0PSXbRDRPVNBg1N/G6I6qNWdzr+4SkZGSopo1uWkS+P2nXiMJJk3GakqUJ9YUlRkdNfN+l1JhG1w2e+ZP/nLe+9zf4oyGB66EvXsASBU5IMpph0Fg9jqJqlGfmHljj+dPn7v72aD7kIz59HmojmUYJ/p0BcdcDCYxjRWI/Inktwpi2MU5XGHxvk6wdkMiTqKmtQLI3RKmYYMlgqOQuTiFkQdzyia71UF0BuZQ0PxFvFZJEMo6QNIF6pKIWTDIvgG5AUhBkQjD4uy305dJD4buXjTJVY1Kw89zsc0iyxBsHb1DUivTCHgkJ42wyN9OQDZI4oWbXuDh1kYE34FbvFqNgxEJhgZyao+217ysK+ryThSFC+/i6vLo1kSjMYpnD9Rskd3rY1QK7Sh9x2Ebsb1AomoQ7TbzSmPCGQhorkDOQ4xSzmaBVKpTbAzqNAkM1Q9UqBLpCqbqEn0U80T6gtNOh3+lwcnMPsbpIoapwu5CjfHsTYedJl+bQl49zEESYlRly03MUjh3HONjj1s9eRtgms6eOcWNjj8gZo+o6tucTxdAb9xifOYOavEN9aYWDmzeIXIMw8QlGLUwJhKSSpQmJHxArKnouR+C6CEXFvCsykAGh75FJAtKUxuoa65d+xkv/919Qacxw7MVvcPWHP+DWqy+Tr9QoTk2zf/Maj3/z9z/WWkfRgDQN0PWpT3JpH/GIB3jojGQyCEiGAXJRx3m7RTwK0FeKJH5E3PFxLx8hN0zkhkUyCpBqBunIp5iBaOTIJKDlEfvxZJ9+QLg7Al0iOnCQaibOZh9dlskVTcIAhCwIr7chzlB0BSmnkRx6kFcRuox/q0N45KKWzS9Mccpa+YNL5tMs5Wb3Jpebl7nWvsa77XdRhIKt2rSDNgn390wiIM5i7vTucLV3FYA5Y46KVeHi9EVyWo5T1VPEaczucJcwDTlRPvG5XKe45xP3A8Lbr2GcPo02/6BX84twhgOUwgGV8imufv9vSY4MrGMFuso+kawj+yaCFqMwR14voDTbWEmICGJEu4Wk6wQli/JgyHhKwTw6ICseYCcV4imTThYTagqWHXAwNSB39C7F6Sm2xz1EHCFZ86iGzrDXJTBNtl/fhZkFZpSJXGN1fpHplYtkXze5/v3vIqUJsecQej6+O0ZcDZCSiMAZ4436gMSxp57naHMDp9cji0Nk3WDliacZd1pEYYAky2imSU1tzzgAACAASURBVBLFBK5DY/UE06vHOLh5jdr8IiJJyOWLyJLCwrkLzK2dYnB4QBxFnH3xGziDHpphkaYJQkgfeV8EYZs4Hj4yko/41Hj4jKQbER65SL0AuWJg5FWM1RLBzgi/1wVVIo0zxpcPwE0xz9eImw6oMva5OqN/2JoYuxWLeG+MVDZw3AgtkVAMldiNMOQ8woPgSgcqOmQCMoFX1LBLOklzDAnINQMyQbjvIJcMDoINODIpTn3xegbTLCXLMpIs4Vb3FoNwwB8e+0N+sPUDDkYHZGQPGkhgGDsIIK/kKIgCbuIiJEFOy7HR2+B2/zb//LF/zqXmJXJqjlE4wos9Lkxd+OxP8iOQLAUlzVCffgrpE8wjVDUNJT2BN3JJ6yneMEUPXTpDjbjYxyosol+TKUcpbhZgyDLIKoGe0ZqqMrRs1CzmpOOhSCaJabJXlVkeKjh6hVZDI86OCFaLZLbJoGkx3j5E1QX5Qh61ssv6uz3K5RrF9iE+Y5LdhCMmWYPWndt814uoLR+juLKKFQSkaYKi6pBlyHFI4Lp0oz1kRUPVNQ5v3yITAkVVCaOQNEvZvvwmVrXC6ed/m3f+/XfxRmNkVUMzDdI0Zvudt8gkmWw0xC6WOfPi19BMk1JjhvU3X+eZP/5PAIGsKBSqdQrVOhtvXcLpdzn5/JdR9QcrVbMsI01D7NyxX/k6P+IR7+ehM5JK2UBSZTBk4o7H+LUuaZQQHTj4O0OEJpO1vUkRjiLjbQwmEz/6Ef7WAPlshWTfId7qQ5ohfBVnrUC8NcTqBxDGSIZMMB6i2TaKIiMVNOIoJYwyvKaHJiRQEhI/RsiCLAnJQo3q8jJ6av66l+hjMR7fJIr6lMvPAHClfYUojZCExIsLL+LGLlvDLb61+i3+bv3v2BytQwaGMEhIiIjuvpJGgkOcxczkZ9jubzPyR3SUDi8FLyFnMsuFZRDw/OzzhMnnt/hC0hUkXWFS2vzLM3/qLIZlEXo+fSNhq/sKth+iKHninTtI0RV252uow4jjU9PERx7OxhaxEJS7Y2I0CqdOkyg6gzmFJguoVzbJem0GtkLlaMRI1wnLp9B1ib5zk+LAJ5ipUSpV0d9sotRzxIy45Y8JCBHRCMOysUtlOns7pFHCwVETqdOkOrdILl8gDjx6nQFyrNJYPonnjpFkgWmXGLQOkRSVlBRZ05BkhTiNUGWFvRtXMPIFGscadHa2CXyPXKGML6sIRULTdBJSijMlDMtmemWNYXuHKBphF6fvW7vGseP09/eR5A/OXXveJo6zTr3+daJoSJqF6FqNJPGRJBUhHs6c9yP+6XkIMmT3IxkKUk7FffMIf72PZMo4rzXxXjsk80PknDpR1BnGk3aOfgimCqZEtDkkWR9B1wdFRrJ1kr7HdD/GGsX4Xoyo5RBzBoEa0893CeZC0jiBYUh1OU/hdAWSuyO0xgnJ4IjE2UGtGeSrdYJbA5y3j37dy/SRGMYcudzqvb9PlE9gBUX+tx//7/z1a9/jq4tfZW+8Rz/oIyFRdCQQ4OMTEWFhoaCQ4gBgSRa3erdwMxdN0QjSAE1oxFnMj7Z/RF7JEyQBV7tXOXAPPpVzeP3wdbaH25/Ka30adA/28K7foLe9Tu+V/4dVvUmuEdE4q2FdmIdTNYxGjaiQQzEUzP0mjipjZRJVMoxVjX68zd5skS1RYLe6ilVQ8KZMzCAhtUz6jSrRW28R7/VQ8zkOCyZaroBaqpBvrDIcegwP96kuncIqL5IlCaosUJMU0gSbhEKWIGsq9eVVvvxn/yUrTz1LvjRNFKYYRgGvPwAhMey2iKII0pTq3CKrTz9LZX4OM2ejWAa716/g9DokfoBVqpAvV1F0lfriIqee+RKrTzyNyAT5hkZx1kDVdRYen2fi296PlS8ys3YK6UMKvExz6d4XuiA4wHN3AOj3X8dxbv/TXdRHPPQ8dJ4kgNBl9JUC7pU2xrEiSRyT9HyQFOSGRez4gIRkSqQRoEoTfUxNoFZM0rGGNpsjOHDQZ/MkQUQog6eC0Q8ZKinD1KQmWWgDnXQQoSwWSZ0Qv+3ixyEyEqoHUtFGrucwzlSRFYmUhPbBJvqp0r1K0s8jipID3puJaSgGemZwtnSO51aeQSBwAgdbt2k6TbzcXXWFKAdRlcxqISGhoBATcxQdISFRkAvklTx9v8/z088jSzJvHr3Jv776r+n4Hb69+m1M5dPxtufz8/dUfj4P5CtVhJ0jzbfJz+ugqjhHXWT9iFG/hr5nwdKY3AmX3kGJzAIpk/FzdaKvPot+54eMhYYbRKwNWkwVDui7KRUvIxcO0f/4Odr7BwRdmWHQx1Z0EgGxH5L4Eb16mdBro6s25akGiq4yICVfb5Bdu4HpBQSyA0GIoWqIdpubP/sJB7dvMmq1sSQbLdHRLRPVtMiSBEmS6DUPsXI2s8fXqM8vcrS1gaLp+I5DsVJn1OtQm1+ms7dF49gJitU6M8dPYhTyGLkc1fqTqJrOcHgFSVLI58/ct25JHHOw+TLdnT6FygLLFy7S672KrjewrCVg0g+pKJNqbds+ee+5pdJTSNIXT/bxEZ8fHk4jKQkyL4EMJFsjaXqIsk526JH5CVLRJB07pHIG4xgyCSHLGMcrxAMfIUt4VzogBIGfQhyjRQlaCFgZWskkMASmak7k6UYBcRBDPwZTYNSLkzxl14NuRpYKBn+3TnyhgTqfwyiXkJTPf/jndu82mqyxWFjktYPXcITDf/e1f8mdwR3aXptdZxc7tNEUjTTMKBtTNNMmGV08PGTke0ayIRfJmVX2x/usu+ssGAscuAdMGVMUjSJ/evJPMRSD3dEuZ2tnP5Xjn85Nf/ROvyrBGGQVlI8WidCtHMmTS6QunNX/HC/Y4NLNv8Wyq3jNXbLWAKkokV/20RYsuk6FwmhA4EDQ3MFuZdjHeoQoiJyKnLMwnBZu1SJ0ZWq+Sseokuh9cH28koI1U4IsJV/M0d68Q1mNkGqrSDeuMB4O8BSFxPOZqar0EKi5AnNrp+nt77Czv8VcrQxZytyps2TdiE5nlySJqTam6WyuU11aRVY1KguL5AolKqfmUXWTxspxMlJuvvISqmEwf/o0S+cvcPbLXyVNQxKnjyQkzHzh7kgdMIwGIAhcl50rb7N84SKKprH+5mv02rdpLJ2mcrcFxDTnUZSJ/FwaJkjag++nMGwTRX1yueOf3vV+xG8cD6WRBNCXC2hLEy8id24KedbEvdIhbnqkbReceCIeYOuohop+uoT7RhuhymQCUCS8wQB1oKJYGiQplHSkBIyOR9FQ2O+PmK9aKPUS8SggixMQIGLIvAiybKLr6gBOhH+njzZrM3XuBHHbQ1RNhCTwN/ooBR2l+sk9qCxLGAzewLbP3PUCP4U1VHQ0aeLtypJMXsvT8Tv8xZW/YCW/QpqlbPY3EQhcfDw/QpZSUmnS/mHLNkESUNdKnMwXuO6MyWk50jCFDG51bjG1MMWLCy8ihGC1tEpRL/6iQ/rMSNLk4/Vu7r8JRgFmPl6hkWWtYJrL7I++j7PnE/guOz/LSGQJ5ZkljFxINIiIZ+eIcwGDF+oY7+wiSXuMTBtxR2Z8IsdRTacm2WQLZ+jLAcH+Hub111kt6XQqY4xsmSQvgdYjX0porr+DbFbYzxpkhWmCcJNux8XM1dC6PcZ9H5ErkiQJcegzvbZGFkNtcRm7UCITEoPcAd7eDoVKDW90RHl6DpEkhIFPf/AGRjdCkhVIEwq1KsWpaWrzixh2Hkl6L7PTvf5DRu++wvJ/9j+zeO4C+7duUJ6ewczXiKIBI+9NKvOLZMKn030NyWpy7OKz5Msz7O79JZL2+/dSAWmY4F3pYJ6pIun3X680i0lTn0d8Pgl9j92r7/y6D+MjeWiNpFDln+vokP/SLP1/2EJkkEYxhNGkBzJNQRWoK0XCpk8aRaBJSIqgtRyivuuj5A3wUzBktMUCIoyJg4RyyyN0IHVcUttH1E3QJ9Pos7EPXjaZFKKBkAFNQp23GNy5jdAzRKog2xrCVIha3n3i55/wjJEk/VMtUFjIL9z7/WLjIh2vw5X2Fb4892WSLEFqSjixw6nKKW50b3AUvpdLWs2vEiYhQ3eIH/r0OyPyahEncpi352m6TQSCH+38iExkHIwP+MneT5jKTfH1pa9zrPTrq1LMsozvb3+fC/ULNHIfUYm8+Dz8kjMzhRBYxSkKtYD5cw2c/YD0KMfRUZf80wrxuIrby+h0hrj9OqcdjaS4zGAmIOwd4sUgtJhtu8PsmWMYb64zijVaIkQJBzCSGIQtyvUTtDb3GB9kFOeOc2vuONWDHbTAQ1YWILmBlS8QuC5OmlKYniNLUga7O3R2dqhdeIytaz9GEzWEouA7k/FZWj5GL4+x1DO0tjZ46vf/iM7+DaYWL5AlEnNnzlGcmsb395F1FUmS7hvSnT/+LKLy3hc5WVEQkoTn7RFFfXS9Rq4yT5ZFEOdI/RKmXcLzdpiqfwtNe080QNJkzDOV+wxkFA2ADEOfxtA/g2jCIz4RiZowmB38ug/jI3lojGSWZcRHLkrNvCtDB/7GAKWko5QNMlUQ7zmk3QBsHdk20GYtvDeOcG93wU8gAqIEIWnUTBthKSBPFAbUGYtwc4hSNBCKhKJKKBUFdxSSApbj4HSGZCQUeE/W7V5X1yhlcHsPdS1jODhk+unz9/axTlWQcr9aflIIiULh/Efv+CuwO9rl7dbbfHn+y2RkjMIR+84+KSmqfL+RPxgfoMkaGZMPxwxBRoYiFJpOkziLWcwtIskSfujT9bqMwzE3ujeYz89/pkYySxLE+6omhRA8Nf0UJf1jTGeRP9lbqHVrxNjb5uSz3ybzK9z8+1dIox3krVuMgpCEHaSpkDnfZlxvEPaGeGGIpBrIg5jKXIV8aY16OeGgaBEfJNgXp4luHpIMAhw9oZJKyLKG0w+Ig3Xs/pCgUkU+2CG1c9RWjuH3ezTWznK4cYskjEjTFDnLEI0Fbg9GnDR1hs0WuqqhWTZWuUzsB9RqzxAEuyydP82ZL3+NOHgBWVXxnTGyMlmTKOojySY6dY427+CPHRbOnEPXy+gzX763Fo2VybX2vCGyYpGzVu5eB41i+Qxe5xaKapNmDrb9oA6rpCukaUCWgSzreN42aRpRKn3+Bgg84ovHw1PdmmbEg5AsSu9tUgoa4cGYqOUS3OoRbQyRZ3JYj9eRBIQ9H+IEWh4MAlBAVhWSMERcCSaDJEMgikmaPrKtoVQM0nEIQQKyII4E0ZSOdqyCYdhosgo1JtbRZCKAngIyOMEQqVqld9AmTd/rKZQL+udGqu4XcWHqAo/VH+P729/n7aO3eabxDJZs0ff6hEmIQCDdvaW8zKOsltHQ0NFZ1pY5WT6JpVoEWUBAwC3nFhvDDS4dXOLNwzeRMonHph5jZ7TDznDnQ4+j7bW50r7yqZxT6jgMv/tdUte9b3vFqCD9kh7iL8PSY48zd3oZTZnhWnQEZ5/AimUsvUKxbpJEKUIPQIxA3yXXCJEMFakWYVR1xt1DtPY17MsyJxa+hl3O073dQVZCksUQw9TwBp1JzlCDNI5R3BFSXiexdNx+j+ljJ5DshMjbZGXpOPXFFQzTYObCk8zVy8w5Y7xWQqlSoWjkeeLbf4AsqVTnF7GKFZIkIvQChBCkYsCw0yJL03sKQ5a5QjDQyLKM8vQsgevQ2f3wamPTnL1nIH+ONxri9LuoavG+ghyAwdHrdN79HgDD4VVGo0noLpc7hu/vEsdj4tjB8z78XnrEIz6Kh8ZIClnCXCsjGe99s1eqJtpcHvdGF1FQMJ+YRtYkok2HZBySdDykujUZuGypaKslJEmAlyBsFTFlTVbIT0iHPkkQId0VJ0BXIIiwSiPK3ZikGaJqCnJdYeAcEmtMposIwAKqOjNnz1KSGpx48YUPLWV/P8PhVVz3/jd4MhjgXb36kc9NxiHJ+MN7Dnt+j7bX/sjXeT9ePBlvdLpymoPxAZfbl9EUDTd1aYdtFBTy8nt6sL2gR0iIpVioqoqf+ARJcM+7BAgJCZIAJ3bYGm6RkbHd375nJJM0eaB3UhEK6qdUsSgsi9wzzyDMz7Z/Vbdy1Ou/g4jLLOhTPHHmLGMn4iDQqaydoDxTRXh16nMlZEdl0y+hV1QqaxlnVmvMLxZxCzKXm1e4tfEaucIsSVQmGRaZKxd49rkzJIqHH7Sxaw1mzi/TmJtmetRhqqAgGxreaEShIZM7nuPMf/ofE4cBmaSgKApWocjUwjIiFRQq83zpv/kXRIGPnrMo1qcgijl+/vc4/uQLBO6QzRvfwR02Cb3Jl40syzjY/x7NzbeJoxDNtFi9+DS1haUH1iL0XLr7uw9sj+Mxai7m+NPPPaC047obxP4A2Zm83wuFcxSkBtz+AQKVQuExZDlHFPXx/U+npegRv5k8NOHWDyJ1IwRgLBVIxyFSXkWxNcJBQHQ4AklCbhikbjwpzAGMizXkAxfzbI3eX90BN4GCBmTQC/BudDFWi6DLMAICFdSUrB+CLHG12iIZBdTNLnP9ConeRw1LIAdkToQ7Dhm1XGaKH10NqSh55H/UDpGlk2rbjyIZBoBAtj84jHvkHpFmKTWz9oGP/5wwCUmyBFMx0WWdilGh5bZQJIUkSXACh+zuv4iINEuRkEhJCVIfgSDNUvaHB8i+RE7NkaYpmqQxSkZERAyTHiUmoc2/uv1XNMwGTa/JXH6Ott9ma7DFk40nWSpOPmBLRomS8Y9CoeMW5GqTVp5fAiEESu3D1yButUhcF33pwQ/3XxVJUqgvLVCKS4zH1/iD//5/Yf2Ny3jeDc791gU2rKsUVyK2m4ukMx754DKatMhPszK5pka1XECbckhTwbg3RJNz2I9bzJ86gS2fYs7aYHzYx7bPEzgDKhUVZzNi5sXfZu/6ZYaddZQ4T6/Z4Y3B/0X3sENj8SxmqcjBzausXnyWs898DWfcxR0OEarH8nMNdMdEvrVP8YmnEKrKrVdfIYuOM3PuMcTdAh0hBI2Zr5LTxriDPsV6A0VVubLXJ6dKLE+915oTeh7uoE9ldv6+9QnDFkHYxii9l1d8bWOTUzMzKELDrK8RWRIHOz+gMffbSNYUoX2I71wnDDt3i6TmMM1fTj7wEY94Pw+1kQxbHnHHQ18qEDdd4maKbCpEbW8iJjA1yS9Chlw2IcoY/cMOSJB0A9SGRTTyJ4IDCqBJZGmKd/kIdbFIkmUoQxXrqQbhvkPcdli7NUNoqhQNDRopiYhgJEBIhO92YD5EMxWyleK93OmHYVkLD2xTymWU8oePsvo52uyDEz7ez8nKPwpdBQPCJHxg6set3i282OOp6afwYx8ncnh572Xu9O4AEDMx2D83jKP0fZNACJCRGEcOS91zNMt3KJklZnIz3B7epiSVkDKJXtzBYUxVKNSMGlWzytPTT/PXt/8KRVL4nZVvEiQBl5qXuDh18UH9zjiAjR/C8a+D+dFr88uQBsEDodhfRJRG7Ax3WCmufCz9WcddJ47GGMYsw2aXhVNnyde+gn+jz8w3L3L9+nexwx1eeOw0OzdCRu0my/YiWaTRM4ZUZlMa4jhSso7vN4niNod7R+RyG5QaZcw0jzCqVKZ/i+Sddzlw30DPZcxfLCBUmYXq77Fx6T9QPHaRc79lMnvicfauX2X62BqzaycxrMmszMj38cIbdDo/Zm7hj7CnXkCoE2/erEbIpk9/8Pq9hn6Ao/Vd4iikWHtPRzUZtOkMOxSU9K4BW8SuVNFswdHR96hWv0KSDNG0Gpa1gvW+8GuWZWx3ukzl8yzVJobv8OA6h5u75KwehWod11CQhYIsW8Sx86lVej/iN5eH2kiqVQNJFsQtD/IKapwR90PUqRyZoRD3Q6ScAkKQphnJnd4kRCoL4q6HlFPBUMEUBL5LqiSYngTjhKjtIKsy6mqR8FaPeOCBlyIlCp6bkfN8lGJK4hioWQamBBUFc8lG6vok/eATtXyMRiPiOKb8MQzlL0PH6zCOxg8YyVOVU7y89zJXO1eZs+d4u/U2X5n/Cgv2Av/2xr9FFSpSNjGQIZOwqI5OQABATa/TC3ps1t5BEoL18ToFt4CTOiBBI9egM+qgyzpO6KMpeXRZp+21kfzbqGqB1eK/wIs9tkfbRGlEP+gzZb1PwFrR4cwff+Iiml+ENj//0Tu9DzdyOXAOWCosIX+MSuO8fQbIEEIisg4YOK+hF5/EPNFg5B1BaqLnI9rrHrZ8Cv3UFpbIcxh3MIdDqmaFzs13WFxeQZpbot/aw5w9xvDSHWYqVWpfsgg6FWInpZMf4R3Pkcq7lKuzjDo9bkWvoS2/yvSxb6LmZCTV5/jTz947Ps/bI8sSpMMYmxWSpISSrqDU3rv/GouPkyTOA+0WdrmCYefv5SiPtjZYKFsYiw2EMkJRymRZhhACVS2Rz58litoMh29Tr//uA18yhBD8s6cmxTjd/T3G3RaL5x5H021UTcfzdsiykFzuHL3+q59LkfxHfPF4aHKSH4Rsa2jzecxTFVI3JhmF6CsFZF0i6gYIU0bkFAhShAaibEyGMEuQSRJJxwcykEETCqqmgyZBQWM4iEjchGh7QLznQD+FCIQFQ5EgdAnGEVriT0KAYqIrq9UsrCcbCP2TtWqEYUgQBJ94TX528DMOncMHtq+WVjlff7A6VhISbbfNO613+M7t7+BGLkES8OP9H+OkDl7mYcrmPY8SuGcgDQycYCJLpwmVmBgFBVmSsRUbRVK4NbpFTMwoceinY5zIIYgDhtGQk9Nf579+8n/kyD3i3fa7rBRXOHKPuNG98eCJ/aoGMk2hfXvS2/orUNSLvDD3wseejymEQNwtEMrX8ghtiKzkEVmAba8yvXKax7/xz6gqOnNZiaee/18JvYiSKHPusSrB4Dr5ExLJ8gZmIaO+WWB0dY/47AqZcpzdd3cIpLdYOncKvZPx1Lf/K46d/TMWV/+I6FChd3CbII3oH22SuRHO3/+Q1PMYddq0tjeJ42BSADPoUCiUKc8sMGrfLxu3/fY1vO0R0m5033azULxnIAE0XUczTUw7j2HM4jg3GQzevLsOEqY5h2HMTnK1H2Hg8rUa5dmJN1mZm0fPmfQ67+INYrqDdd7Z6RBnH53SeMQjPoqH2pP8OeHBGKWgQ5gRd30yQKgCJa/h/ewADA37iWn8qz3CnguZBGUVcvKkqEcCEYHSBPQQecZGkY2JUsgwgJh74VjNS1mLZaIwIyrmMCILDBlhKghZEOyNsc7WJxqyn4Bq9VcbLDubm/14rQ13GYZDFGVSkOPFHtc61zgYH7CcX2Z7sE1IyCAZkJI+8FyfiWeREzmSbFLNa8s2GRlplhIn7xlWlcl62KpNy2vx5MyT7Lk9rvfW6W3dpDi7jCQk5vPzzNm/OMc0bB0x7nWYXTv9sc+TcAyd21CcB/XBKROfBbKco17/Okoqw/XvECyew6iEiD2HyL1N+Rv/BYo8RW1pFl900CoZVfkccqlKb3eDUXiN0vJ5atI8mflTjpo/IfGLaMoSQXLEzFNVolyd73/nr3ju6RcozRyyUHyK8ur/ROCPGRQazD6ziDAMhOcSZvD3bx3wpSWP8rE6amGKMtw3+DhJAtRSk8HeCCVfRQq7pKmPqkxx5/WfMbVWxsgVkSSJ0vT9M0MnSjgP3jc/7/XNshTX3cSyHgxdR/EhfnqHPF+7+xwFkZSJHIP6/HFqFZks7oHyi3Puj3jER/FQe5KpO/lmKzQZ2VSxnp5CLemE+yOyKJk0gcsSIslwNwaEG72JIIA18S4lRYZRBKMEli3ISaApJOMIaxQSH4xhHIEOKAJpJjdZ0YJCRMZhUUU0TCRLQ5vKIRsqckFDMj7Yyxi4EQMv+sDHfhnct97Cv3PnAx9bKCxgKB/fCBT1ImvlNW71b5GkCU23iSIpvDD7AkW1iIaGwS9+vTRLsdSJ4Hk/6ZPECbZqk9NzPD/1POcr55mxZiioBQqajYzPtG7x5uGPud2/Tby5DZ0eyd22mY/yMjTTIlf6eOHo1HXJwnCimnPyW782AwmT8zL0aYRmwdq3MEtnMNR53ILH8u/9Dyh2hcPt14i9DPOEoD++xf7fbBH3DKRmhh8MSewbzDyVp9XuopdKFEunEUmHjR/+G0bmgOb+bUouGEmCXLRw5ITIb9EdvMLfvvQd7oQBO7f+PWouYXpxieMrNepTT1DIn7t3nKHvEfoeSRIQhm1C1lFmapjnzhFFA4KgTeA6zBw/CYpDp/Nj3LttGGHYZjSaRAIUJYei5Ccydcn90ZEsy+g1t+i1rpGmIWkak2XvGVTDmKNYuDAZ35aECCGYWXqOxbOPE4V7zFhNwrD5GVy1RzzsPLSeZBokeDd6mGeryLZKFqU4b7eJez5kAtk2SOIIeS5PcrtPfDSeVLjmDAgicANYKE9WyEuQU4W0aKBNWaR+RHTgoHgZFGRExUBW5Eljgy6BLmEhWE5ArZnE+w7h0RihKIiuR3SmhjZjP3DMG+0xkhCcX/j4nt4Hoc7OIhm/+of9KBwxDsccLx6nslbhdvf2RE6uf4uu1yWv50lEgh/+YukvD48wClk0FhnEA6asKebyc/z04KdkIkMWMrqic6Z2Binz2R4c8dLmTzmdO00QB9Rf/AbDcMhf3/5rnmg8Qc/vsVZZuydhd6t3i4pRoWpOvGzDtjHsB9f3A4/t3XeRrBzmuU9HL/bTIlF1ZMDpJLhtlagxpHn4d3RHL1OdepGjjR5ZcgrpTAvdDDDkGKkhkR7usPf/s/emsZKd553f7z37ObVX3bpr33v73ts72WzuiyhKlmR5LFvjGVuWZzIzQDBIMB+S+ZBggGCAfAiQT8bky2RzkEHsJMhkxrAHFiyMLZuSGIqiSDV3Npu9L3ffqurWfvb3vPlQt5ukSXaTEpuiqf4Djb5VdfY6dZ73ed7/8/9bP2A5meAp6xt4yetk5UOki2dwijpjuQFm5SDVEwZUOgAAIABJREFUQ/dCS3H2xT9DDkJy+QrHJhYxM0k0TBDCQBeCI3mf1upZosKhm7qpzbUVUOCNB4BkceG/oN1+iVQ2yOVGRJvm+goySTHMDMPwqFWfBNgPdO/1He0PzpPEbWq1LyGEhu+v4PvXSAbT5HIPoOs22zt/CUoxOfmbhNE2aTIgnz9Eq/Vjer23mZ39J2iaw2Bwha2tP8PzFpicPLmfiR789L64u/jc4XMbJDVbxzs5hjBGklh6xSFd7ZGujWSQZJRAU2FOF5AlC/wMxhzMukew1sQIIFsZkjtVR8+byCAl7USkbR/ZjrAWy8RvNtEnc7hLZfxzTRAa+lSBzI9ReYF5IE9yuYtqJ5A3UK4id3wMLW/SWLlOwTDREFgzo4fP/XOfDBnHHP/ZXNl3/V1s3b4ZfFa6K7yy8wqTuUmiNOK59eeIVUzNqXGwcJDt4TaDeICne5CBr97PAp02KgykpKd6tMIWs6VZtofbrPRXqDpVLrcvo1DU7BqrvVXiJOQ+8yhr/h4Kl53WeeYKc0glWSwvYuomnahDw2/cPM40S8nU+8t2N5CFIVkQfCAr2HvgAdBuX1DJwpDh6dPkHn4YLXdnGZNZltJoPk218gTj8wt4Y4o4biJQlMtPkMmA8mSVKGnijJkYXkTf8Ah75/HuOUZmpoyXaij9aRrFt9E6swRREz3JI3SH3MFjtLavsbL6NIPkGjk6CLvK0aKJZfXx6kexrCrt9iuw0qZQOYxumCilSJI2U0tHRgcqFEGwwWBwiWBvjcG6orKQp1gsMnZgHqUkUVTAdQ8Qx00sawzbHse233t/FvL30Gj8gCBYw/PmcZwZdCOH/a7WnEL+OFLu319KcaNMKzSDau3J/Z7IJju73yWXX0DXXKQcEsd7d4PkXfxc+NwGSWC/vQOizQHJ7hBzyhsFOV+CUJAqtLwBQw2aIaSQ9BKMAZA3MKo2w8u7pEaMFbpkuiAJfMxQo70zIHE0yusDUs9i2AyxLQ1voUKaKlInJXm5idQVwgRnqYw9XyB/chzh6vjLXRzDxnqXHFpzfYU0Tphc/MW4Fuz6u3iGdzP4hDLkZP0k7aBNoAL+6b3/lD96649YLC3y/Prz9KM+ruEyl59jw98gi7Ob85AAxJJ0r01UNambdWzLZsPfQEqJVDEbwQYCwbQ3zXx5np3+Dp2ww0L+Ae5ZOMwfXfgeBzjAD9d+iGd4/MvH/iWmbpI381xuX2apvEQrbDGZm6TifPgAI1lbI221MB5//H2f3WhjuB2EbWPNzn4iogNRtEOSDMjnP1h6T9MMxmq/gq7nR1qnhUMMh8t0umdBSTQc6uLX0fLXMIw5Jsa/iblUYjC4yNrVf8fg+sscPvQEfrCOyTxKOhTjg6R7ba73/hc89yAinqcr26iZYxSqGjIbkJrTaO4iUbRMkvbRNJM0cbALZfLVMkGwQbP1DDPT/wlC6Pv2VB6KjFLhYfq+SRy/I/wQBOsM/aukSR/TrDA29qX3natSCqUS6vWvIIR18/xt671ziTcyVADHmQKmADCNCqY5ul8tq8783H+OYZSBFE2z9pe9i7v42fG5DpJyECN7EdG1Lsn2EHI6Wt4k8zPshRKyn2BNFBCmQdhJAAmxhIIJsSRLFUkUE+8MceYKZElKPyepSp1cP2Utn2H1Ehxbxy05CFtglG2iveEoM7WglW2TXxijdrKOWXdJGj5G1WX+5P3vOdZwMMD28jjeJ38dlFK0wtZthQPuHbv3Pa+rTpVu2OXfX/j3fHPhm8wUZtgNdtHR2Q12iWRExa1gGAYL5QXONc7hKOedQGlqVKSD6if4BZ+9ZA+AslnCkYJeJqjakzw4/iCaptGKWhTdMo889G2eWXuGulen5JQIox4H8gf404t/ylJlibniHOO5cYQQ7Ax3MDXzlkHSPnwY69DPN/AQQmAvLt5+wY+AUcnx1oIQN7wRb8BxJsnnFjDNGqYqY/ZLuO4USbJLMFgm3gU1YxNuX0NvCLQjLkJYpDIgkzsM0w1kP0E4JrGxh2FKJnM1qtV7KZYeoNl8htXGjxHVAoe1LirpkPPmuVzP07r+VzxpPYSmmbjuAkLotNsvYNsTWNYEgX8OKYaMHX+MLAtIUx/D8PazwiniZA8UbG79GaZRoV7/CuFbr5HtXaF9KANhMDP9uyilCMNNbHuKOGmhshjHmQYgkxKhae+bjy4UjtFqPU+a9sjnRxnwCJ9dr9a7+NuFzzVxB0AO0pEuqlCobozcDTHGLNJOQLLRI235RBs9RNGCvDUyYJYpBBlZlJI7NEHOLpA2A0gU+chD1HOQKbzpPFHZRCUpes4ABIPXd1CZIJ30SA/aVM0J3EGB4Gqb/vMbaI5Bc9cnjd87L7N19SKXt8/TM3/29o4PQy/u8cr2KyTy45GCMpWxOdzkZP0knuXxpxf/FM/wqNpVkizhkalHeGjiIXb9XfzYJ1UpCe/ahxD4ZYcwbzGVn0Ii0dFpJXvEeBStMSZyE+yFewyiAQV7FBj+9Wv/mgvNCyyVltCEhhG0SbtbNH70fcSVFcp2mcXSIkop7hm7h8PlQygpP+QsbhzKZ6dnznGm3qdDejvousPS0n/F1NQ3sfIlCkcOMVZ7gump3yENe7TWn8XWx/EWTmA8uIBS2kjguzIgyJaRuZTY6aCCjJyzSOyH6LrHYHiZ3d2/YDhYZq5Q55C2Trd9mbArsawau/5rYCwTBGv4/gqZDOh230TXC6yt/wkbG39Co/ksY2NfRtc9Njb+A53OSyPZw8Em6+t/TCYjbHscy5zAtGr4/jJGvYw1VqFYfBDTKJNlCVL6dLtv7gfaPnHcuXn+V994jtXzryDTD7iHhYau3xUNuIs7g891Jqm5BnIYQaYwx3NoeQv3foe0GRCt9CCTJDtDNNNA9oKR3JshRryCmol3uEbqR+h5h0tihclGHi9xUVEKhoa1vIoYZESRPpK4qzlokx7C1Ek8i+DsDkXLg92QMOngHMijVV2yQUrSiUhaAe7xEdlk/t77Ef21j9xf93FQskt8ff7rH7ptpRRJJEn1mM3BJlP5KdIs5VDlEAdLBznUPcRLmy9xtXMVW7Npx23iNGaluzLK7ApznG+dxxDGzVYPAIEg6XRIPcWm2ATAFCaxigmyECVhe7hNPIh5ZPIRvjb3Na7sXeGN5hsoqXhNvsZ8eZ7fW/ptlmpHsfU11OwEZ3/8P7BcmaFFwj8+8Y8xzl9DpSneQw996DVot0/juiMFI9Osouu/OBbrx4Xs9UAI9EIB06xQq43KllG0S79/HmnEpIvQG57FtsdI0wF7jddxvTpoYJpltOQoqXUaUVD4nZTdlQ0StYrnLZLJEJmFSCFY2/ghY+YidjokTs5y78UC0dECWZbgebO0269w7fr/NmK7Co1+/wKOO4WUITu7f4Vp1Uhlxrk3nyW0XmWpNE63+xbD4Qr9wVly3iHiaAclUsqHHyELNgGNKNqh13+LXO4Yuv6OE4iUEUrFlGc8Ni7/GHlllblDv4aUId3e61jmGI49hWG8l6illATen3nexV0IIZYZiYpKIFVKPXyr5T/XQTLdC0m3fYKrXXL317GWSiRr/ZFTiD+aO0n9FGPMGTFbMwG+Qqs5jP9nJwkutQl/0kQrOOQP1uie30PfFjhDB0yJaxdI3QB8SZpKUCDjDG3CxenFWNU6ahiDLbFOlkhDiewPmDpUIEsgedfvV9N1DpYP3rFrcavg22+FbK+2uVB8hTONM8wV55jJz/DE9BM0ggZxEvP1g1/nSPUIf3bhz7jYuchj04/x2PRjDOMh1zrXKNtlvjj7Rb578bt01YgcpVA0CgpbN4mImE8qLLY0GkcPkqEI05B23MbWbdZ6a3SjLgWzQE7P0ZEdinaRrf4Wf3LtOxgrBsNoiNE2OKUVMV2Le8dPEqYhtaNHR2IAt4BtT2KaFbrdN3DdFNedu+XynwX0Grt0drepJwoMHffed8rhUkakaY80HaDpBmO1X6XZ/D4KhZQDwuQSmd/HtDxcd4Z29zyZ0cVzH0HXI6aPLWBaBWQ6AKHwvEU8q4bnKVxdp1Sco9d7A20OMh063dfZa/+ENI0QQidJeuQLR5FGl1r1CcJwizjeI45bqAymzDGc/Clsb4w43QUUmmZjmgWq1SfodN5g0L9AmvZx3Xl8f5XAX8fQi+Tz78w/DodXSOWA+uRjuPk6UbRBGG4hZUC/dx5dz1GtPoZpvrOO76/RbP2Icul+isX3TiHcxV3s4ytKqY/k8PC5DJIqUwTnW1hzBbzHJpFRil5zSDaGRGs9MqnQJnNk/RiEIr3eg0wh5nKo9SFZmND+3nVQiizMyIZ9ZkWVRjrAz/o4Tg4MhSFdsqJOI9imFE5CmkFfkqUDiAAdtMkclW8tcOHqWYLNNos763jWNHFvjmCQMFu/A5OQHxOFmoNbnOCA+FUenniY9avnmc3muNS+xPeuf49BPGCpvMSh4gK9aAPPrFAuLGFpFj/c/iEXWxdxDZefbPzkfaICmmPhk4x6KfN5NkWEpWlkKqHklDheP86Ov4OjO2z0N+ibfUp2ifnSPO2wzXRhmoJR4PTmacZsgZ8OScfvQVMZeTPPcneZ/Ni96KaBBch+n2w4xJx8r9mu540EyqvVJz6ty/pzwy2VUCrDqb/f+DmKtoiiBhMT38D3V2h3XkHKEDBGdlX6MTZ3x1lcyqFrBrp3DRknJGmLob+HadbxxAR+eJUo7KNXiiPXjf5p9mQfsg66kWdPvUqyPUSYGrnCAlJKbKuKlD384RUyleAHWyTxqyRJmzTtMzn5G3ScV2i99jyydhTGDTTdodd7C0PPEQRbRHEDVEK5/ACNxl/heUvkckdJZZ9m81lA4VzPYdVqFBaOAZDPL5LLLbC6+n9SLj/E9PTvkKZD4rhFknQwzVHrVJLskaYDPO8XQ4C7i88XPpdBUmgCazqPnrMwNEHh0WnCa22EY4yyyzDdV9LR0T0DGWajkmwlR9yKIZFEl5u4jx1ANANUOyXe7FGMCoi5abzpwsioWQhMM8Ls6li6hjVfJF7u0+vFWJaGU7ERpqD34iZlVaRieuSNKcy0QG4qRzn7+STQPin4qc+17jWWykusD9Y5Oncfpm6iGyW+dehbPLP2DNfb13F1G1d3yHkHWOtuUNBi/NjH0i1iGbMT7pATOYz92yolRUNDRycioicHDFwNv32FglXANEy2h9t8Ze4rZFnG5b3LtKM2KlNoPY2cnWM42KSVpWhCw9VsenGXn27/lH9w7B/w4PiDnGme4dWdV5FK8uXZLyNbLdK9vfcFyb+NMC2b0vgHn4fnHbzZ2mAYOQQKoZkkcZsg3KFQPghphZWdn5JPdrG9KmAwGFzFsoqkaZ/+xlmCtImyYDi8Rrd/mSxtoAkPhUWr+SIKCdJFyh10w6RSeRLT9DCMMlev/s+ksste60UKheN4ucNowuDSpd9H021sCiR6H0NNsrv7NP7wGpXKY7hOnSwLR0bcKsOyJ/GDZXS9A0QkcZtS6SGMmSmsUhUpQ3x/Bcuq7LNVBWk6oFQ6xWBwkSxL6PXepli8B9MsE8VNbHsM42OIZtzFLxUU8LQQQgH/u1Lq39xq4c8tcceoOAhNoDKFcA1ypybIPzCBQECQjMqtfoQwNIzFIngG8eX26LOCBUNoXbxOt7U9yhBNHaEJiFJwNZyZIt7RKlGiY/cLyH7KYGuHdFHhTTo4Yw4iZyK3BiQ7fcycg1srQmIQX+qRrg8wzE9+/vFngVIjmTgdnV1/l8CS9IyQC+0LDNIBv77w69S8Gs9vvMCJ8cfpD7dYa7/GG9tvYGkWC6UFFIoJZ4JMSWwlbmq5RkTktTw1s4at26OeRjKO1I7Q8Tv4qc89Y/dwrX8NTdeoeTUOumNUdYdH6g8xG8csCYtTY6cINYtj44+zWFxkrb/GC1svIDPJ8pVXEQOfNEs553XQ7jtxy/ONrl8n3vpkPQaVUpwfBCS/gIGPZY0xPf27VMqPoekWadoky1IW+m8ymcYYuo1lzDJyAIek3yUZNolzfYQXg+ETx+uc628RZJApyd7ej4iTXdK0B2aXsfGvYJh1NL1Mo/k83d5FEDqaVsRxJukPLrK99edEcY/XG6/T7l1GbQcU1CH6gzMEwVWE5tLee5UwauI408TRHo4zg23ViOM+plGkWLif2dn/FF136Opvsbz7f9Hpvk4YbrK59R2arR9TKBzDtidpt18hiho4zixx3KHTeZUgWMe2xsl5H9xecxe/FBgTQrzyrn//7G98/qRS6kHgG8B/KYR4f2/Su/C5zCTfjWwQE1/vYh7IM3h1G23cg90BuMZoLnGlB8V94fIggphRoKwZqLUues1FK+dRQ4kSKcQZwZvNkZxdzsDRHSItZlNeQ3YzZjtH0KckFCLCSwI7s0AKuNpBO1Bk2O1zxVzlkbHPTtnvwt4FNDQsw2IqN8Xru6/TT/oslZZ4eetlSk6JXtTD0R0mvAmaUZOyUyaVKRPFCQpmgSAOCJOQSHZG1+9dPIowC0lJqVgVSkYJGUoawwYnaidY89c42zxLlEZoSqNiVljzrzOVm+BC+xKunaerKRpbL1Mvz6ILnb10j3Ja5vryj7C9CeKtdYaiwI+8H5HIhGPVY7c+YSlvO4f5cZEq2I1TDjgZ5h0gX90OWZbSH7yFEC6F/AmC4CoybeGVTxBmZwjSn4I0sJwxYpZRJMSJRNOLIEMg4aAtcbQMXfeQMgA0lMoQwqTfP0OS9On1ziPlNr6/TZp2MIwKtjNOGHVBdHHdJfLt18hSnd6pPIHxIkpJpIzQhEmvf4YwXGZm5h8xGJyl2cyj6x5CKAbDC8jMZ3nlDwj8Fl7uAALFcHiFqcm/j8Bie/vPqZSfIEn66EYO0yzTbv+YXv88uuYy9FdASarVL3zq38FdfGbQvBUZRym1uf//rhDiO8CjwHMftvznPkgKx0ApELYBWUZ0tgFJBiqGBNBB6AJhamQFk5Fdh4BmRE4rgeti13LEykf2FdgxUTDACvKkfRgaA4rVKjPZITIFehvYBCZL2CoECcakQ9rtYZ/Mo9s55iywcneuFJSpjH7cvykKcNvls4x+2kcIwUOTD9GLezSGDV7afolm0BwRauwCBbvAc+vPcbxynOX+MrGMR2SlTDJXmuONnTfITBvPK1NNE/bkqC/S0Rx6WQ8t0MgySTECX/SoVCsYGHzv2ve4r34fDb/B5c5lwjSk2+mSHyr08hiHy4dZCU4T+j12dIMg8Xlz40VWhMPfO/4tKg8/SSI01rvXMIXJC5sv8NSBpzC1DxYKsH/OnskPgqkJvly9tYfnnYJSiu3t/4jKJJMT3+Da9T8Y9TMePk6vdxayBK0do2/pxCd2GFWbFCP+cYQmFJlKcDQbyyoRx3103UZKA+ijlI5SJlIO0bQc+fx9ZFmCIEBoHplM9sUFLHZ3/py6VQTlE4a7RKwQRkOUSrAtm0rlCVxvnsHgHIPBZfzuJrnKImG4iadN4q9fpCPeQqkQREqpdB9huMm58/8dxcKoQtBoPkOWJYyNfYGx2lfx/WVcZxLQqFWfGq2LJMtSNO1z/4i7i48BIUQO0JRS/f2/fw3472+1zue23HoDmqXj3VODRJIphVZzoeKMAmQGIFCaIjNAz1kQpCO6/XwecaSIAGSQIDshhJB11KjvMoBMKZrdNYZzJkEhh5EZUDNAZ6Tgs98NIfIW2oRBfHmAVbQZ8z9ZL8i/iV1/l9Nbp1Ef0fbp0elHqTpV2mEbgDd23+C1nddY6a2wF+5RcSoYwuDtxttcbF3k5d2XKV9vwdYO/ajP2nAN27BRKPzMx9Nc7o+nyet5cuTQNI2CKGDoBkdqRzBtB6nBpfYlbMsmZ+TY8Xf4raXfwjVcHpp4iGICA02ym+yyWFnk+OxX6Bka7WGbcavGIW+Sa0mHt3or+InPSm+FultnL9xDFzqG+OV5OIbhBlL2WVz85xQKJ9B1hzTtsdd5GakyhCiQlfIkSwA3pAMFoCNlRKZiRuPlmCwzgAAp24xY8jFJ0ieORi08E+O/SbXyOJ43h2lWgCFp2iefO4BljZOpeH//oz7MUukRbLsEBKQypNc7Q6Pxl3S6r6ENdYLu2zSb32UwOENr8Ap76pV9sQWXKOrQ6Zyh2z1LJhN0w0NJwbC7h6blUJlC113q9W8ANtXqF4njHba2/py19T+m03np0/0i7uJvAyaA54UQbwIvAX+hlPqrW61wx58kQohfB/5HRqHj/1BK/f6d3uf7kCm6z66ShRJN09A8C/1AgWQvJGv60IkgkcgoG5XhuiEUiqhtH/opcSeGTIIGgUopV6eRx3VyeZsDz6RsvNFFmYJFx4JQjVxEwhRRd1CDmGR9QP7+A7i5ZYw0xpiZhP42FO4MuWQyN0nNrd22RyzLMs7unmW6NI1jOBj7o+7t4TZHKkd4YvoJfv+l36cdtWn4DfzU58kDT3K5fZlTJ45xpv0246VRK8WGv8HX5r7G9y/9gF7QY136OMIi1lMSmTCdn8YxHXpJn4YI+JWJx7jYushsbpbxsXF0Xed7V7+HoeC3p77Gf2iv8Ga8Q0Urcr17nY3+BkmWYOgGq/4mnuVhGzYFs8C4N87OcAehBE/NPMWpiVM/d39cr9djOBwyNfXZlDVrDiJSqZgsOTjODFNTv4Om2Wga1GpfYmXlD1EihyIG1QXNBdsGAjStjq57JN01cCUIHTDQtDJp2mI0ujMxzTF0zSVJeyilYeglmq0f4rrzDAYXRizXOCWNd7HdBNc5QJr2CIILgIXrzCGEIIk7gETXy/jBRXRzGkMTpM6o/WSEDIUFwgG6QAFNZETRZcAml7+f4XCZ4XAL0hTLywjCMa5c+Z/odl9HZkOSpMv4xFcpFO+hkD+J593aUu0ufvmglLoGnPo469zRIClGxnD/K/B1YB14WQjxXaXUuTu53/cdhz0i7mAJkAr/TJN4Z4hZsbEOV/DPtZDdEMJs3/UDsiAFz4QxBzoRxnyJYLPDrt/C3TBpXt2kaFRxMoeFDDCh123gFcqgaThLFawpj+CtPYSpoToRbctDSJdSfxtHaB85SI5G1uJjPfg/rNT4bjSbTdpbbQ6UD7BQWsBPRlnGgcIBIhnxh2/9IWWnTJiGXNi7gC50dKFj6ibX4hZvxleZaYbkKZIaCeuDdY4Uj7Hlb7Ct7aBlGpqmMV2YxjVcrnevUzJLHK4cxs98erKHrunU83XWumskIuFIVGHt+ousa33AoOgWudS6RCtuMWNP8OTMUzy3/hxBEnCgI1i5+iZ+4vNm802eXXuWByceZGWwwu8e+d2PfK1G11i95/rquo5hfHaz0Y6fECaSyZKDEALDeEdxZqz2JbrdN2g0frA/9WoBPqYxj1IDbGeK4fA8eDeEHwQQkWUZiBwoCSikTCgUjpHEPkF4HdedJgiW6ffPkGWKKG7g71iEOZMw3WYiWCbn3UOSDIGQdudFpPSxrSqGqJJlQyBFJj0Mq4gQLhgOQuRRKiPLthjJ9Smgi8yKQBkI6PVeRdNyQAAG9HpnSJMhYdRDZX1Mq8Jg+DbeYB5NQC43i6bdNV2+i58fd/op8ChwZT96I4T4Y+DvAZ9ukNQF7uFRiTPa6qN7Js6JGul6n/65JrRDcC1AwGDEylQNH2O2SNoJIZbIvQSzkbFQPICwDVy/gKM5oImRK0HBJcxizMBHhdqo3BdK3IUS+SdnkJ2QqCV5pn2aYr7Kby7+5kc+/nb7NJZVvaWUWbw9xCjZaO5H/0pVTnFo4RBqqPh+4/tsxBscrRzlavsqT688jcgE3z7+bX66+VN6UQ9Ls9jyt2gPtpHDVXJWjmbQJFESZSk2hxt868i3WL58FRlKpJIU9SLo8HvHfo9/9dK/oiM7HHQO8juHf4cjlSOs7l3ita2X2BzuMuFN8NThL7PaPMdS7n4Oo9EIGtS9OmljD8/vcW75HHOVOZphk3W7yfn0PLWNBq7lkrfymJrJQ+MfrrzzYfjJ5k+YL8wzWxyp8uRyOXJ32O3j3ej3++Tz+Y88EDo0/uFWYHG8Q5p2Mc0ipjmGlF2SZIjMBmRZQjo8y75LOOAyytwAQgJVwcUD2mTZNoOBiVIpadoiSUqkaYphlKmUD9Hrv01tYgEpLTaNLVDrKBI8dw4/eBspRyXaKM4w9AKmWSRJdBI0kriByQAApUxGpV0YFZwko8CdAb2bx5plu/vvO4BFFO+glEABhl4gTSPSpI/rTd70nlRK0u2+Tj5//D0Dibu4i4+KOx0kZ4C1d71eBx579wL79Nx/BjA3d+dVUIychTnhYR3Is7fSH5F4HANMgbAtVMGEQQRRNvKebMWggUr7DJ0YPRoy+eUl1MAkeKmJ0bPAlIRhj3I8gZ4LSCZ6uNOH0F0Ds+qhghTdNakePMBX8+WPlOW9G4XCMTTt1u4TKkpR6Uff7sW9i8QyxtZtRCw4WTnJlypf4q+X/xpNaPiJz1xhjpyR46vzX6UTdtgebtMYNtjor+ILF8+a4FrvGoEZMKFNYGJyevM0M/kZam6Na51rzBXnUELx9PLT1Lwaw2SIlJLTm6eJ0oh26wohGpppEMmIZzdepDHcolo8QJRF5MwcRaNIrNk0hYEMVjGUQZiF1AoTlDtt9uhRcArUnBqTuUkWyu+or6TZaNBjaAYykx+qPHS0cpSy8/P5eP6skFKysrLCwsLCJxKYi8WTLC3+16SyT2P3+3S6byFlgN9KMXIxWhKPyGmmYhSQnP3/YzwR7QcYDTCI4w1AYJqzxHETpboIkccP1knTAVKdo2SeZEabwo9axNEOqbwR2GJuCI2nsoHMMmx7np3IQtFmkgGgk6B45851gQGjbHKw/15KTIJAYKIYtbK4+/OmFXQ9TyqHlAtH8f2rCE2g6zd+LwIhDIT43NMv7uIO4U7fOR80LH4Pm0Qp9W+UUg8rpR6u1+t3+HBAL9p499UJr3fRXB1h6WiS6F48AAAgAElEQVSehaYLVCcZZYaOiXawhF4wR79xjdFDJYtQtQDNMaGlUDqYQtAcbtELd9FdgW6V8OxZZCsk2RoS7/SJt4YkeyG6azKRm6DqVm93mO+BaVZuqzVqz5fQCx/d+WCYDJkpzLCyVeRcsMZqssor269wrHKMhycf5jcWf4Nu3OWFzRew4ownJh9HIrnSuYJjVxmrH+FA6QC2bo9Kn4UD5Iwcl/cuc617jcawgRCCjIwwDXlz902UUlSdKjvBDlc7V3lt9zXeiBpcinZphS3CJOSKv0qpMEVGxmZvk8vty/x488cMsiEN1aard9ESSShHriK2MrHQCNOQh+oPMV+cZ6O/cfM83269zVuNt+hGXZ5eefpDRd7rXv1jD14+Kei6zokTJz44QDYvQ9j7WNsTQqdQOIznzlCpfpF+/yxKgZYVEZkzuqdNATcDTsqIyWYwUupS6KIMWQJ4gEGS7KFUCrjE8S6QMl7/u2QipS/OoowmplPEMMvcyAR9THwKjIJljFJdomiTCVqM0wQEGi55e5ZRBgmpuhEYbUal1pEilSBAe/ejQwZEkQC6SLlJJgNQKVHcYjhY5tr1PyDLIoTQKJXuf1fQvIu7+Hi405nkOjD7rtcHGDVI/EIRr/YAQf6BcdK9kHhrSLzaRxQNNF0bzU8KDc01kXmTNEwxbAvaGUms0X1+D3uuwF6yjmUIXM3BSC30koMwNKwxl7SZMtzsMhgkHP3ag9jzpZEYwWcED048CMCp2ZCXGq/S7Pcp22Vqbo3N4Sb31e+jbJc50zzD//PKH+LoDnk3T4cOx2vHMXSDftRnwp1ACMGVnSu00zYRETWjRqpS0ixlzBmjO+xSTIsUrAK7w12kktSdOprQmHOr2IGP5s0gxMjYN2/mSfZl66ZUhS12iJRiL91DQ0MzTGasaWIhicYcfm3m13lu7Tm+c+07lKwS3zr2LWYKI9LGseoxlFLYus0jk49g6r+YQHg7aB9m/DxsgpUHp/ih6wbBBqZZfJ+9Vr9/gShqUcgfwXEPkcnnkFkLyKFpHkK4KOWP5iJJGZU2AXrIwIRBhjFeIM0GQIpljZFJSaYS0jSi030Zy5oijhsotkYSes5RwmgLqXw8kQPVYTRWNgEd264SRSEaMeCQkRDHW0COoRywERscchIss8ZIWQcgwaQEDOGGy4xexFbzjGZuJEpp9PoXqY99lVrtSSC76U95F3fx8+BOZ5IvA4eFEAtidMf+Q+C7d3ift4U5niN3cgxN0wgvt0ci5BboFRehBGgZWS9A6QLzUAWkIgpjchN1isVxJBnEKb3WFh27h1e3sXICoQuMvEkyv4L1RIZdyVE7UL+p/nMDraDF5fblX+AVeAfjRYfJ3ASRjPBTnziLeaB4goWNlCPlQ5yoniA3NkFUsunEHTzdY2e4wyvbr9AIGgzSAaZm0k5ahCrgYP4gk+4kNaocTMusDFYYy41RLVbxTA/P8jB0g7yT54sHvsjx6hQnDz7GerDNqr/KNw9/k3bYZi4/RykOyHc3OdpeYy8e9VwmJPTlEM8qEKURnbjD6c0XcAyHollk19/lgbEH6MejOS5bt3GMEbnldn6an0nMPwHFWzNsRybO7fe9n88fJwrXOXLkv6FUOkyWpYCJrlexrCquW99/L0HTbgQUAQiEm8MYm8a0S+haAQgIwzXipMUo41TEcZMsG2JbBSxZBiXIMp9q5VF0imgY6NoEQtQYZa0xpjaPZZQYZYg2uj7G6DEU4mkms3aMJnLomkOabgA9hChhmvZ+YK8ARWAP3b4xR1lEZRXCgcFW9ywgqdW+cNcB5C4+EdzRTFIplQoh/jnw14zqKX+klHr7Tu7zo+AGuUUBomqj6zoyliStAUQKRr3IpGtd2DNppdBOJccKBuylEKQkjsn0zFESf4jsmHjH6mi6RXK1S759HLNSQBvvkn9iGr1ov4c9makMmd3a//DTxMOTD3Nf/T40NIbpkNd33+SQkaMTdXh86nEMYfCDlR+gCY35yjw7wx1ySY7FsUUmShMst5eZia6wQUrDarE72EUPJKf2iiwXA/biPVzPxdIs5gvz+NJnL9xjtbvKXGGWH67+BFdzEULwzOozjHvjLLcvMdm4zIuORWyNWhssDCQC27BpBA1yRo5Qhqz2VhGZ4on6Y+RLCyz3l+k2u/ydhb9zs4S6Pdzmevc6T0x/dpSOPimUyw9+4Pu+f41y+WEKhXvZ2f0hmiaQmY4gQtfHuZYUqOLg6PvTDaqCYTgkSRulYlLZRoYSy6oiZIZtTTL0zyOERpruoVRClunk7Xvp7LwAlZQ0ySGEA3TIlI0gRqlRTybkkDImTnuMgrWJlD1GpJ0ymgAzMxDaBFF8FRhjlCVm6LpFkmwwykhHrNURMSiHEDqZ2gNjHNx58vnjd/ya38UvD+44x10p9ZfAX97p/XxcqESigpTCQ1Ok20OyICbtRihTgZ5AxYRM0ItTCk5At7VOZMwz1AIikTIVe9hmDkvTiLUOhWmb3rNt0AXJdR8jn6P4lVk0z0T2IvxzLexDZawxj7pXp+7V6YQdNE2jaH14Ke3TgqWPMom+3yc1BMUHHuRwf52NwQbbwTaPVh9FWpJBbsBKf4UBAy40L5BmKZc7lzk2fYpwuE6ahhiGwfzsIvUTh9lpn+V8+zxxOrIme+rAUzSCBhPuBG/tnmHYvUbRKtGNB5ScEuvddcrK5DAGPxo7yF64i0InSyUhIwPtMAnQDQNDN+iFPYpGmUHa5VL/Or959O9iaMZ7FHeyLEYLV5jJ/e0WPd9LUjxNw9E/WgGoUDiOptn0++dwnSm83CK+fw3bnkUTBlVdx3XHKeXm8f1lUDoy65Gm61jWElK2UGqHKNoDdFACQ6+TJC1GNjeQZbt0hq9AJQRcUjlEBpuAy/TUtxj6y3S7bwAatl3GtHWCOARihPDQhEOmMgyjQpZFpL5P5LewTMjnZ5Bso7JROXWUQYKuOXi5++n338a2pzBNG8OocmDm27TbP2F396+Ynf1Hn/j1v4tfTnx2G8HuMNJBgrB1jJxFsLVL0owwCxbCNYgSiX2oSqQy9NcamMphzKhhazaaL1DmgFRrkDYFWSApzOaINwKMI0WcuTJKS7CmXTRv9JDW8hbmZG4krv4urO2bLJ+o3VqQ+9PEIBlQsksIIZgtzjKZn8TWbRaLi1zYvMDk2CTFrS7PVa7S7jfY2rnEQaPEnD3HctTEQyNvediDPeyyoubW8DOfCWeCdtxmbbDGaneVjd4GOjqO4THIEuq5OvfU76Hb3yUWsKc79IYROT1HwS6w7q8DoAcKRwaY4xM0/AaGMJgsTlFz7+Vw9TCL5UWWykvvG3hYuslY4dNvLk82NoiuXSf/1Bc/8PNIRpiaiXYL9mWWJfQH5zibzDLuuBzL3ZqEkmUpSklMcyRLWCqdwjRLRFGLweAcMutimovkkyuEUYtmsMyISZq9axsxo4wtZMT0yUhlB8uqgwwRooBSN9o2NCCPrhXJsiFKNanXv4rnHaa19xIJQ0LtMETbRNEuQnjEqgNpsL/tlDS9DhRw3CFZlpEEGlGyCiIDhphiBl1PkXIdmaWkaRvDsDAK9xGkfU4c+C08b45O92XGx3/j9l/MXdzFR8QvbZBM1vtYswV016D4xQOYl1qoRKEiiVl1QRMkVzvkJFDUqMoqKIVZcjG1iNa1Jp5ZJJvUyR2O6b2yy47oMTF1lI2rLzFXPM5UbfRgFJrAOfh+HdWT9ZOf8lnfHn9THFwg0DQN13J5bOkxVJbxBecYXzn+2+yuXeHfnf2/GdddNLPPvRP38tzacwzjDoldZEHTKNpFBq0Bp0qn+BcP/wueWX2GH2U/4qnZp+hHfVb7q/Q7Vzg1dopzrXMQdNFMh47p8CvDgNOmIE4iNLSRV6UDM/nj9PBJSZlyRvN1D048yDeXvompm5zeOs2jk4/ebOnQNOtDS5J3Gka9DreYG3t151XG3DGOVI7cYiuKLIv4QtlD/wgN8oPByMz4hnemEDq53BLj479CEK6Qyy3Q711CCBPDKO7P/eUZZYf7LFcJDGPIWbjuIkGwDiRIqTD0adAM0uSGnJ3ENItkmURlCRDT612k379EFG1gkpJly4yYtBpKhfuaPjAKkjFgomkWWRai6TrViRP7bU+Cfv8CabK1v4YGSILgbUxzhjjtkllzFIsnUSqhVLqPG1nuXdzFJ4Ff2iDpnqgh9stW2r6VlhzEhJfbmPNFOv/xKlk3xHpwHHSI325BxcZwTcqahzd0MXI29lKVcKcLWcTYUon60UXKhyZwJ2s/87Hd0Fz9OMSDtd4aG4MNHp9+HAA/8bm4d5H76vd9aG/gh+FM4wx1t85UfgpDMzhVf0fFSWgaJ7/xTxgmQ948/xxjXp344DRtobHeW8E2bO71ilh2kUHic1/9PtZ6a8iu5PnV5ynaRSYLkxytHEXXdIxtg6O1o/zF1b9gOj/NHgmdYMhesE6UxXRSgwHDm/uXQnBluIxEYmKSMHL9+Paxb/PazmvM5Gf4+vzXP/DaDeIBoQw/VQKPsCzM6ekP/fz++v03S90fBk2zqJQf/cj7zOeP7bdrvBeFwimOHvlvse06m5vfYX39/wVhkqad/SVcbmR2luURmA5Kxfj+ZURPYlVnELpGFO8g5RAdE00bI4u2SNABga6VEJpJFC1DmkCqgWNiowPOPqO2j4PBiLwzYtQKxrCtcYLwImCMTJ39BkrtUCg8hMpS4sRHyj6GUUDXc4CGS5+J8kGGwytIOaRcehjbfr9J9V3cxc+KX9ogKd41r5O2AvSSTboXYpRsyBTO0QrC1lGZIt0eohVt9JKF3IvJNIU1VcKo2kTLXZyjNbwpgXe4ij1ZwObnc4Po9d4AdEql+4hljKmZtw2YE7mJ97Q3CCHQNf1nYvhVnAqe6d1yGUd3uO/Rb2A254lUwmZ/k6KexxyEKKvCUwvf4PnNF+hHfR6dfpTXd17nyfKTHMgf4N+e+7dcbF3kazNfQwwFT3efJssyZGcdB8FmuocCVu33Bw8LC9Mw8TSPVtyiE3RoB212hjs8MP4AruF+6DlvD7cZJIPPFMv1dtcZ3i+ZdzuMnC/e/9POsogobmDbdfL5I1RrXyAItikV7weh6HbfpDUYYGl9lIDa5Ne5uPMdIilYaFfJ9D5pfiQ5FwEeDqZZIcq2gA6gI7Nk34bMAkOBSNG0cbKsD2Tk8yfo919nlD2m2NYiCIVSECfb+8etiKIGSo1UsIbD1f31BUJYOPY4udwSUbyNTCN03SEI1sjnD+M4f7vnne/is4e7MhRAshciBwlazsQY9/Avtsh6MfZ4nsF2g9DzMadzCNPAnMqBECNd5kBijecpPT5N/t5xnKVPRrHF85bI5RYBeHHrRVZ6K7ddx9ItJt9FTHENd8RY/RmURnR0wjS8+bobdWn4DYD9lgHY8XdYG6wjhUIg2Iv2iElZcuawTI+aV8dPfNYGa3i6x/HacV7feZ1n15+lYBUwNZNnNp/hregtClaBLMtwgx6OpqGhU9HLjP7S0fZvUxcXS1gM0yFlu4wjHGaKM9imjS508lb+llnzocoh7h+//2Nfj18k4sDn0umfkETh7Re+DXz/Ko3G90nTgGbr/2MwuIqm6Wg66FqRADjTO0YntMiygCzrUTGL1J08cklHVEroWgUw8HABgyha39+6x4jBOlLr8bxDeN4p0PNk2RDLmsAwKvsB8obsnEKRYBgVQCJlE4FGLneIQuEUlpXDNCfIsg5J7AIP4ToTCM2hPzhPsXAS152hUnmCWu2LJGn35v15F3fxSeGXNpN8N27out58fahK88wOu2e2qFZydIe7MGNQMuoYdRfzSgd7oYT0EzRNG81hfjwRnVvCNN8hnTw4/iA589PVnPSlP7L92kcraNGP++TwGQ4vU6//KlO5KaqWh22WUCgWSgusdlf5/9l77yDLrvy+73Nufjl3DtOTAzADYGYRFsBiFyQ3cXcZRDGUaJOiy7RVZVMu2+WSqSpZLtosl2VKLpdJmTRtSWUGkUWZWpG7S3HN3QV2FxmDATCYHDunl9PNx3/cwQADDCZ2T3fP3E/Vq+5+fd+5591+/b7v/M7v9/0d3HeQ07XTlMwSewp7eHPpTQzVIKtncQKHxfYidmBj6RYrnRVm/BkODx2m43Q4LQMSQYBARREqKZGifSU5RCCiJCC/hyIVVFXluYnn+MLUF3hq5KlbWpHdKlJKLjYvMpmdvO1Q9VqjWwlGdu1FM+7erDuTOUAyOYmqWuSyRwh8mzC0cb0GpmFSyuzny48eQnSTeM4yUtpkkztRNYNEYhLbno+6f5jbMY0KffsCYRCiakVct0G0ZyiAAEXRcd0qipIkDGtR02UlAyxdOSaFRwff7VE0LZxQAmUU1aDfXyaXGwUEmfQebGeVHnNMTj5Lt3uUZHIHzebrSGBi4pexrAph6NDrXSIMnbiHZMyaEr+aPoT0AoKejzWVQ7dd1I5H+UCFTDCMV+vBYhiVjTw5gpo2aNbeRk+tb8Zkxrj3jXx35j9oSiylZCI7gaZoVzImIwG37Vna7RMMDHyB91bfI2fm2FPew8XWRcrJMqqq8qWpL6EKlbeX3mZncSeBDFjsLaIIBdd3Gc+NM5mZRFd0QhlSSVZIC51SWOBM+xIaGnk9WjG6wkUIga7o/Nqjv8aZ+hmGkkMoIjJBn9Qn1+z5e6HHXGeOgeQAaeOTjcTvBUIIMqW1CQ9H3UKi11M2uwfTrCCESq3+A8ql5wDo9i7R1WwavRwXa6cZFCFh6NNqHUfTkihK9kpCkE8Q2oRhl0z6Edrt4whF0u/NIZF0Ou9gmWMkk9vodBpI2cPzIpcdIVJI2cdUR0FRUFQTRdEwDQNF1fH9Nr6/yuDgV6LaSWMF0xwklazgeyUECqOjP0+ve55QRkk6imJSKl0/gzgm5m6IRfJDBM2oVlLLmQzuq/D+9n8CHa2nEQx76OUEajLaKxOaftNPrbe7n7TZuNi8yEJ3gadHn0YI9UqjXbCsMQwjevMeSg2hKzrTzWkWugsIBI8OPAoiEvnx3DiX2pdQiLqjjKfHKVgFBIIVe4We10MRCrqq0wodqnYVQxj0ZA9d6vj4pIwUE6kJdE3n8NBhvrrzqwQyoOt117zO1FANnh17dk3H3Ggic/LgalJLp3sOQy+TSm1D19Poegnfb0U9Ib0WaW2QvDJO0n6TWu0lDH0E161hmTl8r0a98TpSepjmEI4zi1ACDH0IJZ2g0zkO+ASBDaKFae5DVQV2f55QSqTsYZojlEqfpdM5ie/XESIgCPqY1g6y2YOEoUuncwLTHCGXPYSqpRBCQQgVz68jxE4mJ/+jDzkFxcSsD7FIfgitnEArX78GTR9M4p+p4S33ULdF/5jZ7I3rGz2vQb3+CuXy81v2n3k8O04p8fFMXSE+6LRQTpSZbc+y6qyyp7iHaq/Kdy5/h4XeAtOtaZ4YeoJ3V95l2V6m5bQoJorU3BpPDj7Jq6dfJafnqFzZw/R9n4SeIJ1MU7WrWIrFSGaEy63LLPYX+fUnf51tuW1X55HQHgzj6jvpKfphPK9BGNqY5iCuW6XTOclA5Ut0uxfQtDS2PU+1+gOCoAtCQw19JnUfxXyEbPYQjcYxPHeFfOEI4FDgCaq1NwhDh3bnHIpi4Lpn8f0moET2cSKJ46wCAcnkGJpuIOUgnreEIjRs+yKqmsOyhlDEHlx3lXz+EN3eDIXCEVrNt1lZ+S6eV0PXok4fhfzj6Hqegcrn47BqzD0hfpXdBmrawG9+vAarX19EBh7JcuTlPlPtEcqQwWZIqrBvXQRSSsnihSaFoRRWav1Mu3VFJ2d+vMbzowynhknqSYpWESdweHHmRQYTgwRhgKIo5KwcxWSRRyqPMJQa4mT1JE2nyTMjz3Bi5QTT7WlKZomW00IIwUh6BAWFcqLMYHKQhysPY+omD5c3V23pmfoZyokyRWsNN6WvQ63+MqY5QDq1644en0p9EELX9SJDgz+Brudw3eqVchFBKr0dQy+TTE7Qap0gCLo0mm9iGkNYVp5EYgDLHMA0KzQb/wrTyFAsPke78w7d7jnAwLJG0LQsQkC3fRGTHFo6SyIxSTI5Radzhn4vj5UYQlVMJDrp9E66nfM47gqhDCkVn8Lz26Qze/CDDoqSR9NTIHQGB79CrfZ9bHuGZHLqE59vTMxaEYvkbaAPp9ArH08QcesNQt+5KpKBjFLalaSBkR5bt/momoKirk0ot+k0Ob56nCeHn7yjZBVVUa8KxfsdN+a787T9NntLe5FIvnnhm0xkJjhQOcDnM5/nN1/5zav9HhUUFroLBDIgaSSZbc5iGiZf3flVhtPDHBo4xHRrGku7ccuw2+Xo0lGKVvGa1entEIQBoQxvfuBdks3sR1XXJjlJCHHVjSeT+cA8IpH4oJ4zl4s+jCSTE/h+l0zmAPXGq7Q7pykWn2Vi8pdpt08BCpY1ghA6llkiCG18v49hFNCDBKrMUBp9hm7vMqXipwCXVHIbplXGsat0uicplT5Ht3uOfO4RSsWnURSdvj1LKrkTQy/T6Z7FMApoahrDyDM09BWE2NiEqpgHh1gkbwMhxJU+fNeS236tS8228vpnowohGJhcu724pJ5kODV8RyUj16Pn97B9m5HUCL/91m/z/PjzfGroU/zN9N/Q8TpMZicxNAO7bzPbm+WxymO8NP8SAGqg8lDpIZ4YeYKp/BTb89tRhHKNkLXcFm8tv8Wnhz99V+2vRtOjd5U9vK90b8y0398Lvhmr1RfIpPeuWUG9ZX0gnEKAoRdot4+RzT5MLnsIwxig271Iu/02jrOC46xSKm5HVVPIYkgQ9CmXf4ScVyeV2o7v9wmCXiRyUkdVDdKpHezd898DEk1L0Wy+Qzq1l1RqinR6F/3+NL3eZUql+2uf+EHHs33mTn+8e81mIxbJGCAKq27Pb7/l4303oDrfZWAic90+mSPpEUbSI0gpeWfpHU5UT9BwGvzYxI9h+zZ1p86nBj/Fvzv/79CFTtfr8szoM5xpnCGpJfnc5OdoOS3qdh3btzHVa0sgUlqKiUyUdXs3DKbuL3eWVHLqlgX1djGMAonEGKnUFL7fZWHhzxkd/QUymZ3oRhqBTrv9LoXC0zSarxEGLoZRQtPSeN4qS0vfwDArZDL7abaOUSo9TjK5HSEEQpg0m2+Tyey/Yi0XJRvpeokg6F9d+cbcPxjJHJOH1sNn9z9f09FikYy5I8JQEnhh1G7sBscdXT7KwcGDbMtt483FN5lpz/DTu36alxZeIqtn+cX9v8jZ+lkmMhP8xM6f4GK1QY85nhh5AoH4xNCvqqhM5eI9qY+SSEys29imOYgf9Oj358hkDlAqfQ5VjcLfljlEEPSQRA2cS8WnAQiC/tWs6Gz2EUyzhKomsazPf8JZIktGKQPq9dfIFx7HNAcJr2OzFxNzL4hF8haQUhJ2PdT01sxQ/SiB7xH6Abr1wf6e7/vU63XK5fItZVAalsbIrps7DE3lprBUK2q4rCX5p2/+U7p+l8+MfQZTNWm7bXYXd1NKlEjoGRbqfZ7e+fgtrRBtZ5Eg6JOKEzjuGbqWQVVMhBBks9eGmlU1SaX8/DX3NZtvoWk5stkDNxxXCPUaE3ohVAYGvhDvPcZsOLEt3S0Q9nzs8w1ksL4JGlJKnMstQnt9PzWvzkyzcO70Nfe5rkuz2bxqrr5WFK0iST1J02lSsAr83Yf+Ltvz2xlKDVGwCrihy0h6hMnsJKam8qWHhsglbm2PUYYBMnTXdL5bkcD37tm5DKN8zT5lEDh0Omc+8XWTzx++JjnodogFMmYzEIvkLaCmdJIPV64xRV83hLhx/HINGJicYnTvtTWeyWSSnTt3oijr8xy/eeGbvL7wOrsKu642Q4Yoq/XDfTZvpw4wkRglnd6zpvNcC3pej6bTvCfn8hybM6/8ELffuyfn+yhh2MN1V3k/TPpRFMWMxS5mSxOL5C1yveSUG3H0cp3Vzu31tRNCYE5kUMz1jYILRUHV1q+28nqMpEeY78wDUbnJy/MvE4QB2/Pb1yZ5xrOj2yZguj3N+cb5e3Iu3bTY9shhjMTaedfe1vn1AsXipxFrlBUdE7PZiPck14mkoaLfi5XnFuHp0aevdhZJaAkqicqalZsAsPh29HX8ibUb8w7ZU7i3q9tE+t77+24GVldfIJXaSSKxvv7JMQ82sUiuE3uH19ZPdKujKdpVs3BDNdhZ2HmTR9wmI4fXdry7YCt49ToXLhLUaiSPrM11a7XexbKGr/r53gvS6V0YRuWenS/mwSQWyU2A7dtr7iTzwKHGL+XbQRsYQEmtnemFENo933v8cAJRTMx6EccDN5ie1+Nvpv+GnndniRfzZ0/Ta92bJJGY+wc1nUIfHFiz8TKZfetmYhATs5HEIrnBJPUkz409d8dNgzVdQ1E3R/ZgtV9lqbu00dOIiYmJWTNikdwE3E1j34FtO7BSG9sY+H2aTpOaXVuTsXpej47bWZOxYj4ZKeWa18bGxNxPxBs5MWvG7Xi/3owLzQv4gc8jg4+s2ZgxH6fVOgaoV/1SY2JiriUWyZhNyYHSAeQnFKjHrB0f7jMZExPzceJwa8ymRAhxy3WUs+1Zul53nWd0f6JpGTTtwayzjIm5FWKRjLnnLPeWeWflnTUbb6m3RMNprNl4MTExMe8Th1u3CNILQFVu2x5vM2KqJml97ZKNDg9uHiOBmI/jeXU0Lb8lTBZiYj5KLJJbBPtiEy1rog+tXQH4RpEzc+TMuInug4CUIbXay4TSI587ElvIxWw5YpHcIpjbcggtjo7HRMyfOUV+aJhkdnN/2BBCoVL5MVx3JbaQi9mSxO+6WwTFULdkqNX2bV6dfxUnuL2OKDE3RjN0VG1rfMZVFB3LGkFR7m3nmZiYtSAWyZh1RVM0smYW9XZ8PRsz4G5Mf8StwsC2HZjJ9Qu9x9Na3IIAACAASURBVCYDMfczQghVCPGWEOIvb3ZsLJIx64qmaOwr7UNTbmPVU78MndjebiOZnp5mYWFho6cRE7Ne/H3g5K0cGItkzOZj6hkoTm30LB5oBgYGKJVKGz2NmJg1RwgxBvw48Pu3cnwskjFbk8YMLN/SB8GYOyCRSGCa5kZPIyZmPfhfgf8GCG/l4FgkN5AHas+ndikStrVCUeEe9y+MiYnZEpSFEG986Par7/9CCPEVYFlK+eatDrY10uPuU+yzDbSCiV65szZZW4rAAxms3XjZuOFuTEzMdVmVUh75hN89DXxNCPFlwAKyQog/kFL+4icNFq8kNxBjJIVWsDZ6GveGyi4o7djoWdwX+CsrOBcubvQ0YmK2HFLK/1ZKOSal3Ab8PPCdGwkkxCvJDUVNGxs9hZgtiPR9QjeuO42JuRfEIhkTs8XQh4fRh4c3ehoxMVsaKeX3gO/d7Lg43LrG+E2HoOtt9DRiYmJiYtaAWCTXmLDlEPY2h0i6fsil1bjPYkxMTMydEovkGmOMZ6+brSrDkLnTJ3Dt/pqfs9qv0vM+buNW77lM12J7t5iYmJg7Jd6TvIcoirouPfUuNi9SsArsyF+bPTqYtRjMPiDZs1uUMAxRlPizakzMZiX+77xHCEVheNcedHPtRevI0JGPCWTMvcUPfaZb07f9uPPnz1OtVtdhRjExMWtBLJIPCG8vv83l1uWNnsZ9S9ttc7F1kSC8PcOE4eFh8vn8Os0qJibmbolF8gFhMDVI3ozfjNeLglXgubHnUJXbs8pLp9Ooamyvdyc0V/oEwS3Zb8bE3DHxnuQDwlBqaKOnELNFcN0qUoaYZmWjp3JDGss9NF0hlY+N2GPWj1gkY2LWGdu3UYSCoW4NhyXHWUVKb9OL5OSBuJVXzPoTi+SNaM6CloBU/M8Yc+ecrJ5EVVQOVg5u9FRuiUxmz0ZPISZm0xCL5I3oLIOVu6lIdjodbNumXC7f+bmkhHUoD4nZeB6qPIQg/tvGxGxFYpG8EaOPReIFcP67EPow9Rxo14bNgiDAdd07P8/KaWgtwI7P3vkYN0BKuS71mTG3hq7oGz2FmJiYOyQWyeuxdAIuvwS7Pw8nvwEIMNPgO5B8D4YPwYcKwHO5HLlc7s7Pl5+E5PqFdF9deJWB1ADbc9vX7RwxMTEx9yOxSF4PKwuZQZg9BiunwPdgz5eg8R4svA39Guz8kY8/zu2BoiGFiridtH7dim4fZfE4yBCG724va1dhF2kjfVdjxMTExKwlYbdH97XXN3oaNyUWyeuRGwMJ/OHPQiIPQoVv/yMYexwWjkJ6GFbORCvN9lIkYo1pePtf42Y/hdNLkXn+eRqNBq1Wi4mJieueJnQD7DN1rN0FFOM6opoeXJOnU0rEiUcxW4cwCFm+3KIynkXVFfwgZKXjMJxLbPTUYtaQhObx0MDKRk/jpsQi+Ul0l0FPQH4CznwHCGD2VVpKAS4fh1OvoduQ2PNUtLp0OpCfQB8aRSEJK6cxE8Ok6cPxrxMWJlHMLFhpSA/gOVVq9VcoDj6D0D/B0yG9uVPwY2LWAwmEYbSXDrDScXhvrsVQ1rpne+sylAgl3sePiUXykynvhl1fhJmXwO+A74KeRHTn0bwGCjWUo78L5/6cdmYHtcwetBMvUgz/dxJHfg66SyRa8ySMNPb5H9AqPUxldAdi5jXY8wU0zSI3NIW+8gfgfhpGH93oZxyzxXCcFRAC07iLrOpNiKoqjOz8wB1qOJe4p6tIu+sxc7LG9kcqqFpsSvagE4vk9XC7UTh1+odgt+C5fwCv/i60Z8ggQSQhNwIyAEXH7M2Ts7vI9gkUluDdPwUjDW4HzAyG3yHnLiNG/zY0ZuDEN+j60Jj6MmOVPJz+99CvQnooqs3c9gyoerQfqUdvDucb50moCUYyIxt8cWI2C64bGaO/L5JSSuzAJqFtXFjSdWt4XpNUamrD5nC3mEmNkV35WCBjgNi79fqEAbhtGH4UDv08JLJw8GdATQAalKdAU0BVIVXC6K2QDlxSSh+pFqJM1fpl6CxB9TyKmcOUHky/Ck4LNB2rv0hu/q9xl96NEoHO/g2kyrByFppz0bFnvx3tdQKqWJ82Wzdi/uwpOrW4Q0XY7V4N/W0mMpm9ZDJ7r/48057hlYVXNnBGEIYOvt/e0DncLUIIUrnY6i4mIl5JXg8rC9ufg+3Pcfzkv2X41F9R6tVg23OQH4QLP4B+M0rYmfoMNGdRV46i0kcIHebfAkIYOIRT7aE6LTSqkeiZWTDzaIVJupg0Th9jfPcRSBWhswqzr0JvFfp1SFXg1Dfh87/Btty2e34ZzGQKzXiw3yxkGNJ+4UWShw+jDw5s9HRuyHhmnHJiY0OvljWMZQ1v6BxiYtaSWCRvQra8C3O/GYU97XYkgPkJUOZh6R3w+5AeisRRM2D2NcAHLNBNVK2P6rbA00BPR4k77XmQARU1gWuoBKe/QSfIk0gM44w8SuLCC2jpwchgQDGiGs36JchNXK3PlFJybrnDVDmFpq5PQKA0Or4u424lhKKQ+exziMTmz6wUQpDUkxs9jZiY+4o43HoTJrQ0aWS0Aqzsgewo7PxROPIroFmwch40E+wqJDKQG4XkCGEiDb0q2kAZYRh0CWmGdlT76LTxW/M4nXksv4lid7C8GbTX/jnnLvw1K/1aVJ+58Fa0Wl05Da/9X7DwNvUTv0+/exk/lMw3bHre7fUvjLl9lGTygXYsqtdfu7r/uZUIg2v/N7zlZfxabYNmE7NViUXyZiSLMLAfSjshPw6HfykqzWhchoM/C+OH4eG/FfmumllIVQgVhcDrEnQWo5Wm06YmBLKzAr4Prg34aF4bYdcRRhJTM1BkyCO2x1BuEtw+7PuJKPnn+NfBrkN9Buudv8Q4+z301ZM8Z50j2zq/0VcIgHM9Gy/cfPt2DxrNpUXqi/NrOqam5VCU65hdbGIaiwtcPPbGNff5q6v41a0n9jEbSxxuvRlmJrpdupLpmq5EYVU9CeOfgsmnYODhyFpOhuA7KFaGUCgovSr0aoBPXwFLeiASEHauXHgFUOn7Hs10CUsZR2meZUH2mApDwnf+GNVuoOd3UE1MIKePUw4tOP5NmDsRufR85r/c0MsDEEjJTN+loGmUjPgltZFI5JonGW3FriDZgQGM5LWh58T+/Rs0m5itTLySvFUmnoTxx6PMVUWLMlsvvwSVvXDi/wVFjfYQEzkYeQxt+7N4+QE6Ox6FoceYkCoFlCv2cwnABD1J1QtZDMukWsv4jQUSuV3kjV2cWN1No+lGbj+qSio/QLIyFVVaN6aheg66y/TPHaP62g8+mGdjGi6/ElVj34ROq8vC9N07XqhC8LlS9r4TSMdZxnW3VnguPzhMcXh0o6ex4SiKSjJ7F37KMTFXuL/e1dYTJSr3oDUbrSi7teg+uwVeHwYPRN6tbgeK28F3kaVhnLkX0Lc/jnWiHhkSqAZks9CpgWdjqSmEYpNAQ8/n0DM5KpW96MEFMv4Iuibgib+H9f3fgpkXYfwJWD0TtfGqXUZRCojSU/DWH8LF70NlH/albxPs/zKpHV+7svK9fobqymKd2lKT4Yn7x9mn271AEPTJZg/c9ViOs4SimBhGcQ1mFhMTsxWJRfJ2GTgAyYFoj7C1CC//dmR2buWiTNSVkzDzGkgfFY30zFmCrgv9RuSq017Cqy+j0EZFJ5UrYHVWqerjZBUJYYDyxu9QzI5BcjBasb75L6E5Q+j2kf0mar8VCXa/gdm+gO6uYgchVirLctNCCTRSC++AMRSVs/RrMPk0BE4k4FeY2j3G1O6xjbuW64CmZ1HVtclEzWYfXpNxHkTC0EdR4reXmK3PuoVbhRD/WAgxJ4Q4duX25fU61z1F1aB+HkaPRNZ1U8/B/p+MnHKsNM4jX8PJDxG4fZbdNtKXJDvtqHTD98HrRm48Ih91GumsRmYDYQ+jdg56y7Dnx2FgP26nhtttwco5QBAgcbsd0E0CRSBTBegu0Zo/y3TNhh3Pk8onycgAXS8Srs7Qu+wiL70C7309up3+68hNqL200Vfy9nC7UXg7vHE2r2mU4zq9DabXu0y1+sJGTyMmZk1Y7496/0xK+b+s8znuPUMPQ3cVahejukdVhUM/h1RNGst/hRYsktz1LMX6ItryKUDCxNPQmIPARbeSUN5BNz2OduYvMTWDnKJE+5ntFTDz0JrDERUwSxhhD/RhlIU3MejA4NPYtXcwHB89O0xObWHY5+nUuiQb79BteVjDBmL+FUzvFRjaAae/BV4PLr0clak8+ncikd4qCCUqueH+LsVY6a0gkQwkN7dxwY1IJMbQ9RzVuQ6pnImV3rim054TEPghVipufB1zZ8SJO3eClYsccUo7YMfz0X16AqEoFBL7IVkimD1Ds6ujbf8MjB0BKSGZgswwJEqweg69fhY/Ow5WPgrFWhkCp4VXmwYrT8KfwVx4AToLoGiou7+Gmh4Bp4UlcmgDj9AxRugYJTS3gXXyXyBWziJVE2XoIGLPF1D3/yitVpN5fTxqATZyCFpzsHo6mlP1PFzYAp/69QSMPnZNs+v7kYbToOk0N3oad4UQKrqex/cCguDmCWQfRsqAbvf8mmXoNpZ61OY7azJWzIPJeq8k/zMhxH8IvAH8V1LK+jqf794xdiRK2Fk8fuXNO+oHaaTHKG37GWzrPGZuF8H8S4R6Ad30YO4t2PVVOP4noJgY/SbGgZ+E6hnQ03TzBXpSJ2G76IvHkWaaEIl0XUS/Fnm75sYiGzw9AUtvYVglwlQFTfGodeZIGWUyukS88D/AY78E255B7zTJzrwJhVEY2AdzbxHOHgUzj+L2IuegM38drZAf+umoxOU+QoYhYa+Hmt78jad3FXZt9BTWjMFtt59dGgQ9+v0ZEokxhLh7S8Ty+Ob/m8dsbu5KJIUQ/x8wdJ1f/UPgnwO/QVS08BvAbwG/cp0xfhX4VeATmxNvWmQY3T6M14tWeo/9Lax3/wx/5hUIVXji56KayfYsPPqLkBmKyjR2fg7e/tfQr3O8k2CwfZRyQgVHQTcz6GOHCZtz+JqCvnwyyqa164QoSMXAcJuEZhq3MI70VtE1lU7KQPfrWKtnYeU0STMNQ1NRRuzpb8LcG7T6CcxT/5iE5kB+W7TX12tAohDVhQ7uo9oL6Xa7W+/v8hG86Wmci5fIfO6zGz2VmJugaRnK5c+u2XgPslNSzNpwVyIppfzRWzlOCPF/An/5CWP8HvB7AEeOHNlali1GCiaeuPY+ISJnHqA1vgsr8TMYvS7s+jFIDUBjHuZfB6FF1nO1s+D0oHaBXdkBEskhRGclCoVmR2DkEO7EY/hn/gK9uxKVkaARCAGhRxhYUJtG2fkMuemzqP4ylmqiZseiVe74EUgPRI8zsjD/JhSnSOs9yKRByUB7MbLV0xLQW4myYd/7M5LP/DpabuvXmumTk2jDcTLP3VKtVgmCgIGBjdsvbSz3CIKQ0vDNV4iBF6J+UkPzj9BsHsM0B+Okr5iPsW7hViHEsJRy4cqPPwUcX69zbSrcLsy+Ca151JQJiyfgqf8iqlX0+lGXDyMJF78XGQt0FmHnj0GqSNHIQmsZVBM+/Wvw7p/AsT/CmngS2aziKxpKcRylNImyeoFe9iBq4yJ69yzG6hmkkkLoAbrXi1aql34Yid/wI5AZgcDFS48hxh5HO/MXHPf75MOAscCGRAWcZuQTGwSwfI7E6b8goSqRwPfqMHT3tYcbgRACYT7Y3UzWAk3TNnxlpmqC5oqNDCUCQX4oiaoqSCmvmZvnBFx4e4WRnXmslI5uqjccV9Nya1Y6FHN/sZ57kv+zEOIRonDrJeA/WcdzbR4yQ5Gnq2+TEgIGqlEoszEdZcFmhyIru9ZC5JrTa4L0o6L/ZCkao7MES+9FfSUlUD1HtfAYZvU0hh9gun1Uw8Jbmsaxe5TTKZypT+O1pklffjsyLEgWIDcEvoTqpWgvMztId7WFPPsimc48qj/OgjFMKlunkM5Fc2stQXMaAj8yJxh9BN74l2Bm8NJl6kIyoGewuwFSs0hk7q/9y5hPJrcJogqZYgJFVWgu91i40GKHJsgPpqjVfohljZBKbafbdLA7LpMHStTmO9gdj/xgkkvvrjB1qIKmf1ww328S3Wk4nD+6zMOfGUWJmy7HsI4iKaX8D9Zr7E1Pex5a85GV3f6vRqUi7YWooN/MRGFXpxOVYKyeg14RzhyNRDQ9AoqAdBGe+FW4+CJU9tFTJ9DKUxgGMPsGdJYpmknQbDByqPVpwkwFfAesQtQPM1WB9/4tdBYI3vwzgoHnyOY1gqXLCCPP7pzJTLZA5vwLUPxStALuN6PzCw2W34vCtZ4DeorGmW9xKZFmwFfoN3W88edikYxZFwI/ZPZ0neHtOYxE9DbVWOqyfKlFaTyDqisUhhI0lrqouoKm7CCRKLJ0qUmv6WClDRqn6+QGLNJFk+pCh+Zyj37LJVP65BWjbqnYHZd2wybwJJmi9bFVqAwlQon3Oh8UYkuM9cDKXVv0XpyKbmEATjtqt5UdiYwI9vx4tLf5g/8tWln2a1HfyMCDM9+G2nnw+kwc3A4iC7NvRTWamZGoDKXfhH4LrTWHVt6BlD5+v4n2yu8idv0IqDr4AQQ2VN9D2f8LKNIDJG5zmZHZF9ECD1IDLC7MkAk7JDIVFpYSlLWLmG4PdBNO/yWVL/8W5cIkLB2nMDoCxeyGXeKY+xtFFWQKFpqh0G+7zJ2p4/kh82dqdJsO06eqCCEIfEki1yOR9Xjmpz6LqrssX2oxfqDE0uUmnXof1VCoLfQpVCwWLjTJlBK0Vvs0lntkywmslI6iCoKwi+OeJVUYxO0HdGo2P5j5IYf27mEiMwEShCK4/F6V/GCS/EDcu/NBIBbJ9cDKRbePoqiw7Zno+9SVDvLJAnbnEmJ0N+ahn4WFt6ExC90VGNwfrTZHDsH570bJN14vylINI6FjYA8UpmDuHfADAqtEP0iTbC2jHf1jmHgcmouofgc19OH417FrHUQ6y8Wuy5AhMYQCjWnSA1Nodhol6JJLu8iRz1DzBIXOHKK8D777m4jcOFiZqIXY4V+KVsbrSb8ehaYH4w4ODxJCCArDSZrLfeyuy8psm9nTdXIVi7lzNZorfaykQbdhgxDopsX3//QMEOL0A8SpGuN7i7z74hzpnI6UgoVLTYQQuLaP3XHJVpIMZnJUZ9vY/S5LM+fIlTU0XaXXdHAdnyE5RjBnsmi1IJAM78wzuC2LmdSQUlKb717dF425P4lFcgN541KNsUKSlNJHGX0Ys9uPMmOdNlz4Hhz6eTj1F3DuBVAEjgpSk1il7cgdn0MsnYiOtVsw9gi91iJKZoDs3BtReYmRiJJ3zFS0ogSonyOUA6yknmdS/DVKbgfIMWjOkF49BUMH8ZbPoKIzd7FPwnsBmQwR2QnorcLYpyLHnuZ0lGDk2VA7j5vfSSgllrXGfQcDLzKOj3mgWDjfQDUEy5c7ZPIm/baHqoDn+JgJA0X0QQnJVhJkSyl6DYeV6WWkDMkPJNENlQvvrLB4vkmqoKPpGlKGIGF1ro0MoTCUZvFCnV7TQ0/6hDKgMZ+nXZ1heEeeVN4kly8w804Tp7tKZTxNcczDDx2CZhmhCboth3TRQk3EInm/EovkBlJMGaRMlYy1Lyr5OPY7sONzoJk4tkBevoiVKkJ7FRbeQlNNgt0/Cs4xOpe/gZYaIRGGkQDOvAHdVXrDj2E5HbByOGOfxZ09RkaxYefno0ScjIFlFEjLBfTONHV08pkkemESf3kR2Qsi47fyAcZTabTlFZQ9X4ETfxGdZ/rVqDZ0YE9U7jJ3FDorrLgZArkOta7pgeh2PTrL0b5rXAu3ZgSBTegLNMO4Z5ms7ZpNa7XP6O7ClTmEnHp5Ac1SSBcStGo2nVqf+lIP+1xAoqBSGEwwf7ZDIqVSW2xjWiaKEKQrSQJfcv7oEgc/N0GqaODZARDSrvbRDQXfDzEslX7LpteW9BoeiipJZvJ4nkdjuUvghSTzZuS9nDOpzrdprnTp2j26tTbF8kOM7y8xsa90T65RzNoghLCAFwGTSP/+TEr5393oMbFIbiDbKx+q9RICdn8+at4sFLRtl2Hfj8PyGzBVgB3PoZZ2sVw9Tb7fxGqC2u3B8MFoL1M3SYqA5OJRepaGogZ0l08RDj1BwruEtngc6TQIy7tRVYNSJgR2UvRt1F4XssOIpAWDO/FWVrHa5xDbfxb6y/Den0clIIk8zLwObjsqdfn6r0V7nYXtjOz/2i1ZiX00Vf+O8Z1I9Hc+HxkgxKwJ1dXvs3iyw/aDnyFdvDcCoFsquqUShpKV6TYnXp5n/nydif0l3J7D0pJNOm9SXYrs5frNgH6rE9n5GoL+Krh9h+GpLJXxDEEY0G24nH1tEamCZwdRLpqAfsdDqBD6UXPqwA0pjmbwPZ/ly00CCcmMhtvz0C0F2+rSvKTi9gKslEk6MUV6XGFyf5HCUOzmswVxgOellB0hhA78QAjxLSnlK5/0gFgkNxPlK5ZkA3tRd6yAf6V8JD8elWJkRwjf+zeI5ZPok5+GN/8AmgtQ2QXd5SgjdfgQSm4IpXoGWn3c6rdpiQxF6VI3bNqrJyn7KqlCFbcxhxzch6qbMHcU9aGvIrc9i9O0kIceQ+QHoo4htYuRr3ivFtV2ZkcjwZx+GYwMHPgphG8j9BvXmbmBy/dmvscTw0+QM++8nKD35pso6QzWgZ+8agd4v9IPQkxFoKzBB4vA95FhiGZ8ckZysfQU2U9JzOT6CMDM6Sq9hkMQwo5DFeyuj2v7zJ9tMHOiCopgcFuGREojkTZpLHdIpHSslIYqFFIVne6qByHolkKn6V95ctCs9tAsjYc/M8L8hSad1T6GqZNIGQRhgKJB0ImSt82Mhmd7aLpKc6mLVCW+B4TQrftYWQXPDmnJLrpMkUybuHbA6lyXI1+cIjcQ11RuRWT0Sf59M1/9yu2Gn+5jkdyszLwOmVEY2A+KFmXDAqPlPaDosPAOmLkosae1BKU9URaqmWdRVQllyHYDGk4dTfVAL5JRR1E7S/hGBqwifv8tmOkQ7v4Cip6Eyz9AWGlSTzx1xcLujSgBafhgVMLi98CqgN2I3IKCIFpVvv5/w/Gvw/ZnwelC6MCRX4mE9EMYqsHBykGyxt1lxRpTUwjDvO8FEuCVRodhS2dv6u7flFenL+H0e0wcOPiJx2haGm0d3hVWZlqcfHmRoR0Z3n1hjlTeZHx3gdkzNSpjaSoTaU6+NE8iZdA6+h5mUifYu5vRXUXe+NZFJBLH9tEUQWFIR9U0dEulttRHJCR2K0QGkn7L4fVvXYQAhndk8TiHrk1gmhmWLjYRSoAiBGZCRUmq9DseCMgUErj9Dp4jMRMKvh3i6jYDVgqyaay0wdKlBv2uy5nXF9l1ZJDCUGrtL1TMuiOEUIE3gZ3Ab0spX73R8bFIblb2fBFmXo1s4tKV6D67Bfu/FtnI/dU/gp2fjUpLXvk/IFOJwq5hSCU3DNkJ6LfJh34Uwh0+iD77Kjk5CzShdpaEMOhoebrzJ8gM7oS51+DUX0XnS1dg+jUgRD76d+i/+0cYzQVUYRK2F1EnnkDaTVrzK2SYRVl8F859G7Y9Szj3JmLuLcQX/kcoXLtHOZS6ntXv7aEVi3c9xlbhiXwac41q8iqTU4Th7XXluFvsrkcYSOpLPWQQUBhKka1YEApcJ+D8m8vMna6BEKxcbuM4PhkzQSFlsnKixuTBEgjIV1JYaY3VmS521WNwMkFlMo0MFXRTYfFyC6mA0/Np1x0q40kWLzVRNJXyuKRdddm5t8TMuRqOK/DsACtpoFsSw1BRFQVN1TCzKqP7cqxOt+k2e6TKfRRZobncI/AkqazJ/mdG4vrgzU1ZCPHGh37+vSv2pwBIKQPgESFEHvhzIcRDUspPdISLRXKzMrg/yiKV8oMVWWc5Kg1JluHxX4bmTPT99mfh6b8Py6fg5DdJLb6L37hIYGRRtVRUa9mvgmJB4IJahMBFWBkS5W3g27DwVuTt2riIv3KGzthnyA8dBBEiRo+gVk+htqvUZYp68iA7Ft8mVAzqchdWVsHUZRR+FQq+SKNU59HmjsGxPwCrCDs+CwN7N/CCbk2Sa1haoKgqirp+q28p5dVaQojaVK3Otli61Ma1Pdo1mxf+8BQygFwlyfz5BqWxFE4voLnSJ/AC0jkdzTKxVZ1+y6Y+32Hv40OkywnOvrFEIutiJjWstM6ZV6PG4YWRBFZKY8ehARYuNHFtF6cXks4naC6XqM745IoG/SCk1fQBSaqYZM9Tg7z73VkaK11cGwxLYGU9qtMC3dBQNYOlszq5isvhL0zSa7soqsr5o8uM7ipQHl/n8qeYO2VVSnnkZgdJKRtCiO8BX+QGtqlx3vJmpVeLaiMXjkVlEFJGq8bJpyKh69dBT0H9ctRVZOgh8PqEdpXunqdpF4rY2Tzs/VL0+NrFqGwjmYfQj0wLJGh2Ey1RhqFDhM/8Q3ytghNA8/JxZO0irJxh7ut/j+rMUURuguzuZxn97H8M+34CNTvCNvFDzM55SCSj+snWLNrADlTFg+/+T3D5FegtR/WdgO/7+L6/wRc35k5ZvNikOnf9/ozVuS5zZ6NueHbHZfZsldOvLLJ0ucnc2Tp218N1AhCSQPrUZjvUlnoIRWJ3bTzXJ5Ex8R3IlCykjGzoPC/k7OtLLF9qY6UMeg2XxfM1XDcgXdRYudTB6XhcOlHFMcDK6PQ7HpqmMrw9T7Zi0Wk5rMz3UHVBGEryAxaX3q3i2j6qplIcSOB2JY49T6veoFN3MBIamQGT4X0pug2XwjDolkav5TJ9osbpVxauex1iNi9CiMqVFSRCiATwo8CpGz0mXkluVqwcVPZGpugIOPZHURbn3i9HTZMvvwxf/idRyt7lH8L570VGBNlRwonHyI4+jfLuv4lqG3UTpIh6SbZnwZFlugAAIABJREFUo7GdVtT1I7BBhJAeQi68Q1h+klTqLVKNGfCTkCijp8ok65egeR6tW0NbORH5zT73X8MLvxU1kh47DJ0GzL+BkmpDrxoZJmSGo/KU+gzs/xoLib2gWYwXkwSXX4Lxp1DtKlT2bPAF33rUai+TTE5iWSP37JzZUgJFvX74N1UwmT1ZpTyWZuF8E6cX8tBnx5g93WDhQgOn69Fv25gJHc8J0bfrmKZGY6lPGEoUVUHRwLP9KKGnH9DvRnuGmiE49PwYJ16ao9tuo5gOMszRbQRRtqqUuK6PFgiEVMiVNcb3F7hwrIrd9ZChxFYczJSO0w+YO1MjW0wShoCQNKt9wCddLKNRod/tEtLD91VWVmxGB0ssL8ywbdezDG/L0q45t91QOmZTMAz8qyv7kgrwp1LK63aoep9YJDcrivpBeFLKSETSV/bzRg9D4ERG6JW9UVF/dwWGHkJpzpMJMpEIpoejjNfdX4T6peg2/GhUi/n9fwbV88iJw7SKOdKXj6K051FUHdKDkUn7yW/AyjkGRh6NVq9OPRpXBpFRwXvfiMSyu0jt7T8kXZvB8L2oy0lzJsrWlUE0h9k3YeZ1hp/4T2HfV5hZbeNWwbJPMVr6SEDD7UXJSpoRrZjtJhS23cOLvzWwrBF0/d7uzyazH9+Lmz/XoNtw2PFYBSOpE/iSbQfLcHwV3w2ozbcJPB/FEAhVpTCcQjUEl46tMrIrR22+i6JIhCqoz/dwej6dmo2R1AnDEKfj06raeP0At+8jPZNuxkAXV+yO80la1T69VQ+97aEaKp4tuHS8RiKhIcOo1hEZUltso+lgJHSylQRCSFpVB1UXgAZeggPPj1FfXGFpZgXCLAP5LJVtBS4dUwi3SzKlRGRtV+3TqvbJ3sALNmZzIaV8B3j0dh4Ti+RWQAgY+1CIPVWCscej+1dOQRjQpg6tc2RWFqB+IVpt1i7Bjh+B7M4oE9XpQP084bFZnIROwkhCcw6ZVJFeA79wCMwyergYhXtDBxSV0G3iN6ZRCVAL26N9UFWF7jyUpsDzuLh8jDKCydAFLYE0kgTzRwmHH4bSNozmNLSqaG/8PlTPkRk4gjayHSuZhtRH6hznj0ah5NFHo2SlzkosktchmZxc8zGlDGi3T5BO70ZRbt5ezHcD5s7WURR47ZuXMC2FZNbAs30uvr2CYaogwO37tFZdjIRg6VKL0T1FsuUERkJDM8CxQ3RVJVVMEAQ9li+3sTI6jaUubi/AcQKaqz0GpjIIpUfOUui7Lp2GT7/jkx+28BybIAT8EC2h0VzuoaoqQzsyLJxrkSmb5AbBtwWDu7qcfcXFSiuYaROhhAxO5liebvPGD+cIGw7JdIZ02WB5ukVhMEVr1eHisWUKQ0k8J8B3gw1vHRaz/sQiuVXJDEai116AkUcwlAOIyiI8MhBZ1b3xL6C4EwqTYCaisg0rD7VLuLJLb2CERHIEkZskn8hDvYkcP4Dc9VW48O9h4Shkx8BsEeZG8ZdPoxBAYy6qyRx57Iqn6sOweJzDvoiErHYOOiv0s6P03S7m7DuoSydZUkZxfY/xXgO6q+RP/DEM7oHDvxw5+HyY8SdAXFldFiajW8y60anb9NouAxNZpAzxgz5RAuDN0QyV4nCSxYutyMWm7bFwrsHJl+ZZnm6hGSq5coJ+y43E0paoesDld1cojWSYPVunW/VJFw0CP6Cx2MazAwxL4PY9PNfE8wN6DYdMyaI220GzdLYdKLF0sYWVUpk91UBVFIZ35Jk71UBKSRh6Uc6bpaJoKomMRhhImosa2SGXUKnhOQNoyRCvbaNbgn7Lo6FIFrIhn06naM71SBdC0nmTi9UO88tdRncXaNf6HH9xnsNfmIyzXB8A4sSdrYJnf/B9GMD8sah7SGEbvPMnmMe/hUEC5l6PQrGP/AIMHriy7+hH/q1mGg5HHcyMvgPjT9CpzuEtnYKHf5owWUSpvwtLx6PyE6cD3VW02aMkK3vQ8jtg27NRKLSzTK98kMbMcaiejzJxvX4U+iUk4XVJTjxJojCBpZpkkxr5gZFoP3Tx7Wi1+OYfRr0qJVEC0rnvRM9P1R+IGsh7SeD7zJx4F8+xP/Y7RVXQjeh6K4pOsfA4qnprHS482+fMa0vU5zo0l/sIBU69ukAQhhimhkSSKVmYaQNFADJ6eSqqQrqgYxgqhdEEZlrFcQJ6zYD8UIricBorYdJruMhQMrw7j9tz0QyVI1/cBhJatT7ZikYmb+G5IcuXmyBANwW6FZkGuG5Au2pjpnR0Q8VIKjhti/rlHRimIJVOgpQoikKr2ufTT45ysCcI3IB2y8Z1A1RdZWokx9jOIqWxDFbK4KHPjF4VSN8LWL7cQoY3d5yK2XrEK8mtgNOB09+EPV+OhK52AY7/OZR2RR1A9n0tEpblUyCVaA/TzEThylQZnF70+8XjcP47KKOHEfYSzL+F44cEikLaduicfpWk6aNXdkW9MJ0u0ukStqdRQwmpHCQy0cquW8PprOLMv4WnOGgP/23EpRcJAo9+qJJO5UmYmahxtNsn0a1Grj3JEhi5aPXYnI0SkowMYW4Mb/ZdzGQZRg5G5S6+Dfm184L1Gw5CV1BT+pqNudkIvJBWtf+xQnehRJ0yhKLSWu3j9v2rJQzJrHHdvcarY/oh6kcaEAdBSG2+Q7vqkMxqzJ/tkh1IEHgh08dXCUJI5gy6dZuzrUVSBQvfCSmPJ+m1PKysge9FK1EAK2PQXO4TAnbbQWQNsgMW3Xoftx+gSIGZMujUXBbONfAdH7fnceH4CUx9EFVNomhOJMKeBBQUPSTwA9yuS3bQolPtE3hg96JVcnkshedGDoeqIfECD98PcVccWh0XVREsX2rSWrEZ21Ng76EyZ15fYO5snSNf2HbVYjEMJJ4bRD8Th1/vN2KR3AqYadj1+egrQGknfO4fwPs2cKXt0dfQi7p/LB2H3Hi0j+l2IFwCKwsTn4bpVzD2/TSG58Irv0Np57NR6LYxR2JwHK11DlQd32uj1qfxNBWBghq6kChDdjDybe03yU9/h46VYTlR5tTCD3hy+QxCD+mlAtJaMgr76ka0f+k5UNkdLSOcDuz5Esy8FoV0O8vY7/4/tLPDDJyoIBK5KIysJdZUJMO2g7C0+1oknb5Pp+6QH0xes1+mKCpDOyLbQ1UP0UIV255HCAP9/2fvzYIsu+/7vs/Zt7tvvS/TMz0rgBkABEAS3FdLtBbLiRLbSuw4if2SpfyQVCoPqcpLqvIQx6lUucoplSPHldJmSrRCihJFUiRIYhsQGGBmMGtPT+/dd7/37HseDrhJpEhK2NGfqq6+ffv2uafPuXV+5/f/fX/fn9JEfK23MUszxB/qzXQnIXu3R7SXygiigCV67Hz9FbbC2cJ4fBIiSRA4Mds3RrQXS9QXSjiDgMhLsGo6gRsR2BFxnJCmOaGXMjycYBgSpx6fZf/OiGAaUp+1kHWR3j0HZ+Rh1VVCP0YQIY5T0jhldr1MnuWEXoaqq0TTNpkighSgGgqhMyXPdSKnWMIXFGgOb1C7cIpJD1RDJ00z7GGE1RhRKjWYDgJERWI7VRk9vcM8MoomkcQZjfniRuJwY4pVVzHKKrolc+PZA+ptk8VzDVRdZmG9zihOSNKUtvru/Xy9FzkOku8UftjiTRB+ECCzrOhBlFRovBYsW6eKQHbji3B4FT78z6A8W/zNwsUiE5X0IrhWF+Ha5+HcL6FKEhxeht3LRFqOrMgoaU5SWy6WWP1B0WZy8tOgWkT3/gya88ye+FsYG1/BX3qEutNFiCPw7uMaMyTV01SlFA6v0w8DmqmH4Azg6u8DMvhjCMYYWhV16YMI8xeL93AH8Oh/CkevFq0vlbm/8SFUl366HV6apmxubrK4uPj6j/16Eyiywr9a8WpVC0HOZLqB05fIIpGlsw3yPGfzlT6dlQrlhv797VWaBof3JmRZxsyiwc2NDHkmo7Fk4E5CnHFIY7aEXirmMAZeQhgk6KZCc6GEMylMAUxLZbDrEIU5JCDpIkIuoFsKwz0XWRMRPQFZEzj5QAtnEuH0Rcy6xmjfJfQTyi2To7sTtLKMKIIkS8iKSJaDP43QaxGWWWHSj0gSkGSBgbnC4FUFdxqTJQGNWQPNSIldldCRsJo5kgzLUwVVEFl9qIVuKvR3HHRD5qGPL2NWFUBAUSVycoaHLpr+o5fPwzAmyLLjIPku4zhIvtO5/ZWixnfyk0Wv4veyh8keBHYxHHm0DTPnilpmlsHaxwn3XkY4/3dQBaEIoKUZKHeKQPrCb2HEAkIYkS1c4vn1J7m48Ryl7o3CL/bOn8BwCymOSG7vEAXP0hByqDRg+QMYd78G8ZRUMkhyH+wD0sYJntZO8sHpdVpWHewjsBpFZhmMERQDefcl0u0XIA2RLvxaUet8kxFFkXK5jKK8+y901cqDlIyMOCqWHwVBYH69hm4W//v3HHQ6q2VKdY1v//5tuvdlQqPG4P6I2dUaoiRQ6xjYgwBJ1nFGAUmUopcVyDKO7k2KpVpBwKgo5OQIeUqYZpQbOnEQs3i2gT0KSPyUKE/JEnDHEU4/QC8rWBWFxlyb0aFLua5h9wNEWUK3Cnee1lIxODn0Egyrgu9GiKoAYU7i59ixQqMeUCsdMdyZJ01S6jMWvpdiHzmUm2V0XaMxY4J0hKomJKGEYSmU6joHm2M6S+WidUUpsuzOj7nhOlcqblxDP0HVpWPl67uE4yD5TqcyA5XPFuKcradh9cmifaN5sljeHG+D2y9eax8WxgP2AYd3XiKon+XMpQ/B/MOFMKh7tchGH//PGN97lrBjMltt8IjvoydBkc2aDTi6Dt4I6fyvkPcEpO7XwEjA78N0F+IY9DKVWgOOLsN0B2n2Er948DVkf1hsR9GYqjr3lBIXBRFB1sGYIT58DjH2kca7MN4iL82QqRpvloxHEARmZmbepHd7a8mznKP7UxrzFjs3BjTmS3S3bRpzJoM9h/tXB5x4qM3oyMUe+oR+xKjrImsS3jRh++aASsskjUIqbQ1RlkAQiJOUdJqjmgozJ0r4dkJ1zmDvxog0zSnVFcxmkT2mUc7OnSHuOEaWBdSSQJzmJHFCc8li79YIWZYo1TRCN2F/OqG5UCJKEtzakGVrGWfoEdgxopCTpRBrAjISZi0lCnOEHPxpSpbLyDIEXkKW5pRqGu4wxB2lhF4KWYxgyQz9TWYrpyi3DM5/YI79uyMON6cYFZVS7QefxJ2bwyLQhylmWcWqaeRZzq1nD1h9qHXcP/ku4Vjd+k5n/mGYv1QIdcbbxXM7zxfGAVDU9BYeKR4fvlIoSeOQuZOPsNZpFLXCmfOw9iQ8/k8KFUMcwOf+N6QLn4NP/I8Y85cQHv1H0Fh9TcXqYc88hO/2aKxUURdWYPkjhffr9AjsfRA1GG6BPymmliQhcmOxEP2oJTJBQZYNDIBTn4TaIkRjtNBHSTO4+1WG+1uMv/ub9L/z35N/61+82Uf2XUee5z+qwBRAUkUiL+Lo/oThgYuQ5xxuTph0fUQJrKqK54RsvTpgeODSWiyRBAmSLFCfLVNtmXROxeRCRBKlIOUE4hirKmJYCpsvDzjYmJBFOaopY9UUAjsh9jNkVcR3HBQ1xrRkpKZIqrnolkJv26W/U9jfTSceV5/aYzzwcEcBhiVzsD0gaU0pzyqQU1jNzVhMhnCtpOBkGZ6dk8awdL5J5Kqkfoe5U1VUXSYIU2bXKghSTk7O/JkqZlVluydypOt0lkqcvNRh++aQjZd6aJaCqskETkx3ewpAfcaiVNcQJeH7frWCKFB6rVXFm0Zv+jk+5vXnOJN8p+MNC1/W+Yd/MC3k1KdA+jGn9tSn4OaXQTNR7QNSs8XB8A6zgw2E/u2i97F/F6b71CtfK9pAJneLiSKlNpQ6ZPvXEfQaqX0IcQl6r5JmObgTUn+PpL6EOe6DOwSvR2Y2SRY/iSp7sHsZlDJMNtiRZWK/i3nuH+BIOeXpLgw2EWSzsLIb3CW79u+xLJlyOEQQtwoR0rlfLtSvx/zcJF2PZBphrBfmDYIgMLNS4dVv77F3e4xe0jj/5Bzb14d4QsTS2SbdbRtVVah1TIQ8Z+3SDLv7NhMyOmrO3q0R7ZUauiGhaDKTroskSFTbJbZfHZOGoJUExl0Xd1yIcATAUiWmgwByMKoCWkVlGMfUrQZxlHLmyVmcblBksUcxogqyBIosEfopGjrqzWWuOYdoyxZTMixFoVbXeXCaEDkZ4zLMNizGPQ+zqpHECWlS1FkjL2XzSo/ZE1WccYCqSth2yEfefwJnHKIZSpHZ6jLVtglVBUGCw9tj7HFInhXK1lJdozn/o7M31x5qs3GlS3d7wuoD7bfgTL8zcGOPy4eX3+rd+KkcB8l3OoJQiGpEsVgKhR8EyPFOscS69Nhrzys4eyn6mQXkSx/HCSfc6r6E1ZhFef5fYwTTwgZuslMs0T7w64Upwc53YfERcIdkR7cgz6jFE9ANWHgUb3+XNDPRawKSoILR4IpQo12uMZPlpHefgUc+DkYDZA00HSXT0O0jAtlA3vozmByCqoDaxA3G+LX3Ua+VkfxD0GpFq8uV3ys8YD/7Pxf/1/ylt+aYv0ORmwbS93r7opTNV3qYVY0wiHCjlJcu75MmKQebEyShyCJ7Oza1GYP59SqTA4+NFw+pGCphKefGxpRKCp4dU25IyIrAxU+usPmiiXcUUeuYOOOi7SJyE0QZsghKLZUkSklTIIc8FklIqUkyqZ0QTBIO704hywiDFKMmkIYJeSKiN1UGezaKElOftfCmCqkqoRgSzihCtxRqZRPHCFk6VcG5NyaJfPQyiJRwRwGqKSOQYY8C3EmMKOWMDzyyHE4/McP29QHXntpj5cGQWsdk7dEO3xIiKkmKUdUot3RkVS5qtkAUJKh/QcSzeKZRZNbH/ESiusX9//CJ13/D/8v/87pu7jhIvtMx6sXXj0MrQdYkSWwAZLmMcuoS4uw8pAlV2eJjSx/DH14nv/BLMBlD4hf9mMP7RUN/7EFlgcgvQaijLF4sJo7c/lKxLGsfYK6cBfsI6cSvw6t/CHMP0NA6VKwq8ngLWUyKrPTc5+DWV2Gwgdl6BKGyyEz/xSIbVnQgg84ZbkUT1GFCfXIfLv7dQiz09P9ZLOP6Q7jy24U13nGQ/LkQZBHhtX7HNMno7zosV3WcUUyjoTMZB9z4zh7uNKK9VObKN3YYbDsYZQVygc5yiWk/YNz3UC7V6W2mVMcJ3jgicGJkTUKQRERJQitL9LoOaZSTBCBVRDRVxA8zsixFEEREoRhIU27rBHZKmiTYwxirKtNeKnF4d0qW5phlFdvdpzZTYnF9me79CUkcUJvRGR1OaNZl8kRAqapU2wb1GYuj7SnBjsvMfBVVlan6N7FLj7J31yaJMqaDgBMPtrBHEd7EJ4oyfDfm9uUuXphw8tEZyk2NasvEqmp8OFUR/IzKUolrbsDFso4oCKRpxuWXj5BXSjwxW/3+sdYMGc04vry+GziuSb6bMerQWMN17+K6mwBoaycQNQ32X4K97772svOYrYdh+YmiNlhbLrLRxScKxezah4hLD8BoAyHxEOYvFc46k13wBkiTLSS/Dy//v4AA/Tssn/og5f0XCwcgZw8iGza/Bf1bTLyQfHKA6exQrKNphT3K6U+DbHKpd5cLHCLFE7j/DCQxBE5h2H5wrXjfoLC3cxyH/f39t/AgvzORVYml801CLyIMYtYuNglHIc40JM9z+vs27sB/TRCdgJQx6nq4TkgaQXxlxPkwZ269il5SsHsRuiET+TFIGeORhyhLqIaMakE4TfGnGUgQBxmaJiGJoJdlVh5skyOQZ1Bpq1RnTG4/38X3AhRZIPRS0nSGLG/RnLM4cbFFtdkmiSWyNOfovksSp6iaxPjIZ3Bg409CZE2kXFdpLbQZJgKX91/AmYS0F0qU6zqSKlGqapTqOuWGxoMfXmAqZNyWU3IhJ41zFFVCkkUuP73P7z61yb/cPuBfbR9y62afg12b0I1RZwyuRAF7wY/WIKcDn9CL36IzfMzrxfGtznuASuUvZFxpDIoJzTUc5y5JNKLW3SwENo/9l4U45/zfLlx6Fi5BHGD5I+CXoLYKm98gEWuEPQGt3ENOk2JO5WQfEIul3//vnxWmB9VFWP8sBCO49xSIKklznWw4JM7GKMKLUF8hUy0O+xM63cvIaVos5dZWij7KG18ojAke/vtFu0viQ/c2HF5DmH0MUTy+1/t5Obo3QVGLvkZ76POdz/cJ/RTdUhAQcEYRii4Uw1gUhXJTp7s1JSvMbApPiCxl3PUQJIEYGPQ88kzAGQX40whRLZwKyy2VyH8tgKQQuDGB4GDqFSQh5/7LffxpQHOhgjP26d23yV/rVlJ1iShIUXUJzVCRFJHhnodrB/huRLVj4gx9ZterbPU8BDmnpclUWhZzpyoM9l1UU0FqPoqyuYUgipx6bJY4ygidGNVQWH/fLKIo0l4pk6YZwp0hvY6EI2bcv9mjokk8UMmxzs3yShCwpKt4lsp344AHxwHC9HnMyjnwy3SPAkIhpzpfwh0XDkCa+e5vJ3o3cxwk3wP8pX4tf1w42nTOoWkdFKUCD71Wtzy4WrR4xD5c+JXiuY2vk022GZw6Ta36PhSriRB+gXz1Y4jGftFn2b1JUWAKyKQmgqwhGA04ugpH/7wIzOU5aJ9AxUZ3RihpVATr+ir54B7a4A65lBHaY/JpF/2hM3j2AQel85zUbhU11of/E7j6B8Wy68wZLMXAWn4D6hrvcjonKgiCgKKKuMMIb1TUz1I1QRQVnDUDw09QhjG+F+FOI7IQPB3KCiAVyb/Ti5EMiGWwvRRV8VBNGU1UCdIMSc5RDAl+yMM+JSWNMjqzGqNDjyhxKVU1JEVElkVqsyaCBM25Mu4o4Gh7Qp4LZDlsvtyjuzvlxINNpt0Qq6EjyjA98gm7Ho35EotnW+zc6DPcc3EnIaODXTxXYK7W5OJnVrgVhQSqwKNPzJNEGVZVJ8tyNq/0WH6giaTLiAnISyZf7e2x0I1Zmx7y4bNP4gCvOB7fCnwqksQdWeDDy2tsDnSee+mIuVNVRFXk5itHPLFcx2rq37evc0Yh467H4pmfUB455m3JcZB8L1Jqw/qnAFDECvBDjdHVBXj13xfCmPbpYlDz0hMIcxcpYSPf+w4M7iK11ygpMrBYjO164TeLEV5Oj7AfINVOoTr7rxkY5MV3q0NitYmMOoYyA+NdMlElPLqDUW7QXF+D658nlnISpQXeBEGQqdrbxfJu/yaTb25C+zzVM49D4JCnMbx2EXq9yF/n7b0dkZWi30/RZebXa6T5AKcbomginixQX7TIX52SRCCoYNUioiTCyaqkfooAeJaIJmXICFhCjllWcAYxCAmqIbD2UKHs3Lsz+f77Npd1nJGIKOjfH94s5UXWONibUps1WXukwytf3cU1QyRF4uSlGTav9nFHHu4oRBAydm+O6axWmXZ9sjzDKKusmiqaJWNWZDpLVSZDF0vQ0DQVpxuTVjJqLRPv6oTZlQr15Qq5KnL08gBFk8hWLbYtkbthxIdVi1AVOV8v8/BDVZ6etvnzq308Q0QtCbxquzxSNnlq12F2cYbPrdZ4sTzhKNznE43T7OcCiiXzhcMR6yWdOU1hJCR0aj99/Ngxby+Og+QxP4rZAKtdCGm8YfFcaCO8+FsYlaWi/3LpMbA60L8FrTMQ24VTTy6CrKN99p8gDF6F27eK7FEQCpOB6S6IGbgefhhRXv0U4c0/Y+zn6PZ9hJkHQFBRNBklDWH/RYzTn8IIfBjegtoZ/KiFOTmE1gyELvZ3v4J7e5+5T/3aj297+TlxRiHdrSknLrbe9YEyiVLsYcC5D84zPHBx+x7uyEZttRhu2MxIIokl4dspoaKQxQlVUnRLxmjrbPUCLCGnNmsRhwmyKiFJIlmWoRky456PYamkcVZcaTII3ITQyUgTG98LyROFNALEqGjTCHK++5X79LN90oM58kRENeTCiVGXCd2UCx+aZ3erz7jv4PYjGgsl2osVZk9UuHVrSL8i0coNAi8iCUIkoUytMUKuGTijgNO6ThRlPLMz4htHIz6tlQjUDLvvsygLyKslkpbO7xyMWCrLOEnKbhChtWVOlC3sKETxIgTnBmelgF6o88WdPR6t17k+2OdfBiamYvDrookli+iiSJzlhKJAbeZnm65yzNuH4yB5zF/m/f8Utp4plkL7d6B7A1pni17M0b1iIPLK+8ndHvnOs4iNUyTNNcSbf4IY+4gv/mbRRqKWII2KzDUog9VC1ptUEhVhugHblzEqM2g1CSG0weuBmBdTTMIJIBQDl/1JISiy2rSUELF0unAGevn3MBULXQ7I7q8QhmX0c+f+RsHNrKp0Virv+gAJgFCUjxVDwaoqiIqCoFSpNVScQx/byTAMmVK9qKmFiYRWU3GFnFbVQMpSNE9ieOCiGBJaAlma4k4T0ijDEkQ6F0oM9mxWH2iSJimTvo8oJ5Bb+JlIToYpCFRnTM48PsvV0Svcv50hZyEn1zvEDkz6HvqcyeqFFrdeOMIe+hiPergv68iahF5W8JyQ7vaUyozJeMNGzgWqHQNVk9GsKlu5x8zJCdZMh93RIbeNFrV0wn4QsbGm4E0i5k+UmCawsT3lS/aEzTDlF0SVwwObG7KDK0mIdopLxoekkMu9MaoQMBf0cWc9vNjglClzPfI4a9Z4ejTlrGlw2ip8cOf149mT70SOg+QxP57l9xffQ6cwRF//dPF48fHvt164gk2edCkfBUzkCboO1kf+J7j/bXJRJLJKqEoDYXpYqFOb6xCMkVc+hONO0CMHRdYRvSH+9ABK8xhGA5x+MbWkPAeigrv3ElO1ypw/RVY0KDdhcgBphFxdhPYS6XCTjFNFgP0bBDhRLBrE3wvIisTsWo2je2POPD6HO0nwnYDhfY+sJBazsF8T6ji9qZFIAAAgAElEQVT9uFCmjgKmNYXurSF6mpFVdUQpRxTBtUPSJCfPwHdT5GrCxks9jIpCpaUzOHLJkgzdlBEGu4hyE8eysTURBiKDP7hL6USJCyWLM+sPEdgJ+7sDNMums7pKuWlwb04hvz3hM+fOYnxK59ZXtgmmETt7U6odi7alUW+bbKkpF+arLJ5RufzFTdbft8T8Us43b32d/cGU7dO/SI1D1rQybUVmXRb54tjlpSxirixiKhLNoMv1nkZStTCkhJcmCe7dA8YLKnY5ZyQ0OFOe5+p+xOz8CZ5xJGLf5Ey1RCkfc8XV2PYjLEVi8bUAKb0Xbr7eZRwHyWN+FLcPg3uw/Hjxs2KAVinmPyoGtNaL2iNgth8jG/bAHVE//asIxhlwu0RenzwY4qWHpGqbOJlSqcwjHL4MZovMGxFHLhpZkWkaFcL2I+ijDRjsQR5D7SQIOexfIZM0SFMYb8HZXyrME65+vphNaTXh4BUkQcD6zMeK1OiYnwtZl5ltGZx8OODlb2whyAJ5miMI4HsJ2vdENzoElkJzEpOrIpYqkdsRZAJxkFGqqQROQuilJHHOttOj5hu0Z6rcu9JDkgR8u+inrHVqBFOBiSlhRjpKnBdtt/czrE5Mf8emv+fg2xH1pTFxMCZz4ZdPZ9wYldgII6qqRrllUFot43Rt8ijn7BNzCAjc2RzwnVcOOH2izlFVROtUWRnfQj8yOaMtUTUMGpWL5LLNv9nrc9sNMHN4vFJiosGDlsnJtMflscmtWCVMZFQRzj1kcGPik7oxllbj4XoJUZ1wazxgqOjshGXC/h7vV7dotj/Kh+plFjWFFyYu97yAJ+tllnSV7e1tDMOg0+m8pef+mJ/OcZA85keRVFB/aGCvJMPKB37w88z57z8USx3E878G0z3ENCys6Da+AnGImENNXiRxu6QqCM21wm4lGCOlPg1RBLkEsQthSi29STTcYhKLWJV5ZH9QqHCTkLJZpbz6kcJ7NvHgYJs0i5GGG8UIrdUnC6MBd1QobY/5uWjOlxjsO8wsV/jsP36Il/58m6TvoWYRoZ2Q5wKilpPFYCQZlZZF4EW4fR/ZkElCXhNnRehlDVmXSZMUya0gCwIROYgCsiKiaCJWTUMrl6iXIvzUQvIiyi2dXt8HwBkH7Agp83MVZpYrLF84S7VjMnr1BuGoS72+TnDPYdHSGegSl8WYcxdbPKhqSKJItW3yaVPmKElwpiH3d6YcVCTEs2f5t7bEaSdHr9lcDhXKYYaYwbIuMzvJOD+N+LOyyFEYcsMWsToNZhMZL+gRChX2hZxQgUoYsorAybJBW59w+eqEPM94sG2xKZaJ/Uu8OHZw45SqIpNlGb0wJs4yvjlyqJtlZivWX31ijnlbcBwkj/lR9ArMXvjZXjs9gGBS/I2kw5m/BbMPoN7+42JpVrZQ3CMU6tC/De112Pgm3P82lOfB6RYZ6qlPwN2vIYsiuVVDUtWiJpmGIEggGLD1XNGzeXSN1O4RJAIqMUoSFb0IrXXYfQ5mz/1s+57Gxbbf5pln6MVIsoisvv5zUO57AYogsGBoSJLA5iu3KTebSDmc6JSISzFxktIfBwhxxlwNkBTWHppj63qfysyA4Y5FkkhFfVMSkGWB9kqFwa5PYNvoVYVpkGIaRZ9jHGXU5yzyBEqyzJIX0bVD8iSnfbZOdOji9CKmWszQCWi3TC7NlbCHPnbeZhKXybOI9lKF1XNN1h+eoeOHRDcm3NoesXS+QegmbE18NvKIayRcPN+AuZQv7N7GrUgcChlfD6aYA/joqM/EmqFRUaGccdv3CSKFa1lOXW9xqtRgLxJYDTc5nFTQlbMkIQRWDSEX+O1bhzwxL/GIpbM1lRjGDrnX5/60iqsarEce91X4TgyvTH2OwoTfWGyxZlpob/PP3jEFx0HymL8+nbPF99tfKVx6hneLYDn/SDHP0p/C4//FDzxk07QQ8+QUlnnrn4SDV8jHO3DulxGH96lNtqEyR9y7iYCIrFkgi4WattSBSRVp5gLKsIc8uk6h7NgvRoNd/Xdw7m+D1frp+37/24WKd/aBN/QQ/U3p7ziopkx7qfy6b9tOMkpScaEWJAG9DPNrJUTZYNr3EKo6VkMh3RhjD3w6qxa7t2xuPbePWA6ZWz6J748YD7qYXoMgyKi0FFbOt6h3Qo7uK0RhTH4YoKkC04GHooi058sM9h1ubk9QNInQlBkNA0pxgiIplKsKiibjeznKjsut5/YJ3IQkTPD9hM5ymdp8mStBwIOKwTROeeBSi+RMHaOksndnxE4UciCkdMOIuyWNchTQkGOeq2rYRk6ZFELY0urskLI59REAXRSR4hQzF5CVFk9PIsI0p63XqKQ5jYpOMDziKCyxJ+bM7cbspBW+nsvsmQlmALpSY64hU/Yyno2hL9osliw+1ixTUySWdPU4QL6DOA6Sx/z1sI8Ke7jFR6G6VLjjjHfAGxSGnNVFqAKNExBMC/u5G18sfpeGJAdXEEQZKRc4mNynl9pc9BzQKkS7l7FVHWXlQ1SGG8Xw6DSC3Rdf68XsoqZTnMSCowNK48/DB/9bqCwU7/HoP/zp4p3F94GsvymH6m/C/HqtGJvxBtCLE1SpEJQ4JZnuQ6d5eKZGYwn6OzZJnOJNIj78yVVEUSTwIiptj3tXunjjlD3bpVqpoOsqsZ1jj0Icz+baSzc5sb7G4tkGt54/4tHPrTGxI56/skNl7LLxUpduHONqIicNGS8VkQwVE4E4zMhS8MIEShLBMOKVnRuceqzFzlcFMkWglprc9XyuTyL2vZDfvt/n10+0iKYxyTTGuDPFT1JOLVeQ50pcPrhNPlFZDxtYQsS2nBPksORmNBOJ9RbcSBIcZOI8ZxaBiiSQuxGBJtFRRb7mzpEAC/0jKAcMMosJsNCQsZ2AqZIhqCJyDj4ybWS2xISV+TJZHFMRcuY0FTvN2PRDHiyb2MOA0I1pvQE3QMe8fhwHyWP+ekhKUbvMc3COIAmKPsjxazMkBbFYSh3vgFbClyKUPELOgcoiUTpBGd9HSEIqWYxx+CJZLAIyYXkNJRthLD3GDhm7QY8PSPXCkm64QRyndPNztKspsaAR6wqKrBSZqWL+bOpW7Z1xYfrenMI3gidqFuprx2pWU+j3YoapQ2uxTGelQp7nZGmOJIt404jQiwnshHJdZ+5knWpbwx1F7N3NaazoCOIEhIxkImAPAqY9n8hJ0CwZe3vKfL2EVte4z8skkyp6t0qclUmnEUMdpnlOSZI4v1Bma2uEHadUnAzJqbKrtXn2IZ+mIjI48Jm/MeaRf7TMPT/hI4qOJUkEeUR/x8YkR0wzDisi4uAeZuSguQbjkY2zmtCWLXp5zjDN2IpjLrgB5c4sr0z6XBTu0NfeRy+MaSaAJnIYp0wx0CXwlYxXQpk68GTdYmgl2E7MoiQTphnjcYxhKQzrAl0XnnY81kydfprx+9d3+ZXVDnfcgCDNcOwQK4GfYd3jmLeQ4yB5zF8Ps/GD0VwnPwaHV4uA+ZH/DnobsPnnhZhm/jForCB1r5DaPeTpEIb30AQdkS6xZqL6I1BlotoaeW9ANJriNefQXvkdZtIURVYh84ugrFpI3hCl1sbPVNRgj8SXUF74N1DuFDMzj/mZGMcpL9sev9CqshNEeLMajabFc/uHpIMeH3jgAVIBfmunyy92anhzOhubA9ZmBdLEI/RlpsOA1lKJwA1QLYHOyRlUVWI0CtHaBjOGTJRnvDC0cSsyjy+2aXdXiUcyV1tbCPMKC7QIbo2QKipxWeYgisADUc3Y70icO9Hi3kFMczdgtgmbSwn5CZ3kqZeIl+ZJO2UGo4Bn7nbZaIg0qzrLqsWztkPU15iYHZpmzkCTi6HIccwiIzaqbYaOh++47JUCVEz62gVOWzph7LIl5+hxQkWVeaJqsjPZYjXe4E5+HktQMG7c4KjZQS5ZlGSJ0HWZlyJ8S6ckKTxUEnnRCalKEbqo06malEyVr/QnPFQxeLJRRhNEvjm0eaxqYUrHS7BvR46D5DGvDzMPFDMfRQWu/7tCcXrp78HG1+H6F1CtNhizEIagV8gSGbxDVFEiT3NUqQndDVBL6BWDWklD0k7C5jeZ1aqFqXnnLHFvm6R8AnXpNEQe6pFH3liDyTYgFvZ3L/7bwk5v8X0/8+47owBBELDeQ7Zhs5qCIlgIgkBDkUlUgadefRUxr/LcECa9CR9plCkrEiVJpFrSKX9ihVqW8nvfuY+fBNS2bSqPtzi91uDlrzm8eKdP/nib82t1SGDFmnDl5edpxQucDAUUN8Xcb+KKMaXOGQYTEVsNefIzK3jjCHcSsde1UdoK5ZIKVsr6yX1Upc3VxMSb+Hy4WWPXrHA7Uzlf7vFHh1PUpMGOlKIJEEcxe35AlAlULZUoz5gIArYp8OmBwLaZ00kt0qbMDhWeLVWoZDmPqQIjpcxTQwcvg4oICHChZLLjhyC1qQkHPK74uGKDer2KWrKoqjItRaHkXMOoSChmi26SMghj0iRgFMOnWxU6TZ26JlOVRL49dHCTnH+42CICXn9Z1jGvF8e3Lse8PghCIcaRVfjY/wDrnymENic/WcydlFTszixue41x/WPIcRepVAOjjpD6MDnENkWmkoMQ2EhJoT71KyfwyrNEzZPF8OXKHJKzQ+3eH1MZXEVsrCHLCiw8wiBxiHs34PAaqH95OTWJIrLsxw/CDb2E0H/3jjW6OvW45wU/8pwkCHQ0hXGcMElSSnnO3fGEQJJ4Qqoi5Dk3XZ9fn21SlmW6Y5+XJx6BrOCOU5YjkbnzTcoPNTlxYY5HPrNO9kQLK0hYHqecExXGV0Yk1yIWFkrIisTNrk20bHLj4Qptq8TJYcLiyRq9WZXdOQVFgvTQx4tTRpZEt2PypVjDju6yerLCtTZ8yxvSOIhotyrMZwb/IB0TjyKODInDHB4MJU67Al6Ssi9BB5FKWnisT7KUs5lMv1Th0E6YJjlVoGzq3EDgZScgyGJiYMlQmZMk9vtT7rkhtdCjN5jjkWwT0j7O4jKNMETY28VztzgQOjybz3LTi+i6If4o5GQkYSew6ccEacqX+yNetF16cYwmCsiCwKKm8OX+hCDNftypO+Yt5jiTPOb1R33NnzKNwR9BYwXyBGXufYQbf8D0KKCkV0jVMqKoI+s1BECbexj2Xij6Kcd3SadbpFKZXKkhl8pweAulfQ4u/QfgDYmf/wPiiY41b0KWcdReJzLazPkjqK98f3e8aUSaZAx3b2BWa7RXTvylXW4ulN6kg/PWUFdltL9Q30yynDjP2Q0i/rQ3pqUq3G4t0lUyltZMHjE1vqcaisOULz+zjbRa5tAPmbkQs9DLOXH2JHGYQQ6LZ5tUd6Y8Ih1Qqy0z9GNe6akI6w9w7lNL/ElvRO4m6JFEeWuMfHnE6Y/O8XRJ4t7I487WmI86IJowmoGleYNzLYsHTq5y42DM/zGc8vhSjUYQ0+16fD1K2D+UsE+epFuPIYeZFOyuw15F5mwM93Uoywq344AcidGizn6ecRglyCosIKCTsukFhClMgRIpuqBwFERMUjCiMataDdVI2BdGvBA/xgSJg/0uKxMHLUqQjF2s0mMIISh2SqrDoSWwoKusOhmdfszUTLluB0zjhPWSxYomsxdGvDrxaOoK+vFy69uS4yB5zBvH3ktw648LE/TqAro9QK1qSG2drL9O7hww1FaohzaqpqGO9qG2XljY5SmRqKIM7qFlLkhrRRtIDnznX0AWIwsqsnQAyTwM7nG+dapIFxbeB/e/VVjryTqRFxNHKfOnzyEp783Zfos/xjf0thfQixI+WDX5k2SMorY5Y+lUVJGLZYvfPRjyq506fpLyWwc9rDkLO07pvjKhvZTiDFJmfYfpaIIT6yyvLNJ2A+71hhwsjdnbdikvlliwprx6bYu5eo0ZQaM7K7Fvy0gWXM4jvo1IkuVcnOZkqoJzsca4nhEMYh5rW/yrjU2cyZR5q8NNP6WjqnzwtIb16iGJpbDULqOPA742dugrGS+2RCoJOG6EqCpcat5lLJe5nzS4FSaYQmEeMA2nTHLI1RKTKCOnuCCK6CQ5tHSNuSSkF5RwXZgKMnlYollRaEsiA+B6JaMja7xfqKAJMk1LIrHgwA85LcrIusb5ssJZy2JPSFk1VERB4MmaxXcmHl84GvKhqoUkSewFEQvH/q5vO46D5DFvHHMPFSOutp8ujABkjdwfk1WWkR78+0wOv44mr6JWNbAHJKN7RP4eRhAhyAZGHIJehtWPFoWB/ZcgjopezHCCKOWAUgiFKrOw+RRc/I9gtFG0mnhfgvY5avMX3+oj8cZzeK1wS2qf/itflmQ5L01dHiibnLF01owcIfd5RD2ias0QCzov7uyx6zhsuhlXBjZPj6Zcc3x+wzBZrOh0ZQOjpfNMM8Csj0HvM3LnuDZxmTVq3Orr7Ma7zHRqfHx9ic9/9TaRH6GFPjebKvcmPmuLFu2/V+PLWz3qw5i0orDZlpgVY8xEZ+Egpp/E/KFr0643iTE5GcKzUcrdnTHB1GNspezpFYz9MbVpyKXM5dlajZ4ARpAimBJGSePPJzrD3MIFNKATQJQnzAgJpiEwCjOWSOgLMgvZVVS5Q1+cp50fkkW7NFoP0s81pFzAY8hhlLEmZjRTB7Wc4MQTjsqXqPYnJGLGPUmjhcCCDqYm84Ljcz0KuVi2mKQp50o6QZpxoWzy3fGQMAtYrFcRydnyQ1aM905d/J3AcZA85o1D0WHuwcIRZ7QNnXNIT/xTKk4f7n0T2Q7wwntkp8/Q8F9AKM+Q+z5EGUQ+LDxInmQcbn+DGalEYvfwxRKV0x9CmO4WrjzTLuQJid1DkA0kp1dMLVl5EsiLrPIn4fTAqL8uI7becow6iD9d/iEIIAoCAkVNcjMMmVF1PrH6cfaCiKMg4hv7Xfq6zImZRabbNtK8xX+1MsM4zRiLIjM1jUqY8x+3LQ5ezgkjg+6izP7uhHO9hE995uP8zt1b/On+hK6gMZZlTp2o8KXhmEZoMiQk3Av4XUlmGmV80BHYzyNiS+SZzEHsxay6OzQv1HnGNvjAWCYe7fNH9Sq7oUSsC9Rkk4mQoUdj8tDkekmjYdWZt1PWYpHKIMJrBNxFY2/aJNFlJBnUHE50Y/otmUZUYach4WQpc5KMJEpsRcvIiYkhpUwFE4cma/YVJizQF9tcsFa446QchilzssJOUuLRWoeXdx08bZOGLHFaMpixTmNOdhgkNjWjXZgH5OC6CZmb028InFdEPre2zJe6E9yRzadaFZKc4yD5NuNdcHU45m2NKBYZ5eyDcOcrsPk0hFPQytRrHUR3wK57RKNxAunuXaxEITSrKFodsXWGeHiP3UGPSj5CM2dI/Bz2LsOJj8LOZSjP4OzfIwkjzJJK1O8h2mO0YAr+oBAN/ST2XihUsPXVN+1wvGFUF36ml0mCwKPVH3iGDqOEkiRhSiLPjR1uOx5yZ5bZRoXnJw5BW+Efz9exRIEvdMc4ScJ6KrG/76LUVX6tXkH0A466Xba0Gn8sekTP3mK5YuDGY7QkIfFiXpm6yEHO1I+Zb5e448E0SqiYIr4lwjDGTiWulyt05hMYNTlll2nNKmy7MeZogC6KCGYNI8kR9mLkExptyefI0WmnKVIssp9FtBQVpW5g1y3GUYKoSCz0IvY7Ch1d5to89CRQlZwkTYkBL4U0TYmEKhkgZAHndIW9yGUXg2GmI8iwGaRYIpzzJUJLZTdNWUpEIl1msX6CESVuOBEfjLbZdaZcq53gjCZzoVRCEnKeFzM0w0QRcuLDfZZOneL9jRLnSiYPlY+9XN+OHAfJY94cejeL9hCrDe4RfOi/AfuI6rf+ORVZBmcAgUPePoMf3kcUZcT+PdTdyzyWuiAaUGrSdG9A34U0K7aT5dhCg3xugZruEgQBeZ5BfRHMGkTuT96n9c++7b1b32ger5WYJil/eDTi/RWLuixyGMbcD2LmNYU7bsDXBmPueyFbQcSvtWvUVJUNMSHZ9vljReSlNKGc7CH3p9SEOvGwj8cBw3SVPxpNEJY1rEzgwfk6fzJ2CP0YMUsoT3vEjTbfDRMeMCWmccIDtshyJDNRG7yqiGS7NkNForJ8GlvOKdsRC1KE04w4mWZ0kyp+x+ScbLDjBwSqQqoqhHbKpB8jtGUqlkiWgSoJbMUpqQSlPKAlDfBY4DDNsfMYVVaZOdoj10zW/T5dt86gtkKciqxZJsslnR0/5LSp84xosxEnrCkypRDm6zotq86e43O2YjE1ygz1mAuyxoYXkOYezmhK7tp8+vFFZnSVF0sWV4cOvzpbp6W+N2vl7wTe21eIY948Gidh4WGYe6AYcXX180WW9/BvIIgKaBY8/p8jzD1IbTBASbJi4HPqg2CCbMBwE+YuFYbqolSYF8QOc3NN5sO75OMDpOH1YiLJvafAqMH4XjH+68fxQwFyECW4vk8URW/SASnw7Qhv+ua+51+kLImkWc6f9idcdXzqosTv7w+44wYcRSn/+8Yhd92AtSzlKzv73HJcdElCPVXmRKeEGiTUshkSvU7PEjh7foY0q7BfLTHUJAZRQj4MePVgwkEQ8/LU5a4AFatOAAxVkS0NNCclzHIWNZGumvCqnLGpCvRrAt/NUo5SWG36zDczdEVkOxURGfBwekTSd5iSE8kSQzGjulYiaSssSRJOnlFt6Xxi7x7NYY8QqOPhImEGMD9MkLMMN0uwSwKuMmazvcAzWpkj0QAl577vcK07ZVVWUCSJpiZRlwU6gsBoGlISBRAFfrlTx49T7oVwvlolznM0UeDTrSrlksaZssk3xjZ+lrFkavxCu8q3hw43Hf8t/Qwc85M5ziSPeXOQ1eJr5QNQmoWn/lfo3oQn/+tCtVpdKgY7v/B/EysVlCwFrw9Gq7C8y2LI5aK+KapFwNSrBJqFMP8I0v4VcmKiMCfWItTYK6aUkMP6T78X/O7UpTLsslyymJ+ff+OPx2t405AszTErb76q0UlSBAEsSeIXOjWiNGM/jPm/to6YUSXOWwZTf0hVhkEc8+UoIUfA6zvMGgoTN+IostnXwTQa3LBdkiTnGUck85cRVYiiFFmReVkHU4I8hYoM3SjnxLRLM4mx51awBRAsBdMU6E8SYgTcskgigpBBHmWU8oyOUmM/6OFICc7/z967xUh23/edn3O/VZ26V/X9Mj09F3JIDklRIiXKutiSJTvaXdvwboKNH/ZhHXidBQLkabGPiwB5CAIsNlhgDQRYGPDmAqwDO4ljr+XEiiJRpMiZoWaGc+vpnr53V1fX9dyv+1DDIilSkkVS4pCqD1Do7qpzTlWfU13f/v3+v9/3VyjTjmskRxmumRJIMg3AHw0JzAxfyJhzZZ5SIg7397lrlhgZJjZQNpvc8hM8bYgZSCCb1DKBObtFOw2pGSZGJnMYJTxdKXIYBbSBO2HEXt8hAs6aKokAj63a7AcRr3SHfEcQEV2P3Em4GZocOhENvYAhCrhRyN/VMi7O1Vk0NG46PsuGhiGPTc+nPJpMRXLKzxdZg9YFeP73x248eQqFxtjzNQkICmt45hEl3UPS7bFzT+zDyV2IHDi4BrFHkoXkoUoq5iiv/xECkCOj2RWUmQuw/NlxxFpogHvyloXeD5OlcHCVX249jlBdQ3gfk+PzPOe2G3DG1H7q6Q61+Y/IQ3awz/6D13FXvsAzJQtblkCW2Atj2knC35mr8fLAYT/KUBBxyeikEJDzjJvyWbvIP/GGnJLjO3AchBi5yFIqMK/K/EUloiKLXEoiOpFAKEBBEpAzkFwPV9Q5BlJFhSTCCCTMosKsqbDX9QhLEstALMMAEAoyZyM47SRsVks0C2CrAg5F9mtgZVA7jlmoS5wEOvf3B4wMGJSarJk6D+I+O0oNmZCF0GOYWbR0GS0WKPsJlAV0H05liaZRIhFFvtEs8SdHp5wxLDa9lEESo2VddLmImOTYkkQmCPSjhN3eELvXxqvP4KYJomnyWUVBk2JUU6Ouyfx3Z5b51WaZ+35IkOUcBjFemvGZ8o/u0U3TEMiRpEffjP+TylQkp3w0zD01FkhJg7O/DOEIDq4ihPcRKwMkqvD03x23eyw9C//xH8NgE6QCqBZ5qJGnKVYeAxKIytjay27h2otsdttcCIdw/qs/vjAnzyFNkPL8fa9PZkAnSpjTlI/PCCSrzvriRbDNd9x90dL4rUaZ10Yur/Y8ulGCpKqsSQJiMm6huCmHnAx7FDWJfpggA0KeIysCt4QMf+BjphlpWaFQqSPuj2jqIhuKSDsMmZFFVk5TfKlJxwbkHCHMOJBifCljbslgKcyYlxW2tbEReAyIVY3dLGTWk8hNEZ1x60ZflYlzqJDjxgJGTUeQ9LFrT5CgDwUsew7VT6gECXOpxe00oS/m6BSJFxL6KVgWKAKM4piWoXPtpMvnDnaIyhYrgy63dYOOd5966RJ7qcSVoY8uwUBT2c5FCuUZ/k6zzKnj0SiXWNRVjIKF1D7mwVDjtxZncNOMP2v3+W9aFZYNlf/SdbD866xXz6BprXddJse5RZ4nlMt/c4vFKR8uH5O/6Ckfa0ZHcHxr/P3+lXFUGI7G64ozj49bNk7vgawhP/7fomsNkCRIA1A1uPcfx2uRlfVxFWdpGUWMUXFBNWDhKdLMI80CqK6SKjbW8fXxUOfy8o9/bZIMy8+/5RL0PpAEgc9Xi5SUj9H/nLKGWF1FfFvknOU5miRxkqZcHXpjrx1VJQV2w5Q3V04PY7g6cgn6PqoATyowyGEQJURpzF7ikmc5zxUN3oh8jpsq19SUu25ITZCJY50DS+DQlpBkmQyFo5qAL2ecpjkv+R77eYJrSPxS1UaVRQrAiRNymvukJszrMlLUoUifOX9AFoccNlVEP6OCSLNo8uuKxUyasRU6bAcJDTLKJ/e5b3VIbQUBsCVwc5mCALOGzBlznIbeGgV4okStUadsGGt08okAACAASURBVMzPzSArKcXiJQzZ4hu1Ei8IKmuazrqq8vzwhHliyoaBoPSQogdcd3xGpx3KeYyqqOwEIaYk8reaFRZ1lbtuQCeOMPQm3czCew9bumLxErb9C9Dn+wjzMfqrnvKxJc/HY7MAijPj6FEY943R3Rw3763+ErRvIZl1pF//Z+Ptd7437rUcHo3FdebSOOXavAjREOxFOL4Je1cIyZFJkQ6uYddH2POX4NP/wzid+rAPMj4+JnNdtDNnPrpz8Qjz190Ry4bKeVNjTlewJJH5JOEHfkIQx9QUkRcqRe44PodOzD1Rwsrhdjy2c4uApiRgl0yWdY1hd8jd7OE0NcYm3iopx2mEKGtUZIEC4AuQ5TK17iGBpVPTM7460+SGc8C/3bYZAY0QumlGJYUoz7nW9yirMNvv0YhrVGSZ/UKMnmfcSSJ63Zh/oBok7T3arSZ5Dq4i4y1fIMtDFnQFTZBojzziYDxaND09YdusoCNh91NGSsZ/0Itc8Hx6ac6XzVMOhVnuRhl/PXSZ1eHvmRmHWonNYoX12RpfqZX5NovMqDmyVOJMQSaTJZYaFaSHWYYz5rgP8lcbJX6tWUYSBL7bc2jmEWfNcVrViz28xKNuTAdpfZgIgrAI/CEwwzgJ9Ad5nv/vP26fqUhO+dljz45vAPbbimJ6W3B4BRZfAKMKtbPjdcS9V6G6AqufH3u/GjUozUHkjQW1vAyDXVh5cezzuvd9EE1yWQOysXAOj8ZR6Gd+F2prAORpCtnURPpH8VTRxJYl6rLElaFHkma8MnCxSXCyjDhM+HbfQ8gBzSRnLHwSMCOAn8N+0CE1mhx2XYrDHka9gZvElMMuqVkiDLu0ZIdN9RxKAskwxq8qCHnOU0aL1+QMQZW5HaQYssBo1KdRsLDzkKMcFmo2QzejcRKjFyUCuc7OfItWKqAdPUAVI7KsTkM0WF1r8CD2KeQ7lIU6MRayrHEaqSiOTyGNOV+r8K2+B8CoWMHLQBMF9DmdKIc0zfDjDEUQ+MrK5/nzw11OU4FemNIdDvn/9k+pPmlzWC/TcSLmdvdpOn2+8qln6B7sEekGXdXkDTfgieI4W7G7u4tlWVSrVbIsw/cintU0NPOtNpAj94hu0J2K5IdPAvzDPM+vCIJQBF4TBOEv8zx/40ftMBXJKR8dZ744doo5vAYnt8aGAzAWVKNCLsl4veuYgo1g1qB+Dqz6WATXvjgenHzh62C3MDv3weniL79Idv8v0Z0jpJ3vwwu/N3k69edYtfpxpKaOPw4USaKmKqgC3Bh5LHt9ts0yGZDkgDciUHQWNQUnGxccR5IASY6s1OgnKbYs41SLNGSwBAVPDBFwONEaCLmFJYAjg50LNNo+btPgRJUQLYFcytkIc8K0yFq0R00yuIpOruQcDUY4uolV8pFykWYc4AQRA0lgtlzmgpZgbnYpVhxWhCaWBP2kRT2MqOkiS2bOt7oBZUFFiXIMWUYS4LJtECQpt9weBcHiadvijKHz2sihqakYkkQnTlmRjtnD5vO1Ohkx/eIKv1mr8IbjAQLP2Ro7xxl7h4ckgwEDW+QklynGAcM8wbZtisUimjaOJg8PDznZG9CqzTF7tjy5FmfKZzjDNOPxYZPn+SFw+PD7kSAIt4B5YCqSUx5BCo3x18rqO4tmamuwf5VMNQgsHb38JJJkvPV4eQkKrXGl7Bv/DuIQvvaP4Y//R5SNvyBCRBRVKM+De/rIOer04oSSLL1jPfBRwpREnrEtMqAsy2yVTP7V4SlOAgUpQTUl7scZmTCuPD0z6FGyK7QDB01WWYh95uwSV3KDURTxG80yf9heIhdBF6DqyQyFnFAVGJUk5jWNKE15Vc8gBSsHSwTh9ASxuUBTD6iNTlkQy5ymAeupzqXOVV6av0SrsYIRJ+yHCbkfcUcpMpgr8gCff/3yJk/OVXnFAVPJ8Y4CvqWJJIZGqKn0BZFbnSG6ANdGPk8WTcpkJPF9vn26xrdklYYssuXF/MMzs4iiwGa+xmcaOqIAir3EYZTwQqWALAochhGWqrGf5JgbG3zqqadYsW0A2u2QMAwBKJffEsNWq0Wj0SAMQ3Z2dlhaWvoIrvgvJoIgrABPAy//uO2mIjnlo8eqvfu+YIB0cI3a0//92Lj77WjF8Q1g6TNj04H2DaitIR/eQs5TsBsgyFB991isj5qX+y4XC/oj7dH5zJvWdSWLOyOdf9XuE6UpXurzAANNkUkQKJHT13QsEUw/wHTbnC6sEkgyLVmmLmjspSIrlsZpGKMnAZuahcV4CJdLyuupQM54gQig6mekUU5VFjgc+uS+wJwks2VqPGGouPsBtxeeRrGb9FKBNM2wspDGsMeOorDQavCEXOXaSZdQlOnkCUqss+rH9IQQ07YoyQJFH46BqirQiXJKosiMWWPTU3jCsnimZPGfuwPaYcRLPYffWajzwDDYO2nzxbLFC6vjorBhkrIfRCxoKq8NXGolm1OrgGK91drRbDbf8zzL8vgjOMsydH3a5vEhURcE4dW3/fwHeZ7/wds3EAShAPy/wD/I83z44w42FckpjybLnwXF+Mmm3bUz8OzvjNcl178KiklyeJU46WFEDhz+YJyafYT45ZqNIj6aUeR7cb5o8s8eX+X/2DzgKJSJowSNcbtEQ5cJVJNQkFCKRdphyND3SSWBmiiynabMCQJOnBCnGTNhwHktZy9IqBQsHGQSQAHKQDUNmBNNunnMnl1nTZbZSRKWMYj8gAM3oGfr6FqNMM9JwoBPVQpkmcmeZRM7AcM4454cMyhqGLmIJcAvzapUFmy+4g35F6mEG0SsCyG7gkonzrEUkQdRxAvlIqdJzn4Q8bghs6ZrzGkadVWmqio8bhusqjN8plKYXMNXTl2iLOOMpaPLItrhHlnBRn/bNW6HMddGHl+p2ZNe3CzLEB9mUHRdn4hkr9fj5OSEtbU19qOEvSDms5Wx4N6/f59arfaOaHTKu+jkef4je2YEQVAYC+Qf5Xn+xz/pYFORnPJokiWQRuPq1J8klNXV8e36H0NlidxuIWz8BZzefcvgPM/B67531Ppz5uMkkG9y3jJ4sV7m8xWbMM/43+7tUZUl/DznxsAhI2GtbHMSx2jugK5hkscpku9xO07xM7ioqIj1JvdHDqEuYY8GVBWNWctiGKboEcxmMY9XTPxaih+EzFkqQSjSTRPCICCPXPS+il2K0Jcter7IG0OfiwUT1cs4tx0j1k8ZRQabDYEVQ+eZssUNt0PTSAjsKknPYU1IsLKIL7TqDJOMa0OPOMvZdFzqqsSKpfGDnsOaJvGN5XnOmjrmaMhzhoH4Q83/l0smbpJSU2VqqowvL6JpGoIgkKYpWZax1b1GRWkiCCUA7t69y2Aw4Nlnn50IJcD+/j6O43BwcAB2ifn6W4U7bpLymqjxNeP9tyv9oiOM/0P558CtPM//6d9kn2mf5JRHE9UcV7fKP8KuK03GX7MUjt8YV62ufA4kDfHsV8l/45/Dhf9qfAwY90xufWta3fo+SNMMwowXKkXOWhr/tt3jl+slnrJN7o98nigaRLnAthNSMRSeiV3Oy0Mussvig23mwojPlkyOhYyDQR9pNKSMjKhqpLKKLAj4IrRVuGIUua6kvNE5QfZdXus5dPoR/TDD00sctua4tagRz4TMSQPMOEEKE153fV6Yr/D152cRsxFZUeC8paFLUFVkXqzN09TK6JLIP7m4TEFXGRQrCM4Isd9HFmBOUzlOMvw8Z00ScESRO8j045QUCO/eJdrZmZyXXq/H1tYWDVWhpan4sYfnPcAwjInwtdtt9vb2mLNmeLzUmOxbq9VYXl5+h0ACVCoV6vU6C8vL3ETh1HHYu/oarutiSiJPlQoUPk79uI8enwN+B/iyIAjXHt5+7cftMD3bUz5+dLeg/QZc+PWxr+vocBxJmjV4/u+xM9whvvMfONc4N07ZAhRbcPEbv/BTP94P/SMPdxjy2MVxFP5ixWZGlbg2dPHiFM3LsCQBxRsh6SbW6hOc1x1e73W4P3uGJU/EJ+dx2+TSXJXbfYdelPCaNy7QkYKIFJEIUHJIBz1mhZxUDTlNJeqCyoyps+cFLMkwb+i0jDJVXWErHrGiaewHEQ+CgF1JZGFhngWzwBUvpKoo+GnO62HInO9yT4C/1arxjZV5jsOIk6Mj3NEI314gyDJerBRYN3X6vR6mILCmSCzpCkuGRv7pTwOQ5jl5Dm8ksFIscnr1Kq/bFUqllFWxjWmuTM5dq9UijmNOT0/RrLfWoEVRpNfrEUURCwsLk/vvxjmxqPD00hIrgohIjlMuIwgCWZbhbW8R6OcxzWk0+X7I8/y/MF4O/xszFckpHz/KS6CP01ao1tjWDmB4gLfxp7TUIrJSHFfJiiIkIQz2Jv2SU346qnMW5Zm3PpR/qToumkpyCATYjlyKssZCVODy2SpqO6EqtUjlhFrLpC+KKKLESRhzW4CKofO9fheB8XztU0RyYF4bz7U0JJXDIETTdYphQqTk9MkxdJmZooGc5gzSlH4q4SPwW02bUS6w70d0um10S+OZVoWvSSL/vjPATzK+3ijxaldkNkvQBLg29Dhv6pxdXeEPVYu6KPKlShFEgQdeSKNW41fSjMuDE5LuD7h7MmJQf5GaJnPL8fmybWBpKm77iN72NquPWSxW15HFd77HwjAkTVNOT09pNBpIksRLBy+h+Ao1qcbdu3fRdR1Jkjg5OWF1eYXj4y7324fsx/vohs6nL7xAmGV0u13iOEZVx9mVdrtNFEU0m01836dUKv2c3hG/WExFcsrHD1F6b8Nysw6ChJiLKPOfGVvf9XfHtniROxXJ94kgCEjSu//5DrOcvz9foXR/gxvlBTpxyq80qvxR2KEe53x2dpVKReevRw63Rj5+ltH2I171R+wz/vA5bypUJJlCkmDJEkme0c8FbsUCa3LOp8sFDsMYSxT4XLnCMEv57bJNRZUpSBJut8N2+5jLa2dZ1BP+7PQB9ZM2zy4vY5omT5WKbHkhTpJSlwSEzjEFAb5WtvnebpfGYoXfW57lMIx4VhW43m6jjHZ4/rEXcbKc+bkaw62/4n53QM+OeNw22Gu3uXZywHPnz3OgKix97WuToptOlGDLIoPTUyRJot1u03Y9bh++TrOpEvdy5uwGtbkZBoMBMzMznFz7c24fBSjlGSzL4szcLIeHD0h+8G3U0pNcLZzyxuERraM9qtUq169fp16vU6vVSJKE09NTdnZ2eO65596Vvp3ywZmK5JRPDrKK+djfxru1yeFWiDo/R91pQ+w9chWunwQKssQXZ5p0alW0MOKP0w7fvHbIRrjFXb/Ks4UCFc8gEUOGcU4UnSLLOQk2VeBpAmq5yIJl8rqXYUYBX2lW6EsKlwsRq6aOIAgsGCmfr9o8XTT5Xt8hyDJWDA1NEtkoVXihWiVKEqws5Lnlx7ikiQRhyBU/oapIDDodlg2VJ2ZneV3XOU5zLmgqHVmlpck0NJUwTukcdjGEGD9PMCWJqiZxHMaUFRtraY3FchFTkmjU6ijlMjdu3ABgZmYGgE6nw/dOhzQlUEcDKtUqYRjSPT1Fs2vs7nYZXbmOYJfYXZjn9e3XWZlZ4YzVxFoa4Y5iXjo45sViEcsqs9RY5/zsAv/ntdfxXQc7j9nq94jihPPnz4/XKU0TRVGYn5//CN8Jn2ymIjnlk4VeQr9wAXsYgazA3pWx8cCUD511axw93XI8Hngpz1UKnBKwWm2CbKNpGv/3UZdWkqKZOmdlk8eVjN9fX8GSZU4HQ/7zyEXqHPN7tRIvaxYvDTzQDej3SMQykapRlCUuFQ2Ow4h5TeaaE/Dt3ogv12weBBEl2+R2u4Pvujw514LhgKPRiKg+wxtRgqxonCmMK1LPq+I4TVmp8NnzdRBF7rgBp6OQXk/kueI+rxZm2Qoizlk63zzu4h9rrAtdLs6OMxEXiya+75OtrzPIBkREyJlMu91G3d/n/s2blGZnqTz/PK/uvEpJLNFMLUY79/E0nVG1geztMlufZXV2lZNOh739PVYW1jiKQnaOdhEtn9n5L7Adx8xEO4TxES3D4qWNXWTd5vr161QqFRqNBsVikcuXL39k74NPOlORnPKJQzQMCoYxbvuIHx97wk75mWGJEi9UbJwk5VANKB8brK7X+OvuiBfKRS4VNO77MQuJyLOGzGYQE2QhM7pBMclpFS3+ZBDwPz++TizJCMBhQWYnl7kfx9QlCUuS+OZwSA58o1nGSTP2gogMaKgK1aV5BoMBOw8eIAgCi4uLPFatkuc5Dx48wBIK+L7P9773PWZnZ/F9n0ajgVEqcxIlPFnT2d2+zV+5Os/MFFiwxlHsBTlnM3AxFYEHD4/tF0sEDzY5W7K5PryJlFpcXrnMaDTi8cceYwOotFrs7u4iuzKVVoWb128yUyjQmJ3lYH8XQ00wCiqyLDM/P893v/MysqcyCgb4ly/Rf/UOyfx5Dno9jjvblIycYdxAiaEyV2U4HHLhwoVJerXb7VKpVN7XPNSPjGgEW9/+qF/FT2QqklM+uQjCeHLIlJ8pM7JAGEYUS0UuF03cQkQoSTxdsvhyvcSNkQcEXCqWkUWBl/Y6XCqafNrp8YVqhd5sjXvXbrLdH/D5pXHa8Iy1wGNxgiqAIoyF4Mt1mxxQRZGqKGJLEupDkXCGQ3a3t7FtGyuO8ft9qFYRBAHLslBVFUVRWF5eZmZmBtd16ff7qKrKejBCyjVWm3VmlpaZM99yvrFLZb7w5CW6R0d4nsdxt8uDeJPPFm2u/PWfIisx20mDva09ZEFm7/CQfm2G2ZMTaoUCak2l0+2gKAquKFJVFE59F82HMyWZjY0NoijGalQRDUj1lM079xkdd1H0LpluQGmVxdkGRUVha2ubwWCfM2ce48qVbyOKO1Srn+Pll1/m61//+o909nkUaUgm/1P5yQ/9uL/Pv/xQjzcVySlTpvzUDE48kiSjNltgMBjgOA7FYhFBFChUNAq8ZZi+H8o8JVuUu6e8LKrYisSX6iXMO/skqsJiuczff+YJrjveO56j8kP9gEoSc6fTpVqrsWhoyKLAjDaenCHLMvVGgxNFp7G9hVp7q7Dr7cJx5swZvGuvYxYLSJUKx8fHBFmGLsm0FuaRkohu16NarbK9vc22pHGhPPZfzbKM7sEext73uXPpWUpnn0ETcs6JCl23y97WHm5Zoet5DDonDOU+n1usE4QBp9KQ7cYcmushuS5STWNv4yo5BYx6i64gshneI8v6fNr4NJ35JfQ0QzzY4+LaGoHnce32bUIgDAOu37iFpiqUKzbdbp9er8f3v/99qtUqly5dolgs/gyu+i8mU5GcMmXKT40oi5MPj0ajQaPR+JHbPmOPfWCdm6/zfKPFb589gy6J7Fx8nOuOz68DKeCmOVmeIwoCaZwR+gmm/ZaZRBzHHDgeabHErDtCkGUyy0KSJCzLQtQN7vYdpKefhiTm4OCA2dnZd6UglVYTL8vY39/nscce464f8Z/2j2l0h6xnMQe7bT5/YRklTZlzXYKdTbY8H39ugcW1da72Dtne3uXXnn+Cs2fP8t3vfpeCUuD8+nkGesSZkkZ7IJJ32qQlC0mU8IYD0u5VOpbJzMwM/X4fOQjAzjDlPlZbIVk2eHb5U6wqS5y89G0OtjsszC2yublJx+8wCkfMmrOUy2V6Bz0URcMZqRSLMa1WiyAI6Ha7Ez/YKR8O07M5ZcqUn5pi5ac34y587nOsv+3nRV2l9jBanNEUfq1RmkxGGZ76OL0Q034rIiwWi8zMiUgIREdHiIrKniRSLBZptVoYksiXauOozwtTgiDgzp07NJtNwjBkdnY803Q/DCmXy6zXauy+/AqzS4v89pkF3FGHO6/eoN/3+KP/9C+w157j9oN9zgz7uPPLJDu7RINTFNlmdTjk5tWrfOc732GY5tjFIucX56npJjPlOgPxECHw6Wxv0/U8wjCkrOuQ5wyHQ+bm59k8GKDk+xzvL2IYJdbdIs2oxol7QrFg4HoGd4OIVdNAF3QUW8Ef+WRZRpImaJZG4AT4kY8buCzMLvDYY49hGG+bmDPlA/OBmmoEQfhtQRBuCoKQCYLwqR967H8RBGFDEIQ7giD86gd7mVOmTPkksnd/g+FwyI2Rx5YXTu6vzFgsXKi8a/unbYsnbRPz0iXktTNUKhXCYomX+847tjNNk8XFRer1OoqiEIYhSTK2MrRtm9PTUw4PDznd32fnwQ5tz6d772UayoDV9XncwhyDzoi188u4S/MMw5hnaiVmV2eIajHNC+d5bG0NzXVZXlrgS1/6IrVajf07+/T2eqiqSqlUwg9DVubnmZmZ4fLly1y8eJHUsrgSZjy+cA4zWkSMBIx87POaJAl/df2v2O5vs7Qwh+GeMGwfk4Ypoi8iSRK2bWMWTTIyLly4wIl3glyWkWWZbreL4zjvOm9T3j8fNJK8Afwm8H+9/U5BEB4D/jbwODAHfFMQhHN5nqcf8PmmTJnyCSHPc5rNJpZlUYlTFEFgNBrh+z7NZvMnVmoOBgP6/T7NUpn8h7bN45iNe/eoz8ww8H22trbwPI+1tTWq1SqaphEEAeL6WU5HHq9ubBJns5z3TznZvs6l5WV2f3CF7r7H/byGYRi8dvUq/miEl8bMXJjnTLHI+tIScbnA9179KwpZgYWFBW7fvk25XOaxxx5jc3MT/+HvefPmTeI4JoxjLj31NOtnzzA7v8KN23fodDqM0oTbt28TuzFxHrMb7xLJPiUK5HlOd9ClVq5hWRaO45AFGY7jcG7mHOfPn+fGjRs0Gg2iKPpZXrZfOD6QSOZ5fgt4rzfzfw38yzzPQ2BLEIQN4NPASx/k+aZMmfLJYWdnB03TqFQqzEvjSS/DcJxOfC9GoxGSJGGa4z7FPM9ZW1vDTzMWdJUwDJEkCVmWcV96iflGg51M4OSkT6PRwPM8dnd3OXv2LJIk0d/awvQD5hSZQtEgurnJ667Hietj/eAHJIpIoIjMOyFnyjVuHt3DUi08vcmtrS32d8evf7ZkkiUpSZbgui6+KCNsbaM9fC1hkuCfngLjz8ooCDi+c4u7WUK5XCaOYzzPY35+Hs/zKPgFNF2igYGSKviRT5zEpMWUgl0gCAJ832d2dpZarUaapuzt7aHrOnfv3kVV1Y9fO8gjzM9qTXIe+N7bft57eN+7EAThd4HfBaZTuadM+QWi1WpxenpKmqZID0XStm1s237P7YfDIYqiYJomcRzj+z4AL/cdWpqC0TlGVVXm5uYwLl9GME2s738f/+CA2mc/i2VZHD1s5dja2uL6jRsEgoIVuFCtoAC1mRk832PkjiCV+dwzv8xrr10l3d6lYFoMhkNmkGi1WriuQxiG3HlwyFqakikjbu3vk5kF3DwjODigXq9zpTcgRKLi+VQtk3K5zOXLlzk62GVrawvf9ykWi/R6PVZWVgjDEC0MMV2XkWURhiECAspQwZVd1JpOmqYcHR2RJAlpmhKGIU8//TSj0Yh79+5RKBRYW5vaMH4Y/ESRFAThm8DMezz0v+Z5/ic/arf3uC9/rw0fToz+A4BPfepT77nNlClTPnm8fa2w3+9jmuaPLTp503ot2t9HdRwWz58H4NPlApoogLGIl2UPTQwKeIMBQ9NkWC6zs7OD2prlpD/g5s2bmKbJE889x937m2zeO6JlmWy026Q7u8hxhG3b5HnO5uYm55YWmO126bgucqFGFEXcvn0LXdcxTZMwithIM+yCxdzSHKEbkmkKpVIJURR5dqbJsePhDgXyPEeSJCQBJO8+eVzAdV3CMKRYLBIEAefOnWNjYwOvbKF7MVEUTdZT5UKRQz+k0WhwdHTEwcEB586dQ5bHa5JHR0fMz8+j6zr7+/tTu7oPgZ8oknme/8r7OO4esPi2nxeAg/dxnClTpnzCiLOc7/RGPG2bk2jn+PgY13WZnZ1FUZQfu7+gKAjqW60hpvSw/lCSMASRbhTxZ5tdnhQSsiyjUChwcnJC1BugGjrdbpcwDGk2mzRrVe7eSnAchxSBRFMpyg9Tv86QmbkZjEqFV7a3satVms0mBwcHhGGILMs4/S61moFZmyNMQu4e36UQFtA0jaWlJfb29ogBspyVSoWB4yAVi2yd7PCG62L749+jWCyiaRq+7yPUBR6ED/CPfWaiGUiYTP442d3FMHSGqoooilQqFURRxPd90jTl3LlzmKZJt9ulWn2PIQBTfmp+VunWPwX+H0EQ/injwp114JWf0XNNmTLlY4QswKyuYj5MscJ4qeX+/fu4rktR12lvb1KcnccPo3f1YIamifjQi/WHyZIYO03YcYdYps7JnTs8AE5PT2m1WohZwqF4yDzzXL16lU6nQxRFjEYjdEFASRMURSEIAiRdIiCg0WjwWpoyOtnCcXr0es5knuOlx5+gt7vLTvsAERHRF0n1jJNOh+gH1zEMnURVSQyL3b0TJFFECx2ObvfREwurYk2qWlutFgcHBwycAcWoSCktIUsyRtHAcRwEQUBRZERRJE1TNE1jfn4e27YnFa3FYpHtB7ssN9eZm537mVy/XzQ+aAvIbwiCsAe8APx7QRD+AiDP85vAvwbeAP4c+P1pZeuUKVNgXLxy3tJRxHeuyqytrVEulwlu3MC7e4/AD3Bd9137DwYDhsMhACdRTPi2Qp+9vT2G176D5r3Bn9zZZCipSJJEmiTcvXaNBxsbLJqLLM8uI8sygiDQbDaJ43iytud5HlEUMVOZwcTk3t27RP0+SSTTbncxTZNKrU6sm2x3u3QVBV/0aZxvsLq6Ol4rzQEBTk5OGB0fMy8JSJJEGEU4jkOSjAcve56HLMskSUKnM7av80Yey7PLNBtNRFHEcRzyPJ+kdwGq1SqmaXJ8fEyv12Nzc5NXX32VTqfDE5eeoFwa+9ZO+eB80OrWfwP8mx/x2D8C/tEHOf6UKVN+8TCfeoozly8jyDL193h8BRNPdwAAH2FJREFUZmaGW7duUSwWeSPMmNOUyUSSlZUV2idtrm/e4zjtsBXELEUejXodqd/HdV1a8jz3736LPK9z8eJFXNclC0OiLCPNMkRRRNM0DMPg9PQUZzDAzHOGfkjBtvnKV77CX/zlN/HjmIJp4LouZb3MaGvcvtKo13Fdl6E/IM9ysmjs7gOQpilra+e4d+8elmWRZRlBEEzGbdVqNW7fvs1wOMTzPNI0pVB4q6I1SRLyPGcwGExaWYbDIaqqkmUZ7XabS5cuUV0q/7wu1yeeqePOlClTHimEn7AmKUkSyw+HKv+S+VYLWp7n9Pt9FuYXuHPUpnzcRu60cWQZwzBIKxUS1+Xw8JA0PSUMcwaDgFKpxLDbpdKo0LcSykmZwAu4f/8+oigyOzvLbhhSKBQmxTUiOXMlm9FoRBAExHGMaZosLS0hCAKDwQAhESgWi3iex2AwoFarUWg0UAyZs2fPkqYpURRxenqKYRgkScL9+/cBiKKIOI4RRZE4jskeinej0UCSJIbDId1uF1VVJ9WxmqZRKpXY2tpCUZSpf+uHxFQkp0yZ8rHjvQQgiiJ2dnbwPI+KAOVWg+YTj7O9vc3u7i5xHFMsFpFlmTw/T6mkMhgMiOOYbtPHbrbo7R6hq/pEmJrNJjMzM8iyTKFQwPM8XnvtNcIwJM/z8VqmrmPbNsPRkCiK0HUdXdfJ83wSDYqKQhbH+HbC1oPb6FIZWZYZDocTAXyzPWV9fZ04jjk8PMT3fRRFwbIs8jwfv9Zul+XlZdrtNnEcTyacSJLEuXPnODw8ZH9/nwsXLnwEV+aTx1Qkp0yZ8rEjTcclDtLbin/Ch9Geqqrcu3cPz/Podrusrq6S5znb29u0Wi183x+3YUgSSZKg6zqFToHBaMBKeYUkSYiISEiwLIuNjQ0sy2J7extVVREemgSYponrunieh67rpKWUvc4e0TBCkiQURUEQBIaqjlGqILUPqGYWmVXCcVyq1SqyLE/WHa2HZu0wFnzXdTEMg1KpRJ7nLC4usrOzQ5ZleJ43mSWpqiqapgFw48YNnnrqqR9rOD/lp2MqklOmTHnkcRyH/f196iurXBn5zJ4cELguCwsLiKJIGIb4rsvtu3cxDGMiJJ1OhzhNWV5c5HB/H+/gALFanfQiFgoFVlZWkGWZg9MD2qM2tmKjqRqF+QJbW1vkeY7jOIiiSJ7n7MUptYJFkowrYaMootvtYpUtfHdscCAIAmtra+zv7xOGEXLvBN+yyFSNxB8brMuyjKZpJEnCYDCgUqmQpil37txBEIRJFWu73SbPc5Ik4ZlnnsG2bb718iuMwhBNEJBlmSAIME2TKIqo1+skSUIYhj/SmGHK35ypSE6ZMuWRxzAMGo0GJVVhzcypz8/T6/VwXZdms4kcBBTub7JjmkjlKlUnIBrIlCsig0TkoNNBjGOOTk8J+n2GwyFPPPEEDx484PDwEBjPmhwOh7gn40IYLdA5c+bMJFrM85xOt4OoSmSDmF6SIEkShmHgeR5+pqBqOeWHBuoHBweoqoqdJCBpVKtVut0uiqKQ5zn1ep1CocD169eJoohischgMKDZbNLtdknTlFwUKZYrVAsWnU6He/fuIQgCR7kIpSqWN0KWZSqVCuVymfn5eSRJYjQa0e12GQwGLC4u/oSzO+XHMRXJKVOmPHKcnJwgiiK1Wg0Yp1XfbI4/Y2qANomShsMhsaahP/kEK5LMd3cOGB3sMjszz1e//lVe+v4rnLT3MWs1Uk3FUlRqtRobGxsTW7dyuUyr3CJxE/x8QC7GDP0hgiBwenqKJEkUi0UkUaKq6giZgKhp5ElMUfDJNI04HkeIkihSKpWQJImZmRnu3r1Lo9FAVcftKHmekOcZpmliWRb9fp80TdnY2Bhb0mkauq4jCAKZqtENQ/IwmKw/9no9bM9DkiTq9Tqj0Yj9/X16vR6rq6v0+2OvWsuyJtZ9U94/U5GcMmXKI4ckSZM1t/fi+PgYWZYnIuo4DkeOw8nJCb/5/PP86eYdjnsHvPzqd6mUS7x+7YBIinAFl1pcG4+UGg1wQhcpV4gGA9rtNpqmcfGppzjKjtDDFCMw8OKYulUg8H2KhSJ5nmMUjPEaIgknBzsE4XjMl/swqpuZmeHg4IDhcMjy8jKj0YjNzc2HBuwJggCvvPIKrVaLYrE4qZI1DANVVUmSBMMwEAQBOYkZDofIsozv+0RRxPLS0kQEB4MBMzMzLC0t0e/3J/9MmKY56auc8v6ZiuSUKVM+crb9kJaqoD+0mHsvS7Uoiuh0Og+dZ5RJ64dt29y/f58HDx6wsrJCv9/HS1PyPMd13UnFabfv0ZlZYFlQmWu1uHf9VSTGDkC5LCOJIr1ej2vXrqHrOmtra/STPqZpUc0zxEKBtuAyPBwSxzFBECAIAm6qABGmaaIoCkdHR+zv70+KadSHFnKtVotut0uhUGdhYYHr169zeHiIoijYtj15ncA7qmuzLEOSJBYWFibuQKPRiGKxyOHhIcVikbm5uYnBueM4KIoyHb78ITEVySlTpnzk7PgRoiCwKL3lyZrnOe1RSLOojatEh0N6vR5Zlk1Srbo+btcYDAZ0Oh183+f+/iH+zCJWvsOtW7cQBIG5uTn6jkNpGNHx+qRRRK4WUbOASvUtd5rFxUWOj4+J45iDgwNM06RVrXAUnaLOwsbWBs+fe55RdwSMC3Te7HeEsYvOm96zb1advrnmqWkacRyT5zmKovDkk09y9+5dsiwjiqLxXM1KBVmW2djYAGBjY2PSH9npdNB1nWKxiOM4vPrqqyRJwvz8PJ1Oh9dee41nn312sn465cNhKpJTpkz5SHEchyfElLKuvvP+MOHVBz2+fKGBKgnU63VKpRL7+/uT3sXNzU02NzfHKcjlZTqdDoaQ86mCxqYkYds2MTH9QZ9yoUAhThklGTvdHayKhcE4pRnHMXEcY2gaiqJMWjPeXNNzug5u26WSVAjNcWo1yzIyO0OOZMzEnAxyhnEkOPI8AkGcpI2bzebEOefevXvkeU4URZNq3DiOJ+uJpmlSqVQoFAr0+30uXrzIrVu3JiL6ZoFPsVikXC4zGAwmt2KxSKfTmY4e/JCYiuSUKVM+UsIwJIoiyuV3WqkVdYWvXKhz796dSfpTURRWVlYm26yurtJutzk+PqYbRHgjl0KhwM2bN9kbOGSigBj2UBSFglxA01XSVMcuFPC98YBnRVEYjUbYto2+u4vRbOKoKq7rvtVeIZloqkaYhhOHHU3TCEYBlVKFTtCZCF2tVhubCIgiMmMxrVark5FgsixPhkebpjkxNAjDkGq1ytbW1tghKE1ZWlriy1/+MuVymSRJ2NzcxDRN1tfXyfOcIAjQNI0vfvGLk/RuEARYlvVzvIKfbKYiOWXKlI+UN4tvDg8PsSzrHb19aZqgqiqzs7Pv2i+KIhRFoWlZWM0mr3QHjNIUz3WpVCro80tEgU98onPaOaXnDDjR2lTlCnP5eLSVVbAQFXESsYWFAkKSMNdqUa+PnWNv3ryJqqo4skOkRoin4sQcvWgUGfaGk/aNYrGIZhiYpkmhUEBRFFJdw3EcfN8nTmJiJcZSLF588UWuXLmC4zhE0XiGZaPRmMzYTNMULQi4e/06dqPBE088QbvdptfrTSz4isUiV69exTRNzj+cr/mm48+UD4epSE6ZMuWR4M0U59sxDIP19fV3bZskCbdv3+bMmTNsXbtGo1TiXKXCnl1g7uJjvPraFVpBQB4ElOwScRTzwA8ppQ3mSk0szSJXc6Igwu12WV+5xNraGq+//jper0eUxlSrVUJ/PDfSMAwKUoHhcIhmarz44ou02+2xO08UcXJyguM4uFGEXyjiui6u6078XsMwJE1TVFPFERz6gz5Xrlyh3W5jGONK2SRJ2N3dRZIkLMvi6aefRut0kEolOnHM/v4+pmly7tw5oiiiVCpx9uxZKpXKO6LrKR8uU5GcMmXKI8HbrdTyPKfb7U6GCv8wSZIgyzKqqlI8f57r166hBgG9Xo/g3l3cOOGo30UUx2uCsiyzVFJpVZbJ0wQ/8BnkA2zBYkmr0j46Ioxj4iSeeLKmYoou6ARaQEErEHkRpqJQKJXYur9NFgmoBQHbtkmSBM/zCMIQadCnUCjgOA5ZljEYDFBVdVJMc6F5ge3tbUajEfV6nXa7jSRJ+L4/iQAdx8FxHHY8D/PhuXEcB8Mw0DQNURTZ3t7m8uXLlEol4jj+uVyjX0SmIjllypRHjjiO6XQ6FAqFyVrbm7w5VaNarbK/v0+jVsNpn+DaNvV6ncXZWcw45sAZTMZLZdm4ef/B5n1M08RxnLHxeE3hRBTRdZ2DTgeSBAC7aLMwu4Dv+3hDD0u3iLwBynCIWKthWgY7nR3i3rj1Q9f1cQ+kphP6PiXbHovmw97HN6d5+L5Pq9VC0zROTk7Y2tqi1WoRRRGqqqLr+sTM3HVdHGc84LlWqzEajeh0Oti2jW3bWJZF/DDCvHv3Ll/60pfe4WU75cPhAw1dnjJlypSfBaqqcv78+XcJJDARH8dxOD4+JgOsL36B1UuXMAyD7e1tjo6O0DQNy7LQdZ3o4UBlURTpdrsIwngIsuu69Ho9VlZWEEplEMdxg6aOn3d1dZWSXELIBHwZjgrjhv6Z2RZDZzDuyfQ8Tk9PWV1dJcky5IetHmtraxSLxcmsSIBCocDy8jJLS0uTApv19XUqlQrz8/OUSiVKpRKWZSEIAqZp8rnPfY65uTnW19cpl8vs7e1xdHSEIAj4vk+e55TL5Unf6JQPl2kkOWXKlEeWjY0NRqMRTz311MTw+8qVK/z/7d1dbJvXfcfx7+H7iyhSJEVbEiXZlJX5Za5dz3C6BMsabGu9YUDXAQXa3RTYgG7AerWrDrvosN4MA4pdDHvLsKK9WYsCRbZiK9ZlvVhQIG4SI0kTO7FlK7ZMWpJFUTQpUnw/u6D4VE5Mv8iRRTm/DyBIpJ7n4dERrb/Pec75/0OhEKOjo8TjcTqdDpF4nJs3bxIOh1leXsZaS71e766cbTapev1MZQ5Ryt0gGo1SLpdpNpu43W6MMeTzeRIGyl439Xp3FWqxWOTmzZv4/X4ikQi3bt2CVjc93VtvvYXX6yUej+NyuVhdXeX6aoF2YpTQxnq3nuRmIG42mxw8eJC5uTnGxsZ44403MMYwOTnJ4cOHCQaDlMvdfZeJRIKFhQV8Ph8+n494PI7b7SaXy1Gr1Thz5gzWWlwulzNqHBkZYXp6ejd/TU80BUkRGQj1ep3FxUUmJydZWVnprgxtt6lUKk7NRbfbzczMDMFgkGQySaFQoFar0el0qNVqFItFXC6XkzC82WxigHQiTqtYoFwu02g0sNYyNDREItFNUbexscH6+jrHjh1jfn6eTqfD0NAQlUqFcDjMtWvXGB4edmpE9spsFYtFpz3zK3k6rQ4HDs9SKBRYWVlx7p32ymmFQiH2799PNpslHo9TLpfxer1OkoBkMsnY2JizkGd0dJR3330Xl8uF3+/H4+n+ya7VauRyOSYnJzWC3GEKkiIyELbWYHS73c6Ua29rQ8/6+jqxWIy5uTl++tOfcvzESUYTcYwxTmL0XnmrXvHjcn6FqttNs9Gk0Wji8bip1+vMzMyQz+e5ceMG9XqdXC5Hp9Nx6kzu37/f2drh8XjuWKnamzKtVqvU63WmRmLk83m8Xi/T09OkUineffddUqmUs7m/Wq0SjUa5dOkSq6urTjHnUCjE3Nwcly9f5vDhw06prCtXrjA6OnrHlC3gfF92nnpZRAaCx+NhYmKCarXK0tLSHRvie0HPWusEqlKpxNDQEFeGE+SabTweD8ePH2dqagq3202tVqNWqzkJwz0eDxaLMca511kul3G5XE5FjkqlQiAQoAUs5vPOKtTeAhroBvNencdCPs/S0hLxeJxMJkMsFuOdd97h/Pnz3S0fPh+hUIhms8ni4iJra2ssLy9z9OhR1tfXKZVKzkrWaDSKz+fj/Pnz5HI5NjY2nP2YH+T3+0mn0xpFPgYaSYrIQCkWu1soto6UlpaWuHjxIp/4xCeYnp7m6tWrFNxepg4dYshjcdUarGwWYc7n83i8PqrhKI3bBQLVKvCLclutVotoNOoEOmMMwWDQWXnaarUIhcPUNzao1WrY8TRruSydbJZUKsWRI0d45ZVXuqtZXdA23XuGvdWnvSw+Fy5ccBYKffKTnySXy+F2u8nn8ywvLxOPxwmHwzzzzDNcvnyZ5557jlwuRzab5fTp00oIMCAUJEVkYLTbbeLx+IdGSBsbG06S75WVFer1OteGE8y0akx43IwePMhbb77Ja6+9htvt5kY0zkq9yWS7TTKZdEpMhUIhrLXkcjmgO20ZjUY5cOgQ4VSKTrncLU9VrxMaHqZcLhOKRNg/OwvlEu1228nA02q1CMWTWGvx+XzUajW8Xi+1Wo2RkRFKpZKT/3V5eRm/38/4+Djvvfce9XqdTqdDIpEgnU6TTqfpdDqUy2VmZ2fvuqpXdoeCpIgMhFKpxI0bNzhy5MiH7reNjY05ZaE2NjaIx+P8ltuLq+pmMXuDc+fO4fV6aTQaDA0NcWokyvtX5ggG/Ozfv5+NzVFhoVCgUqkQDAY5cOAA8/PzlEolVgoFJn/9Nxi68f4dSQBSqRSpZByPx8Ply917oY1Gw9n6EY1G8Xg8XLp5iaA7iG3YblL1zc39w8PDPP3009TrdfL5vHMftN1us7GxQSaTcf5D4Ha7mZ2dZW5ujlgspkC5Q4wx3wJ+F7hlrf3l+x2ve5IiMhAikQgHDhy464KUYDDIwYMHGR0dZWNjg7fffpvC1TnKhVUCgQDFYtFZkVooFEiFAoyPdAPN/Pw8lUrFuaeYyWSc1bEnT57sBt5ikYO1btHmK1eusL6+zsjICKOjo4yPjzMzM0N8bBxPNEY0GqXT6Tib/0ulEq6OC+Pq7lv0+XzOoqFwOMwr17Nky+uEw2FSqRSnTp1yUs91Op07fs5AIMCxY8cUIHfWt4GzD3qwgqSIDARjTN/qFWtra1y+fBmA1dVVXC4XIyMjdDodXn/9dadEVblcptPpsL6+7mTage6K2Fwu5wTger3OxYsXGRsbIxQKUa1WOXfuHNVqlY2NDRKJBCdPniQej3PhwgVeeuklrhVvs+zp1ocMBAIMDQ2xurpKMpkkM5bB1eimv+slKk8kEsTjcTzrZWqFVTqdDiMjI9TrdSYmJpyA/kFatbqzrLUvAx/u+D403SoiA2t5eRmPx0MsFnP2CLpcLvL5PFevXsUY49SD7JWgstayuLiIy+Vi3759ZLNZjDEkEglarRaRSIRsNovL5eomLPf7OXbsGMVikUajwVNPPUU6nSabzdJqtVhdXcXn8/HsoUx39e3kGHNzcxQKBay1TE1Ncf36dcbHx3G7u1tLIpEIwWCQZ599lp/97GcsLCwwdOCAc68yHA4zOTm5y737xEoaY17f8vgFa+0L272YgqSIDCyfz0exWOT69eucOnXKmcas1WrOgplgMOikmDtx4gRXr16l2exW8UgkEiwuLhKLxUin05RKJTKZDMPDw1y+fJm33noLj8dDIBAgHo9Tq9W4fv06nU6HTCaDx+MhFAo59R1v377N+Pg4CwsLhEIhvF4vfr8fa61Touqpp56iUqkwNzeHy+XiyJEjuN1uZ+tIrzSY7Ji8tfb0R3UxBUkRGTjNZtO5L9hL9n3r+vu0MKRSKWZnZ0mn07z66qvUetU/ajXa7TYnT54klUoRCASoVCo0Gg3W1tYwxpBMJolGo06x42q1irWWtbU1/H4/mUyGcrnMmTNnSCaTtFotJ7MPQDQaJRgM8qlPfYpyuUw4HGZ9fZ3333+fqakpotEo165dY2ZmxkkbV9ncmjI9Pa19jXuQgqSIDJxKpUKxWGRkZIR4PE48Hmfu52+yuFrgzK8+w8rKCuFwGI/Hw8LCAu12m06nQ71e5+bNm1SrVaanp1laWiKTyTA3N0cul+Ps2bMkk0nm5+fJZDIEAgHW1taIxWI0m03Gx8ex1pLNZqnVakSjUYzplsMaHx93gtzq6iqlUgljjLOvMZFI4Pf7SaVSBINBpqamAJzUcQqQe5OCpIgMnFgsRiwWo91uOyOyQ8dPYK5epVQq4Xa7icVijI+Ps7i4iNfrdSpiFItFxsbGuHHjhjPKbDabHDt2jHg8Tjabxe/3U61WabfbpNPpbr7W0f0sVmqcSaex1tJoNMjlciQSCSYmJu5oX7VaJRwOE4vFnCng3orUDy4+6t1LlcFgjPku8Gm69y6zwNettf/a73j99kRkILXbbS5evEgmk3FKRx06dMgJnG63m0Ag4Kxg7Y36jh8/zvr6On6/n5mZGc6dO+fcD6xUKs6IcHh4mGKxiLWWEydOsLC0xOLCdUKnTzkBLxaL4fV6P9S2iYkJJ3in0+nH1ylPkEq9xSvzq4/9da21X3qY4xUkRWQgba34sVWvfuPIyAitVotMJsPS0hKRSIQjR44wPj5OqVRyKnY8//zzXLx4kUgkgt/vd4JeLBZjeHjYWR3rbrc5c/Qwfr+f27dvc/v2bWfK9IN8Pt+O//xPunVfknNTX9mBK3/zI72agqSIDKxeAoCtIpEIkUiETqdDIBBgdnaWyclJbt++jd/vp1Qq0Wg0mJ6eZmNjg/n5eUZGRjh69OiHrtXbk+j1ekmn0wwPDwPdINhvz6Z8vGjXqojsSS6Xi6NHj9LpdDDGMDQ0hN/vp16vO3smw+EwY2NjHD58+J7X6uVwNcZQKpW4du0a8Xj8Mf0kMsgUJEVkT7HW3vH4wIED7Nu3z0kTF4vFnAw8xhhGR0ed+4cPYmhoSGWoxKHpVhHZM1ZXVykUCszOzjrP1et1isUimUwGn8+HMeaRRoEul4tIJPJRNFeeAAqSIrJn9AoTb9W7R7mVRoHyUdF0q4jsGR6P556jvE6nw9zcHJVK5TG2Sp5kCpIi8sToVQcJBAK73RR5Qmi6VUSeKMlkcrebIE8QjSRFRET6UJAUERHpQ0FSRESkDwVJERGRPhQkRURE+lCQFBER6UNBUkREpA8FSRERkT4UJEVERPpQkBQREelDQVJERKQPBUkREZE+FCRFRET6UJAUERHpQ0FSRESkDwVJERGRPhQkRURE+lCQFBER6UNBUkREpA8FSRERkT4UJEVERPpQkBQREelDQVJERKQPBUkREZE+FCRFRET6UJAUERHpQ0FSRESkDwVJERGRPh4pSBpjvmCMuWCM6RhjTm95/oAxZsMY8+bmxz89elNFREQeL88jnv8O8PvAP9/le1ettScf8foiIiK75pFGktbad621lz6qxoiIiOwkY8xZY8wlY8wVY8zX7nf8Tt6TPGiMecMY83/GmF/bwdcRERG5L2OMG/h74LeBo8CXjDFH73XOfadbjTH/C+y/y7f+wlr7H31OWwSmrLWrxphfAf7dGHPMWlu6y/W/AnwFYGpq6n7NERER2a4zwBVr7TyAMeZ7wOeAi/1OuG+QtNb+5sO2wlpbB+qbX583xlwFngJev8uxLwAvAJw+fdo+7GuJiIg8oAngxpbHWeDpe53wqAt37soYMwoUrLVtY0wGmAXm73fe+fPn88aY6w/wEkkg/4jN/LhRn22P+u3hqc+2ZxD7bXqnLpydu/DjP/vMLyV34NIBY8zWAdkLm4MxAHOX4+85OHukIGmM+Tzwd8Ao8F/GmDettZ8FngP+yhjTAtrAn1hrC/e7nrV29AFf93Vr7en7Hyk96rPtUb89PPXZ9nzc+s1ae3YXXjYLTG55nAZu3uuERwqS1toXgRfv8vwPgB88yrVFREQ+Yq8Bs8aYg0AO+CLwB/c6YUemW0VERAaNtbZljPkq8GPADXzLWnvhXufs1SD5wv0PkQ9Qn22P+u3hqc+2R/32GFhrfwT86EGPN9ZqQamIiMjdKMG5iIhIH3smSPZLpr75vT/fTDF0yRjz2d1q46AzxvylMSa3JfH87+x2mwbVw6auki5jzDVjzNub768P7YsWMMZ8yxhzyxjzzpbn4saYl4wxc5ufR3azjfILeyZI8otk6i9vfXIzpdAXgWPAWeAfNlMPyd39rbX25ObHA8/Lf5xsJ3WV3OH5zffXx2Y7w0P6Nt2/VVt9DfiJtXYW+MnmYxkAeyZI3iOZ+ueA71lr69ba94ErdFMPiWyXk7rKWtsAeqmrRB6ZtfZl4IP7xj8HfGfz6+8Av/dYGyV97ZkgeQ93SzM0sUtt2Qu+aoz5+eaUj6Z07k7vqe2zwP8YY85v5mWWB7PPWrsIsPk5tcvtkU0DtQVkm8nUHzrN0JPsXn0I/CPwDbr98w3gm8AfPr7W7Rl6T23fs9bam8aYFPCSMea9zZGTyJ40UEFyO8nU2UaaoSfZg/ahMeZfgP/c4ebsVXpPbZO19ubm51vGmBfpTl0rSN7fsjFmzFq7aIwZA27tdoOk60mYbv0h8EVjjH8z1dAs8Oout2kgbf7j6/k83cVQ8mFO6ipjjI/uwrAf7nKbBp4xJmyMifS+Bj6D3mMP6ofAlze//jLQb+ZMHrOBGkneS79k6tbaC8aY79OtB9YC/tRa297Ntg6wvzHGnKQ7dXgN+OPdbc5g2k7qKgFgH/CiMQa6f1v+zVr737vbpMFjjPku8GkgaYzJAl8H/hr4vjHmj4AF4Au710LZShl3RERE+ngSpltFRER2hIKkiIhIHwqSIiIifShIioiI9KEgKSIi0oeCpIiISB8KkiIiIn0oSIqIiPTx/xb933FDNEOPAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 576x576 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots( figsize=(8, 8))\n", "sc = ax.scatter(\n", "    embedding[:, 0],\n", "    embedding[:, 1],\n", "    c=Y_train.astype(int),\n", "    cmap=\"tab10\",\n", "    s=0.1,\n", "    alpha=0.5,\n", "    rasterized=True,\n", ")\n", "ax.axis('equal')\n", "ax.set_title(\"UMAP in Tensorflow embedding\", fontsize=20)\n", "plt.colorbar(sc, ax=ax);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### plotting loss"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T06:08:45.713308Z", "start_time": "2020-08-17T06:08:45.671857Z"}}, "outputs": [{"data": {"text/plain": ["dict_keys(['loss'])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["embedder._history.keys()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2020-08-17T06:08:46.317803Z", "start_time": "2020-08-17T06:08:45.714605Z"}}, "outputs": [{"ename": "KeyError", "evalue": "'reconstruction_loss'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-13-3fd0bc7df218>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      6\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      7\u001b[0m \u001b[0max\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0maxs\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 8\u001b[0;31m \u001b[0max\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mplot\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0membedder\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_history\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'reconstruction_loss'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mlabel\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'train'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      9\u001b[0m \u001b[0max\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mplot\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0membedder\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_history\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'val_reconstruction_loss'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mlabel\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'valid'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     10\u001b[0m \u001b[0max\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mlegend\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON>r\u001b[0m: 'reconstruction_loss'"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x360 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(figsize=(10,5))\n", "ax.plot(embedder._history['loss'])\n", "ax.set_ylabel('Cross Entropy')\n", "ax.set_xlabel('Epoch')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.1"}}, "nbformat": 4, "nbformat_minor": 2}