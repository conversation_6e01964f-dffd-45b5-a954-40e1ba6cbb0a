# Trigger a build when there is a push to the main branch or a tag starts with release-
trigger:
  branches:
    include:
    - master
  tags:
    include:
    - release-*

# Trigger a build when there is a pull request to the main branch
# Ignore PRs that are just updating the docs
pr:
  branches:
    include:
    - master
    exclude:
    - doc/*
    - README.rst

parameters:
  - name: includeReleaseCandidates
    displayName: "Allow pre-release dependencies"
    type: boolean
    default: false


variables:
  triggeredByPullRequest: $[eq(variables['Build.Reason'], 'PullRequest')]

stages:
  - stage: RunAllTests
    displayName: Run test suite
    jobs:
      - job: run_platform_tests
        strategy:
          matrix:
            mac_py39:
              imageName: 'macOS-latest'
              python.version: '3.9'
            linux_py39:
              imageName: 'ubuntu-latest'
              python.version: '3.9'
            windows_py39:
              imageName: 'windows-latest'
              python.version: '3.9'
            mac_py310:
              imageName: 'macOS-latest'
              python.version: '3.10'
            linux_py310:
              imageName: 'ubuntu-latest'
              python.version: '3.10'
            windows_py310:
              imageName: 'windows-latest'
              python.version: '3.10'
            mac_py311:
              imageName: 'macOS-latest'
              python.version: '3.11'
            linux_py311:
              imageName: 'ubuntu-latest'
              python.version: '3.11'
            windows_py311:
              imageName: 'windows-latest'
              python.version: '3.11'
            mac_py312:
              imageName: 'macOS-latest'
              python.version: '3.12'
            linux_py312:
              imageName: 'ubuntu-latest'
              python.version: '3.12'
            windows_py312:
              imageName: 'windows-latest'
              python.version: '3.12'

        pool:
          vmImage: $(imageName)

        steps:
        - task: UsePythonVersion@0
          inputs:
            versionSpec: '$(python.version)'
          displayName: 'Use Python $(python.version)'

        - script: |
            python -m pip install --upgrade pip
          displayName: 'Upgrade pip'

        - script: |
            pip install -e .
            pip install .[plot]
            pip install .[parametric_umap]
          displayName: 'Install dependencies'
          condition: ${{ eq(parameters.includeReleaseCandidates, false) }}

        - script: |
            pip install --pre -e .
            pip install --pre .[plot]
            pip install --pre .[parametric_umap]
          displayName: 'Install dependencies (allow pre-releases)'
          condition: ${{ eq(parameters.includeReleaseCandidates, true) }}

        - script: |
            pip install pytest  pytest-azurepipelines pytest-cov pytest-benchmark coveralls
          displayName: 'Install pytest'

        - script: |
            # export NUMBA_DISABLE_JIT=1 # Disable numba coverage so tests run on time for now.
            pytest umap/tests --show-capture=no -v --disable-warnings --junitxml=junit/test-results.xml --cov=umap/ --cov-report=xml --cov-report=html
          displayName: 'Run tests'

        - bash: |
            coveralls
          displayName: 'Publish to coveralls'
          condition: and(succeeded(), eq(variables.triggeredByPullRequest, false)) # Don't run this for PRs because they can't access pipeline secrets
          env:
            COVERALLS_REPO_TOKEN: $(COVERALLS_TOKEN)

        - task: PublishTestResults@2
          inputs:
            testResultsFiles: '$(System.DefaultWorkingDirectory)/**/coverage.xml'
            testRunTitle: '$(Agent.OS) - $(Build.BuildNumber)[$(Agent.JobName)] - Python $(python.version)'
          condition: succeededOrFailed()

  - stage: BuildPublishArtifact
    dependsOn: RunAllTests
    condition: and(succeeded(), startsWith(variables['Build.SourceBranch'], 'refs/tags/'), eq(variables.triggeredByPullRequest, false))
    jobs:
      - job: BuildArtifacts
        displayName: Build source dists and wheels    
        pool:
          vmImage: 'ubuntu-latest'
        steps:
        - task: UsePythonVersion@0
          inputs:
            versionSpec: '3.10'
          displayName: 'Use Python 3.10'

        - script: |
            python -m pip install --upgrade pip
            pip install wheel
            pip install -e .
          displayName: 'Install package locally'
        
        - bash: |
            python setup.py sdist bdist_wheel
            ls -l dist/
          displayName: 'Build package'

        - bash: |
            export PACKAGE_VERSION="$(python setup.py --version)"
            echo "Package Version: ${PACKAGE_VERSION}"
            echo "##vso[task.setvariable variable=packageVersionFormatted;]release-${PACKAGE_VERSION}"
          displayName: 'Get package version'

        - script: |
            echo "Version in git tag $(Build.SourceBranchName) does not match version derived from setup.py $(packageVersionFormatted)"
            exit 1
          displayName: Raise error if version doesnt match tag
          condition: and(succeeded(), ne(variables['Build.SourceBranchName'], variables['packageVersionFormatted']))

        - task: DownloadSecureFile@1
          name: PYPIRC_CONFIG
          displayName: 'Download pypirc'
          inputs:
            secureFile: 'pypirc'  

        - script: |
            pip install twine
            twine upload --repository pypi --config-file $(PYPIRC_CONFIG.secureFilePath) dist/*
          displayName: 'Upload to PyPI'
          condition: and(succeeded(), eq(variables['Build.SourceBranchName'], variables['packageVersionFormatted']))

