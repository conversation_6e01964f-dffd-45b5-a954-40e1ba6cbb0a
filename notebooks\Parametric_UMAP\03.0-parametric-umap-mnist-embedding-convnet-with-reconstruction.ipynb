{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Reconstruction with a custom network. \n", "This notebook extends the last notebook to simultaneously train a decoder network, which translates from embedding back into dataspace. It also shows you how to use validation data for the reconstruction network during training."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### load data"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T20:48:49.032658Z", "start_time": "2021-04-20T20:48:47.085191Z"}}, "outputs": [], "source": ["import tensorflow as tf"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T20:48:49.044974Z", "start_time": "2021-04-20T20:48:49.034381Z"}}, "outputs": [{"data": {"text/plain": ["'2.4.1'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["tf.__version__"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T20:48:49.457948Z", "start_time": "2021-04-20T20:48:49.046475Z"}}, "outputs": [], "source": ["from tensorflow.keras.datasets import mnist\n", "(train_images, Y_train), (test_images, Y_test) = mnist.load_data()\n", "train_images = train_images.reshape((train_images.shape[0], -1))/255.\n", "test_images = test_images.reshape((test_images.shape[0], -1))/255."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### define the encoder network"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T20:48:49.463905Z", "start_time": "2021-04-20T20:48:49.460001Z"}}, "outputs": [], "source": ["import tensorflow as tf"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T20:48:49.476054Z", "start_time": "2021-04-20T20:48:49.469799Z"}}, "outputs": [{"data": {"text/plain": ["'2.4.1'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["tf.__version__"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T20:48:49.538037Z", "start_time": "2021-04-20T20:48:49.480424Z"}}, "outputs": [{"data": {"text/plain": ["[PhysicalDevice(name='/physical_device:GPU:0', device_type='GPU'),\n", " PhysicalDevice(name='/physical_device:GPU:1', device_type='GPU')]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["tf.config.list_physical_devices('GPU')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T20:48:51.341141Z", "start_time": "2021-04-20T20:48:49.542747Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model: \"sequential\"\n", "_________________________________________________________________\n", "Layer (type)                 Output Shape              Param #   \n", "=================================================================\n", "conv2d (Conv2D)              (None, 14, 14, 32)        320       \n", "_________________________________________________________________\n", "conv2d_1 (Conv2D)            (None, 7, 7, 64)          18496     \n", "_________________________________________________________________\n", "flatten (Flatten)            (None, 3136)              0         \n", "_________________________________________________________________\n", "dense (Dense)                (None, 128)               401536    \n", "_________________________________________________________________\n", "dense_1 (<PERSON><PERSON>)              (None, 128)               16512     \n", "_________________________________________________________________\n", "dense_2 (<PERSON><PERSON>)              (None, 2)                 258       \n", "=================================================================\n", "Total params: 437,122\n", "Trainable params: 437,122\n", "Non-trainable params: 0\n", "_________________________________________________________________\n"]}], "source": ["dims = (28,28, 1)\n", "n_components = 2\n", "encoder = tf.keras.Sequential([\n", "    tf.keras.layers.InputLayer(input_shape=dims),\n", "    tf.keras.layers.Conv2D(\n", "        filters=32, kernel_size=3, strides=(2, 2), activation=\"relu\", padding=\"same\"\n", "    ),\n", "    tf.keras.layers.Conv2D(\n", "        filters=64, kernel_size=3, strides=(2, 2), activation=\"relu\", padding=\"same\"\n", "    ),\n", "    tf.keras.layers.<PERSON><PERSON>(),\n", "    tf.keras.layers.Dense(units=128, activation=\"relu\"),\n", "    tf.keras.layers.Dense(units=128, activation=\"relu\"),\n", "    tf.keras.layers.Dense(units=n_components),\n", "])\n", "encoder.summary()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T20:48:51.421311Z", "start_time": "2021-04-20T20:48:51.342900Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model: \"sequential_1\"\n", "_________________________________________________________________\n", "Layer (type)                 Output Shape              Param #   \n", "=================================================================\n", "dense_3 (<PERSON><PERSON>)              (None, 128)               384       \n", "_________________________________________________________________\n", "dense_4 (<PERSON><PERSON>)              (None, 6272)              809088    \n", "_________________________________________________________________\n", "reshape (Reshape)            (None, 7, 7, 128)         0         \n", "_________________________________________________________________\n", "conv2d_transpose (Conv2DTran (None, 14, 14, 64)        73792     \n", "_________________________________________________________________\n", "conv2d_transpose_1 (Conv2DTr (None, 28, 28, 32)        18464     \n", "_________________________________________________________________\n", "conv2d_transpose_2 (Conv2DTr (None, 28, 28, 1)         289       \n", "=================================================================\n", "Total params: 902,017\n", "Trainable params: 902,017\n", "Non-trainable params: 0\n", "_________________________________________________________________\n"]}], "source": ["decoder = tf.keras.Sequential([\n", "    tf.keras.layers.InputLayer(input_shape=(n_components)),\n", "    tf.keras.layers.Dense(units=128, activation=\"relu\"),\n", "    tf.keras.layers.Dense(units=7 * 7 * 128, activation=\"relu\"),\n", "    tf.keras.layers.Reshape(target_shape=(7, 7, 128)),\n", "    tf.keras.layers.Conv2DTranspose(\n", "        filters=64, kernel_size=3, strides=(2, 2), padding=\"SAME\", activation=\"relu\"\n", "    ),\n", "    tf.keras.layers.Conv2DTranspose(\n", "        filters=32, kernel_size=3, strides=(2, 2), padding=\"SAME\", activation=\"relu\"\n", "    ),\n", "    tf.keras.layers.Conv2DTranspose(\n", "        filters=1, kernel_size=3, strides=(1, 1), padding=\"SAME\", activation=\"sigmoid\"\n", "    )\n", "])\n", "decoder.summary()"]}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2020-08-16T22:12:46.790121Z", "start_time": "2020-08-16T22:12:46.759185Z"}}, "source": ["### create parametric umap model"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T20:48:56.364427Z", "start_time": "2021-04-20T20:48:51.422923Z"}}, "outputs": [], "source": ["from umap.parametric_umap import ParametricUMAP"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T20:48:56.369810Z", "start_time": "2021-04-20T20:48:56.366115Z"}}, "outputs": [], "source": ["embedder = ParametricUMAP(\n", "    encoder=encoder,\n", "    decoder=decoder,\n", "    dims=dims,\n", "    n_components=n_components,\n", "    n_training_epochs=1, # dicates how many total training epochs to run\n", "    n_epochs = 50, # dicates how many times edges are trained per 'epoch' to keep consistent with non-parametric UMAP\n", "    parametric_reconstruction= True,\n", "    reconstruction_validation=test_images,\n", "    parametric_reconstruction_loss_fcn = tf.keras.losses.MSE,\n", "    verbose=True,\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T20:48:56.394144Z", "start_time": "2021-04-20T20:48:56.371052Z"}}, "outputs": [{"data": {"text/plain": ["(60000, 784)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["train_images.shape"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T21:00:30.171817Z", "start_time": "2021-04-20T20:48:56.395427Z"}, "scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParametricUMAP(decoder=<tensorflow.python.keras.engine.sequential.Sequential object at 0x7f99f6495ca0>,\n", "               dims=(28, 28, 1),\n", "               encoder=<tensorflow.python.keras.engine.sequential.Sequential object at 0x7f99f6f21a60>,\n", "               optimizer=<tensorflow.python.keras.optimizer_v2.adam.Adam object at 0x7f993407f400>,\n", "               parametric_reconstruction=True,\n", "               parametric_reconstruction_loss_fcn=<function mean_squared_error at 0x7f9a043c0ca0>,\n", "               reconstruction_validation=array([[0., 0., 0., ..., 0., 0., 0.],\n", "       [0., 0., 0., ..., 0., 0., 0.],\n", "       [0., 0., 0., ..., 0., 0., 0.],\n", "       ...,\n", "       [0., 0., 0., ..., 0., 0., 0.],\n", "       [0., 0., 0., ..., 0., 0., 0.],\n", "       [0., 0., 0., ..., 0., 0., 0.]]))\n", "Construct fuzzy simplicial set\n", "<PERSON><PERSON> Apr 20 13:48:56 2021 Finding Nearest Neighbors\n", "<PERSON><PERSON> Apr 20 13:48:56 2021 Building RP forest with 17 trees\n", "<PERSON><PERSON> Apr 20 13:49:03 2021 parallel NN descent for 16 iterations\n", "\t 0  /  16\n", "\t 1  /  16\n", "\t 2  /  16\n", "\t 3  /  16\n", "\t 4  /  16\n", "<PERSON><PERSON> Apr 20 13:49:19 2021 Finished Nearest Neighbor Search\n", "<PERSON><PERSON> Apr 20 13:49:23 2021 Construct embedding\n", "Epoch 1/10\n", "1905/1905 [==============================] - 167s 52ms/step - loss: 0.2818 - reconstruction_loss: 0.0570 - umap_loss: 0.2248 - val_loss: 0.0411 - val_reconstruction_loss: 0.0411 - val_umap_loss: 0.0000e+00\n", "Epoch 2/10\n", "1905/1905 [==============================] - 53s 28ms/step - loss: 0.1698 - reconstruction_loss: 0.0394 - umap_loss: 0.1304 - val_loss: 0.0381 - val_reconstruction_loss: 0.0381 - val_umap_loss: 0.0000e+00\n", "Epoch 3/10\n", "1905/1905 [==============================] - 54s 28ms/step - loss: 0.1550 - reconstruction_loss: 0.0373 - umap_loss: 0.1178 - val_loss: 0.0382 - val_reconstruction_loss: 0.0382 - val_umap_loss: 0.0000e+00\n", "Epoch 4/10\n", "1905/1905 [==============================] - 53s 28ms/step - loss: 0.1465 - reconstruction_loss: 0.0354 - umap_loss: 0.1112 - val_loss: 0.0369 - val_reconstruction_loss: 0.0369 - val_umap_loss: 0.0000e+00\n", "Epoch 5/10\n", "1905/1905 [==============================] - 52s 27ms/step - loss: 0.1417 - reconstruction_loss: 0.0345 - umap_loss: 0.1072 - val_loss: 0.0361 - val_reconstruction_loss: 0.0361 - val_umap_loss: 0.0000e+00\n", "Epoch 6/10\n", "1905/1905 [==============================] - 53s 28ms/step - loss: 0.1386 - reconstruction_loss: 0.0343 - umap_loss: 0.1043 - val_loss: 0.0383 - val_reconstruction_loss: 0.0383 - val_umap_loss: 0.0000e+00\n", "Epoch 7/10\n", "1905/1905 [==============================] - 52s 27ms/step - loss: 0.1361 - reconstruction_loss: 0.0339 - umap_loss: 0.1022 - val_loss: 0.0358 - val_reconstruction_loss: 0.0358 - val_umap_loss: 0.0000e+00\n", "Epoch 8/10\n", "1905/1905 [==============================] - 54s 28ms/step - loss: 0.1345 - reconstruction_loss: 0.0337 - umap_loss: 0.1008 - val_loss: 0.0371 - val_reconstruction_loss: 0.0371 - val_umap_loss: 0.0000e+00\n", "Epoch 9/10\n", "1905/1905 [==============================] - 55s 29ms/step - loss: 0.1332 - reconstruction_loss: 0.0336 - umap_loss: 0.0995 - val_loss: 0.0369 - val_reconstruction_loss: 0.0369 - val_umap_loss: 0.0000e+00\n", "Epoch 10/10\n", "1905/1905 [==============================] - 53s 28ms/step - loss: 0.1326 - reconstruction_loss: 0.0335 - umap_loss: 0.0991 - val_loss: 0.0354 - val_reconstruction_loss: 0.0354 - val_umap_loss: 0.0000e+00\n", "1875/1875 [==============================] - 2s 1ms/step\n", "<PERSON><PERSON> Apr 20 14:00:29 2021 Finished embedding\n"]}], "source": ["embedding = embedder.fit_transform(train_images)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### plot reconstructions"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T21:00:30.811242Z", "start_time": "2021-04-20T21:00:30.173336Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["10/10 [==============================] - 0s 6ms/step\n", "10/10 [==============================] - 0s 16ms/step\n"]}], "source": ["test_images_recon = embedder.inverse_transform(embedder.transform(test_images.reshape(len(test_images), 28,28,1)))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T21:00:31.109389Z", "start_time": "2021-04-20T21:00:30.812977Z"}}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T21:00:31.126659Z", "start_time": "2021-04-20T21:00:31.110829Z"}}, "outputs": [{"data": {"text/plain": ["(0.0, 1.0)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["np.min(test_images), np.max(test_images)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T21:00:33.312491Z", "start_time": "2021-04-20T21:00:31.138264Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x144 with 20 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["nex = 10\n", "fig, axs = plt.subplots(ncols=10, nrows=2, figsize=(nex, 2))\n", "for i in range(nex):\n", "    axs[0, i].matshow(np.squeeze(test_images[i].reshape(28, 28, 1)), cmap=plt.cm.Greys)\n", "    axs[1, i].matshow(\n", "        np.squeeze(test_images_recon[i].reshape(28, 28, 1)),\n", "        cmap=plt.cm.Greys, vmin = 0, vmax = 1\n", "    )\n", "for ax in axs.flatten():\n", "    ax.axis(\"off\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### plot results"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T21:00:33.316627Z", "start_time": "2021-04-20T21:00:33.314255Z"}}, "outputs": [], "source": ["embedding = embedder.embedding_"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T21:00:33.327645Z", "start_time": "2021-04-20T21:00:33.318156Z"}}, "outputs": [], "source": ["import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T21:00:34.504597Z", "start_time": "2021-04-20T21:00:33.330600Z"}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x576 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots( figsize=(8, 8))\n", "sc = ax.scatter(\n", "    embedding[:, 0],\n", "    embedding[:, 1],\n", "    c=Y_train.astype(int),\n", "    cmap=\"tab10\",\n", "    s=0.1,\n", "    alpha=0.5,\n", "    rasterized=True,\n", ")\n", "ax.axis('equal')\n", "ax.set_title(\"UMAP in Tensorflow embedding\", fontsize=20)\n", "plt.colorbar(sc, ax=ax);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### plotting loss"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T21:00:34.519374Z", "start_time": "2021-04-20T21:00:34.510578Z"}}, "outputs": [{"data": {"text/plain": ["dict_keys(['loss', 'reconstruction_loss', 'umap_loss', 'val_loss', 'val_reconstruction_loss', 'val_umap_loss'])"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["embedder._history.keys()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"ExecuteTime": {"end_time": "2021-04-20T21:00:34.850925Z", "start_time": "2021-04-20T21:00:34.522071Z"}}, "outputs": [{"data": {"text/plain": ["Text(0.5, 0, 'Epoch')"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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****************************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\n", "text/plain": ["<Figure size 720x360 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, axs = plt.subplots(ncols=2, figsize=(10,5))\n", "ax = axs[0]\n", "ax.plot(embedder._history['loss'])\n", "ax.set_ylabel('Cross Entropy')\n", "ax.set_xlabel('Epoch')\n", "\n", "ax = axs[1]\n", "ax.plot(embedder._history['reconstruction_loss'], label='train')\n", "ax.plot(embedder._history['val_reconstruction_loss'], label='valid')\n", "ax.legend()\n", "ax.set_ylabel('Cross Entropy')\n", "ax.set_xlabel('Epoch')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [conda env:umap_dev_new]", "language": "python", "name": "conda-env-umap_dev_new-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 2}